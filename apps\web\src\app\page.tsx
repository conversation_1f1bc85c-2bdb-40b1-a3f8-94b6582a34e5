'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useEffect, useState } from 'react';
import {
  MapPin,
  Clock,
  Users,
  ArrowLeft,
  Mail,
  Phone,
  User,
  UserPlus,
  LogOut,
  Calendar,
  AlertTriangle,
  CheckCircle,
} from 'lucide-react';
import type {
  Location,
  LocalizedString,
  OperatingHours,
  Room,
} from 'shared-types';
import { locationsService } from '../services/locations.service';
import { Button } from '../components/ui/button';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '../stores/auth-store';


import { LocationSelection } from '../components/booking/LocationSelection';
import { RoomSelection } from '../components/booking/RoomSelection';
import {
  CustomerInfo,
  CustomerInfoFormData,
} from '../components/booking/CustomerInfo';
import { TimeSlotCalendar } from '../components/booking/TimeSlotCalendar';
import { Hero } from '../components/ui/hero';

interface LocationWithRoomCount extends Location {
  roomCount: number;
}

interface BookingFormData {
  locationId: string;
  roomId: string;
  startTime: string;
  endTime: string;
  numberOfGuests: number;
  isGuestBooking: boolean;
  userId?: string;
  guestName?: string;
  guestEmail?: string;
  guestPhone?: string;
  notes?: string;
}



export default function HomePage() {
  const router = useRouter();
  const { user, clearAuth } = useAuthStore();
  const [locations, setLocations] = useState<LocationWithRoomCount[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLocation, setSelectedLocation] =
    useState<LocationWithRoomCount | null>(null);
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [bookingStep, setBookingStep] = useState<
    'locations' | 'rooms' | 'date-time' | 'customer-info'
  >('locations');
  const [particles, setParticles] = useState<
    Array<{ left: number; top: number }>
  >([]);
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  const [estimatedPrice, setEstimatedPrice] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);

  const [formData, setFormData] = useState<BookingFormData>({
    locationId: '',
    roomId: '',
    startTime: '',
    endTime: '',
    numberOfGuests: 1,
    isGuestBooking: true,
  });

  // Helper functions for date and time
  const getMinDate = () => {
    return new Date().toISOString().split('T')[0];
  };

  const getMaxDate = () => {
    const maxDate = new Date();
    maxDate.setMonth(maxDate.getMonth() + 3);
    return maxDate.toISOString().split('T')[0];
  };



  useEffect(() => {
    // Generate particle positions on client-side only
    setParticles(
      Array.from({ length: 20 }, () => ({
        left: Math.random() * 100,
        top: Math.random() * 100,
      }))
    );
  }, []);

  useEffect(() => {
    // If user is logged in and we're not in customer-info step,
    // check for redirect data
    if (user && bookingStep !== 'customer-info') {
      const bookingRedirect = sessionStorage.getItem('bookingRedirect');
      if (bookingRedirect) {
        try {
          const { locationId, step } = JSON.parse(bookingRedirect);
          // Find the location in our loaded locations
          const redirectLocation = locations.find(loc => loc.id === locationId);
          if (redirectLocation) {
            setSelectedLocation(redirectLocation);
            setBookingStep(step);
            setFormData(prev => ({
              ...prev,
              locationId: redirectLocation.id,
              isGuestBooking: false, // Set to user booking since we're logged in
            }));
            // Clear the redirect data
            sessionStorage.removeItem('bookingRedirect');
          }
        } catch (error) {
          console.error('Error processing booking redirect:', error);
          sessionStorage.removeItem('bookingRedirect');
        }
      }
    }
  }, [user, locations, bookingStep]); // Add dependencies

  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const response = await locationsService.getLocations({
          page: 1,
          limit: 100,
          isActive: true,
        });

        // Fetch room counts for each location
        const locationsWithRooms = await Promise.all(
          response.items.map(async location => {
            const rooms = await locationsService.getLocationRooms(location.id);
            return {
              ...location,
              roomCount: rooms.length,
            };
          })
        );

        setLocations(locationsWithRooms);
      } catch (error) {
        console.error('Error fetching locations:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLocations();
  }, []);

  const handleLocationSelect = (location: LocationWithRoomCount) => {
    setSelectedLocation(location);
    setBookingStep('rooms');
    setFormData(prev => ({
      ...prev,
      locationId: location.id,
    }));
  };

  const handleRoomSelect = (room: Room) => {
    setSelectedRoom(room);
    setBookingStep('date-time');
    setFormData(prev => ({
      ...prev,
      roomId: room.id,
    }));
  };

  const handleBackToLocations = () => {
    setBookingStep('locations');
    setSelectedLocation(null);
    setSelectedRoom(null);
    setFormData(prev => ({
      ...prev,
      locationId: '',
      roomId: '',
    }));
  };

  const handleBackToRooms = () => {
    setBookingStep('rooms');
    setSelectedRoom(null);
    setFormData(prev => ({
      ...prev,
      roomId: '',
    }));
  };

  const handleBackToDateTime = () => {
    setBookingStep('date-time');
    // Clear time selection but keep date
    setFormData(prev => ({
      ...prev,
      startTime: '',
      endTime: '',
    }));
  };

  const handleLoginRedirect = () => {
    // Store the current location in sessionStorage to redirect back after login
    if (selectedLocation) {
      const redirectData = {
        locationId: selectedLocation.id,
        step: 'date-time',
      };
      sessionStorage.setItem('bookingRedirect', JSON.stringify(redirectData));
    }
    router.push('/auth/login');
  };

  const handleTimeSlotSelection = (startTime: string, endTime: string, duration: number) => {
    setFormData(prev => ({
      ...prev,
      startTime,
      endTime,
    }));
    setDuration(duration);

    // Calculate estimated price
    const hourlyRate = selectedRoom?.pricePerHour || 50;
    setEstimatedPrice(Math.ceil(duration * hourlyRate));
  };

  const handleDateTimeSubmit = () => {
    if (!formData.startTime || !formData.endTime) {
      return;
    }
    setBookingStep('customer-info');
  };

  const handleCustomerInfoSubmit = (customerData: CustomerInfoFormData) => {
    setFormData(prev => ({
      ...prev,
      ...customerData,
    }));

    // TODO: Implement final booking submission
    console.log('Final form data:', { ...formData, ...customerData });
  };

  // Duration and price calculation is now handled in handleTimeSlotSelection

  return (
    <main className="min-h-screen relative overflow-hidden">
      {/* Enhanced Background Layer */}
      <div className="fixed inset-0 bg-gradient-to-br from-gold-50/90 via-white to-gold-100/90" />

      {/* Animated Background Pattern */}
      <motion.div
        className="absolute inset-0 bg-[url('/patterns/luxury.svg')] opacity-[0.2] pointer-events-none"
        animate={{
          scale: [1, 1.1, 1],
          opacity: [0.05, 0.1, 0.05],
        }}
        transition={{
          duration: 20,
          ease: 'linear',
          repeat: Infinity,
        }}
      />

      {/* Floating Particles */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        {particles.map((particle, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 rounded-full bg-gold-200/10"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
            }}
            animate={{
              y: [0, -30, 0],
              opacity: [0, 0.2, 0],
            }}
            transition={{
              duration: 3 + (i % 2) * 2,
              repeat: Infinity,
              delay: i * 0.1,
            }}
          />
        ))}
      </div>

      {/* Content Container */}
      <div className="container relative mx-auto px-6 lg:px-8 py-12">
        {/* Enhanced Hero Text with Animation */}
        <motion.div
          className="h-[200px] mb-4 relative"
          layout
          transition={{
            layout: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] },
          }}
        >
          <AnimatePresence mode="wait">
            <motion.div
              key="hero"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{
                duration: 0.3,
                ease: [0.25, 0.46, 0.45, 0.94],
              }}
              className="absolute inset-0"
            >
              <Hero />
            </motion.div>
          </AnimatePresence>
        </motion.div>

        <AnimatePresence mode="wait">
          {bookingStep === 'locations' && (
            <LocationSelection
              locations={locations}
              loading={loading}
              onLocationSelect={handleLocationSelect}
            />
          )}

          {bookingStep === 'rooms' && selectedLocation && (
            <RoomSelection
              selectedLocation={selectedLocation}
              onRoomSelect={handleRoomSelect}
              onBack={handleBackToLocations}
            />
          )}

          {bookingStep === 'date-time' && selectedLocation && selectedRoom && (
            <motion.div
              key="date-time"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="container mx-auto px-4 py-8"
            >
              <div className="max-w-4xl mx-auto">
                <button
                  onClick={handleBackToRooms}
                  className="flex items-center text-gray-600 hover:text-gray-900 mb-6 transition-colors"
                >
                  <ArrowLeft className="w-5 h-5 mr-2" />
                  Back to Room Selection
                </button>

                <div className="bg-white rounded-lg shadow-md p-6">
                  <h2 className="text-2xl font-semibold mb-6">
                    Select Date & Time
                  </h2>

                  <div className="space-y-6">
                    {/* Date Selection */}
                    <div>
                      <label
                        htmlFor="date"
                        className="block text-sm font-medium text-gray-700 mb-2"
                      >
                        Date
                      </label>
                      <input
                        type="date"
                        id="date"
                        value={selectedDate}
                        min={getMinDate()}
                        max={getMaxDate()}
                        onChange={e => {
                          setSelectedDate(e.target.value);
                          // Clear time selection when date changes
                          setFormData(prev => ({
                            ...prev,
                            startTime: '',
                            endTime: '',
                          }));
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-queens-gold"
                      />
                    </div>

                    {/* Time Slot Calendar */}
                    <TimeSlotCalendar
                      roomId={selectedRoom.id}
                      selectedDate={selectedDate}
                      locationOperatingHours={
                        selectedLocation.operatingHours
                          ? typeof selectedLocation.operatingHours === 'string'
                            ? JSON.parse(selectedLocation.operatingHours)
                            : selectedLocation.operatingHours
                          : undefined
                      }
                      onTimeSlotSelection={handleTimeSlotSelection}
                      selectedStartTime={formData.startTime}
                      selectedEndTime={formData.endTime}
                      roomPricePerHour={selectedRoom.pricePerHour}
                      roomCapacity={selectedRoom.capacity}
                      showAlternatives={true}
                      showStatistics={true}
                      enableRealTimeRefresh={true}
                      minDuration={0.5}
                      maxDuration={8}
                    />

                    {/* Duration and Price Display */}
                    {formData.startTime && formData.endTime && duration > 0 && (
                      <div className="mt-4 p-4 bg-queens-gold/10 rounded-lg border border-queens-gold/20">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-gray-700">
                            <Clock className="w-5 h-5 mr-2 text-queens-gold" />
                            <span className="font-medium">Duration: {duration} hours</span>
                          </div>
                          <div className="text-gray-900 font-semibold text-lg">
                            Estimated Price: ${estimatedPrice}
                          </div>
                        </div>
                      </div>
                    )}

                    <Button
                      onClick={handleDateTimeSubmit}
                      className="w-full mt-6 bg-queens-gold hover:bg-queens-gold/90"
                      disabled={!formData.startTime || !formData.endTime}
                    >
                      Continue to Customer Information
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {bookingStep === 'customer-info' &&
            selectedLocation &&
            selectedRoom && (
              <CustomerInfo
                selectedLocation={selectedLocation}
                selectedRoom={selectedRoom}
                user={user}
                onBack={handleBackToDateTime}
                onContinue={handleCustomerInfoSubmit}
                onLoginRedirect={handleLoginRedirect}
                onLogout={clearAuth}
              />
            )}


        </AnimatePresence>
      </div>
    </main>
  );
}
