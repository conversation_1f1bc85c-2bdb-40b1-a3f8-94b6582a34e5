'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useEffect, useState, useCallback } from 'react';
import {
  MapPin,
  Clock,
  Users,
  ArrowLeft,
  Mail,
  Phone,
  User,
  UserPlus,
  LogOut,
  Calendar,
  AlertTriangle,
  CheckCircle,
} from 'lucide-react';
import type {
  Location,
  LocalizedString,
  OperatingHours,
  Room,
} from 'shared-types';
import { locationsService } from '../services/locations.service';
import { Button } from '../components/ui/button';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '../stores/auth-store';
import { useAvailabilityCheck } from '../hooks/useAvailabilityCheck';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../components/ui/select';
import { LocationSelection } from '../components/booking/LocationSelection';
import { RoomSelection } from '../components/booking/RoomSelection';
import {
  CustomerInfo,
  CustomerInfoFormData,
} from '../components/booking/CustomerInfo';
import { Hero } from '../components/ui/hero';

interface LocationWithRoomCount extends Location {
  roomCount: number;
}

interface BookingFormData {
  locationId: string;
  roomId: string;
  startTime: string;
  endTime: string;
  numberOfGuests: number;
  isGuestBooking: boolean;
  userId?: string;
  guestName?: string;
  guestEmail?: string;
  guestPhone?: string;
  notes?: string;
}

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08,
      delayChildren: 0.3,
    },
  },
};

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

const shimmerAnimation = {
  initial: {
    backgroundPosition: '200% 0',
    opacity: 0.4,
  },
  animate: {
    backgroundPosition: ['-200% 0', '200% 0'],
    opacity: [0.4, 0.8, 0.4],
    transition: {
      duration: 4,
      ease: 'linear',
      repeat: Infinity,
      opacity: {
        duration: 2,
        ease: 'easeInOut',
        repeat: Infinity,
      },
    },
  },
};

// Card animations
const cardContentAnimation = {
  rest: {
    y: 0,
    opacity: 0.9,
    transition: {
      duration: 0.5,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
  hover: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.4,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

const cardTitleAnimation = {
  rest: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
  hover: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.4,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

const locationCardVariants = {
  initial: {
    opacity: 0,
    y: 20,
  },
  rest: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
  hover: {
    opacity: 1,
    y: 0,
    scale: 1.02,
    transition: {
      duration: 0.15,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

export default function HomePage() {
  const router = useRouter();
  const { user, clearAuth } = useAuthStore();
  const [locations, setLocations] = useState<LocationWithRoomCount[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLocation, setSelectedLocation] =
    useState<LocationWithRoomCount | null>(null);
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [bookingStep, setBookingStep] = useState<
    'locations' | 'rooms' | 'customer-info' | 'date-time'
  >('locations');
  const [particles, setParticles] = useState<
    Array<{ left: number; top: number }>
  >([]);
  const [availableTimeOptions, setAvailableTimeOptions] = useState<string[]>(
    []
  );
  const [estimatedPrice, setEstimatedPrice] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);
  const availability = useAvailabilityCheck();

  const [formData, setFormData] = useState<BookingFormData>({
    locationId: '',
    roomId: '',
    startTime: '',
    endTime: '',
    numberOfGuests: 1,
    isGuestBooking: true,
  });

  const [errors, setErrors] = useState<
    Partial<Record<keyof BookingFormData, string>>
  >({});

  // Helper functions for date and time
  const getMinDate = () => {
    return new Date().toISOString().split('T')[0];
  };

  const getMaxDate = () => {
    const maxDate = new Date();
    maxDate.setMonth(maxDate.getMonth() + 3);
    return maxDate.toISOString().split('T')[0];
  };

  const generateDefaultTimeOptions = () => {
    const options = [];
    for (let hour = 8; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        options.push(time);
      }
    }
    return options;
  };

  const generateTimeOptions = (
    location: Location,
    selectedDate?: string
  ): string[] => {
    if (!location?.operatingHours || !selectedDate) {
      return generateDefaultTimeOptions();
    }

    try {
      const operatingHours =
        typeof location.operatingHours === 'string'
          ? JSON.parse(location.operatingHours)
          : location.operatingHours;

      const date = new Date(selectedDate);
      const dayNames = [
        'sunday',
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday',
      ];
      const dayName = dayNames[date.getDay()];

      const hoursForDay = operatingHours[dayName];
      if (!hoursForDay || !hoursForDay.includes('-')) {
        return [];
      }

      const [openTimeStr, closeTimeStr] = hoursForDay.split('-');
      const [openHour, openMinute] = openTimeStr.split(':').map(Number);
      const [closeHour, closeMinute] = closeTimeStr.split(':').map(Number);

      const options = [];
      let currentHour = openHour;
      let currentMinute = openMinute;

      const crossesMidnight =
        closeHour < openHour ||
        (closeHour === openHour && closeMinute <= openMinute);
      const endHour = crossesMidnight ? closeHour + 24 : closeHour;
      const endMinute = closeMinute;

      while (true) {
        const displayHour = currentHour >= 24 ? currentHour - 24 : currentHour;
        const timeStr = `${displayHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
        options.push(timeStr);

        const currentTotalMinutes = currentHour * 60 + currentMinute;
        const endTotalMinutes = endHour * 60 + endMinute;

        if (currentTotalMinutes >= endTotalMinutes) {
          break;
        }

        currentMinute += 30;
        if (currentMinute >= 60) {
          currentMinute = 0;
          currentHour++;
        }

        if (options.length > 48) {
          break;
        }
      }
      return options;
    } catch (error) {
      console.error('Error generating time options:', error);
      return generateDefaultTimeOptions();
    }
  };

  const getValidEndTimeOptions = useCallback(
    (startTimeHM: string | null): string[] => {
      if (!startTimeHM || !availableTimeOptions.length) {
        return availableTimeOptions;
      }

      const startIndex = availableTimeOptions.indexOf(startTimeHM);
      if (startIndex === -1) {
        return availableTimeOptions;
      }

      return availableTimeOptions.slice(startIndex + 1);
    },
    [availableTimeOptions]
  );

  const createISOString = (dateStr: string, timeStr: string): string => {
    return `${dateStr}T${timeStr}:00`;
  };

  useEffect(() => {
    // Generate particle positions on client-side only
    setParticles(
      Array.from({ length: 20 }, () => ({
        left: Math.random() * 100,
        top: Math.random() * 100,
      }))
    );
  }, []);

  useEffect(() => {
    // If user is logged in and we're not in customer-info step,
    // check for redirect data
    if (user && bookingStep !== 'customer-info') {
      const bookingRedirect = sessionStorage.getItem('bookingRedirect');
      if (bookingRedirect) {
        try {
          const { locationId, step } = JSON.parse(bookingRedirect);
          // Find the location in our loaded locations
          const redirectLocation = locations.find(loc => loc.id === locationId);
          if (redirectLocation) {
            setSelectedLocation(redirectLocation);
            setBookingStep(step);
            setFormData(prev => ({
              ...prev,
              locationId: redirectLocation.id,
              isGuestBooking: false, // Set to user booking since we're logged in
            }));
            // Clear the redirect data
            sessionStorage.removeItem('bookingRedirect');
          }
        } catch (error) {
          console.error('Error processing booking redirect:', error);
          sessionStorage.removeItem('bookingRedirect');
        }
      }
    }
  }, [user, locations, bookingStep]); // Add dependencies

  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const response = await locationsService.getLocations({
          page: 1,
          limit: 100,
          isActive: true,
        });

        // Fetch room counts for each location
        const locationsWithRooms = await Promise.all(
          response.items.map(async location => {
            const rooms = await locationsService.getLocationRooms(location.id);
            return {
              ...location,
              roomCount: rooms.length,
            };
          })
        );

        setLocations(locationsWithRooms);
      } catch (error) {
        console.error('Error fetching locations:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLocations();
  }, []);

  const handleLocationSelect = (location: LocationWithRoomCount) => {
    setSelectedLocation(location);
    setBookingStep('rooms');
    setFormData(prev => ({
      ...prev,
      locationId: location.id,
    }));
  };

  const handleRoomSelect = (room: Room) => {
    setSelectedRoom(room);
    setBookingStep('customer-info');
    setFormData(prev => ({
      ...prev,
      roomId: room.id,
    }));
  };

  const handleBackToLocations = () => {
    setBookingStep('locations');
    setSelectedLocation(null);
    setSelectedRoom(null);
    setFormData(prev => ({
      ...prev,
      locationId: '',
      roomId: '',
    }));
  };

  const handleBackToRooms = () => {
    setBookingStep('rooms');
    setSelectedRoom(null);
    setFormData(prev => ({
      ...prev,
      roomId: '',
    }));
  };

  const handleLoginRedirect = () => {
    // Store the current location in sessionStorage to redirect back after login
    if (selectedLocation) {
      const redirectData = {
        locationId: selectedLocation.id,
        step: 'customer-info',
      };
      sessionStorage.setItem('bookingRedirect', JSON.stringify(redirectData));
    }
    router.push('/auth/login');
  };

  const handleCustomerInfoSubmit = (customerData: CustomerInfoFormData) => {
    setFormData(prev => ({
      ...prev,
      ...customerData,
    }));
    setBookingStep('date-time');

    // Generate time options based on selected location's operating hours
    if (selectedLocation) {
      const today = new Date().toISOString().split('T')[0];
      const timeOptions = generateTimeOptions(selectedLocation, today);
      setAvailableTimeOptions(timeOptions);
    }
  };

  useEffect(() => {
    if (formData.startTime && formData.endTime) {
      // Calculate duration
      const [startHour, startMinute] = formData.startTime
        .split(':')
        .map(Number);
      const [endHour, endMinute] = formData.endTime.split(':').map(Number);

      let durationHours = endHour - startHour;
      let durationMinutes = endMinute - startMinute;

      if (durationMinutes < 0) {
        durationHours--;
        durationMinutes += 60;
      }

      if (durationHours < 0) {
        durationHours += 24;
      }

      const totalDuration = durationHours + durationMinutes / 60;
      setDuration(totalDuration);

      // Calculate estimated price based on room's price per hour
      const hourlyRate = selectedRoom?.pricePerHour || 50; // Fallback to 50 if not set
      setEstimatedPrice(Math.ceil(totalDuration * hourlyRate));
    }
  }, [formData.startTime, formData.endTime, selectedRoom]);

  return (
    <main className="min-h-screen relative overflow-hidden">
      {/* Enhanced Background Layer */}
      <div className="fixed inset-0 bg-gradient-to-br from-gold-50/90 via-white to-gold-100/90" />

      {/* Animated Background Pattern */}
      <motion.div
        className="absolute inset-0 bg-[url('/patterns/luxury.svg')] opacity-[0.2] pointer-events-none"
        animate={{
          scale: [1, 1.1, 1],
          opacity: [0.05, 0.1, 0.05],
        }}
        transition={{
          duration: 20,
          ease: 'linear',
          repeat: Infinity,
        }}
      />

      {/* Floating Particles */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        {particles.map((particle, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 rounded-full bg-gold-200/10"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
            }}
            animate={{
              y: [0, -30, 0],
              opacity: [0, 0.2, 0],
            }}
            transition={{
              duration: 3 + (i % 2) * 2,
              repeat: Infinity,
              delay: i * 0.1,
            }}
          />
        ))}
      </div>

      {/* Content Container */}
      <div className="container relative mx-auto px-6 lg:px-8 py-12">
        {/* Enhanced Hero Text with Animation */}
        <motion.div
          className="h-[200px] mb-4 relative"
          layout
          transition={{
            layout: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] },
          }}
        >
          <AnimatePresence mode="wait">
            <motion.div
              key="hero"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{
                duration: 0.3,
                ease: [0.25, 0.46, 0.45, 0.94],
              }}
              className="absolute inset-0"
            >
              <Hero />
            </motion.div>
          </AnimatePresence>
        </motion.div>

        <AnimatePresence mode="wait">
          {bookingStep === 'locations' && (
            <LocationSelection
              locations={locations}
              loading={loading}
              onLocationSelect={handleLocationSelect}
            />
          )}

          {bookingStep === 'rooms' && selectedLocation && (
            <RoomSelection
              selectedLocation={selectedLocation}
              onRoomSelect={handleRoomSelect}
              onBack={handleBackToLocations}
            />
          )}

          {bookingStep === 'customer-info' &&
            selectedLocation &&
            selectedRoom && (
              <CustomerInfo
                selectedLocation={selectedLocation}
                selectedRoom={selectedRoom}
                user={user}
                onBack={handleBackToRooms}
                onContinue={handleCustomerInfoSubmit}
                onLoginRedirect={handleLoginRedirect}
                onLogout={clearAuth}
              />
            )}

          {bookingStep === 'date-time' && selectedLocation && selectedRoom && (
            <motion.div
              key="date-time"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="container mx-auto px-4 py-8"
            >
              <div className="max-w-2xl mx-auto">
                <button
                  onClick={() => setBookingStep('customer-info')}
                  className="flex items-center text-gray-600 hover:text-gray-900 mb-6 transition-colors"
                >
                  <ArrowLeft className="w-5 h-5 mr-2" />
                  Back to Customer Information
                </button>

                <div className="bg-white rounded-lg shadow-md p-6">
                  <h2 className="text-2xl font-semibold mb-6">
                    Select Date & Time
                  </h2>

                  <div className="space-y-6">
                    {/* Date Selection */}
                    <div>
                      <label
                        htmlFor="date"
                        className="block text-sm font-medium text-gray-700 mb-2"
                      >
                        Date
                      </label>
                      <input
                        type="date"
                        id="date"
                        min={getMinDate()}
                        max={getMaxDate()}
                        onChange={e => {
                          const selectedDate = e.target.value;
                          if (selectedLocation) {
                            const timeOptions = generateTimeOptions(
                              selectedLocation,
                              selectedDate
                            );
                            setAvailableTimeOptions(timeOptions);
                          }
                          setFormData(prev => ({
                            ...prev,
                            startTime: '',
                            endTime: '',
                          }));
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    {/* Time Selection */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label
                          htmlFor="startTime"
                          className="block text-sm font-medium text-gray-700 mb-2"
                        >
                          Start Time
                        </label>
                        <Select
                          value={formData.startTime}
                          onValueChange={value => {
                            setFormData(prev => ({
                              ...prev,
                              startTime: value,
                              endTime: '',
                            }));
                            setErrors(prev => ({
                              ...prev,
                              startTime: undefined,
                            }));
                          }}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select start time" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableTimeOptions.map(time => (
                              <SelectItem key={time} value={time}>
                                {time}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.startTime && (
                          <p className="text-red-500 text-sm mt-1">
                            {errors.startTime}
                          </p>
                        )}
                      </div>

                      <div>
                        <label
                          htmlFor="endTime"
                          className="block text-sm font-medium text-gray-700 mb-2"
                        >
                          End Time
                        </label>
                        <Select
                          value={formData.endTime}
                          onValueChange={value => {
                            setFormData(prev => ({
                              ...prev,
                              endTime: value,
                            }));
                            setErrors(prev => ({
                              ...prev,
                              endTime: undefined,
                            }));
                          }}
                        >
                          <SelectTrigger
                            className={`w-full ${!formData.startTime ? 'opacity-50 cursor-not-allowed' : ''}`}
                          >
                            <SelectValue placeholder="Select end time" />
                          </SelectTrigger>
                          <SelectContent>
                            {getValidEndTimeOptions(formData.startTime).map(
                              time => (
                                <SelectItem key={time} value={time}>
                                  {time}
                                </SelectItem>
                              )
                            )}
                          </SelectContent>
                        </Select>
                        {errors.endTime && (
                          <p className="text-red-500 text-sm mt-1">
                            {errors.endTime}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Duration and Price Estimate */}
                    {formData.startTime && formData.endTime && (
                      <div className="mt-4 p-4 bg-gray-50 rounded-md">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-gray-600">
                            <Clock className="w-5 h-5 mr-2" />
                            <span>Duration: {duration} hours</span>
                          </div>
                          <div className="text-gray-900 font-semibold">
                            Estimated Price: ${estimatedPrice}
                          </div>
                        </div>
                      </div>
                    )}

                    <Button
                      onClick={() => {
                        if (!formData.startTime || !formData.endTime) {
                          setErrors({
                            ...errors,
                            startTime: !formData.startTime
                              ? 'Please select a start time'
                              : undefined,
                            endTime: !formData.endTime
                              ? 'Please select an end time'
                              : undefined,
                          });
                          return;
                        }
                        // TODO: Implement final step submission
                        console.log('Final form data:', formData);
                      }}
                      className="w-full mt-6"
                      disabled={!formData.startTime || !formData.endTime}
                    >
                      Continue to Review
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </main>
  );
}
