import {
  User<PERSON><PERSON>,
  Booking<PERSON>tatus,
  PaymentStatus,
  LocalizedString,
  PaginationParams,
  BaseQueryParams,
  DateRange,
  RoomTypeEnum,
  DecorStyle,
} from './common.types';

// Authentication DTOs
export interface LoginDto {
  email: string;
  password: string;
}

export interface RegisterDto {
  email: string;
  password: string;
  name?: string;
  phoneNumber?: string;
}

export interface AuthResponseDto {
  user: {
    id: string;
    email: string;
    name?: string;
    role: UserRole;
    emailVerified?: boolean;
  };
  accessToken: string;
  refreshToken: string;
}

export interface ChangePasswordDto {
  currentPassword: string;
  newPassword: string;
}

export interface ResetPasswordDto {
  email: string;
}

export interface ConfirmResetPasswordDto {
  token: string;
  newPassword: string;
}

export interface AdminChangePasswordDto {
  newPassword?: string; // If not provided, generates a random password
  generateRandom?: boolean; // Explicit flag to generate random password
}

// User DTOs
export interface UpdateUserDto {
  name?: string;
  phoneNumber?: string;
}

export interface CreateUserDto {
  email: string;
  password: string;
  name?: string;
  phoneNumber?: string;
  role?: UserRole;
}

export interface UserQueryDto extends BaseQueryParams {
  role?: UserRole;
  isActive?: boolean;
  emailVerified?: boolean;
}

// Advanced User Management DTOs
export interface AdminCreateUserDto {
  email: string;
  name?: string;
  phoneNumber?: string;
  role: UserRole;
  isActive?: boolean;
  sendWelcomeEmail?: boolean;
}

export interface AdminUpdateUserDto {
  name?: string;
  phoneNumber?: string;
  emailVerified?: boolean;
  isActive?: boolean;
}

export interface UpdateUserRoleDto {
  role: UserRole;
}

export interface UpdateUserStatusDto {
  isActive: boolean;
  reason?: string;
}

export interface UserResponseDto {
  id: string;
  email: string;
  name?: string;
  phoneNumber?: string;
  role: UserRole;
  isActive: boolean;
  emailVerified: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
  bookingCount?: number;
  totalSpent?: number;
}

export interface UserDetailResponseDto extends UserResponseDto {
  recentBookings?: Array<{
    id: string;
    bookingReference: string;
    locationName: LocalizedString;
    roomName: LocalizedString;
    startTime: string;
    endTime: string;
    status: BookingStatus;
    totalPrice: number;
  }>;
  auditLogs?: Array<{
    id: string;
    action: string;
    createdAt: string;
    performedByUser?: {
      id: string;
      name?: string;
      email: string;
    };
  }>;
}

export interface UserListResponseDto {
  users: UserResponseDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface UserStatsDto {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  verifiedUsers: number;
  unverifiedUsers: number;
  usersByRole: Array<{
    role: UserRole;
    count: number;
  }>;
  recentRegistrations: Array<{
    date: string;
    count: number;
  }>;
}

export interface BulkUserActionDto {
  userIds: string[];
  action: 'activate' | 'deactivate' | 'verify' | 'unverify';
  reason?: string;
}

export interface BulkUserActionResponseDto {
  successful: string[];
  failed: Array<{
    userId: string;
    error: string;
  }>;
  total: number;
  successCount: number;
  failureCount: number;
}

// Location DTOs
export interface CreateLocationDto {
  name: LocalizedString;
  address: string;
  description?: LocalizedString;
  imageUrl?: string;
  phoneNumber?: string;
  email?: string;
  operatingHours?: Record<string, string>;
}

export interface UpdateLocationDto {
  name?: LocalizedString;
  address?: string;
  description?: LocalizedString;
  imageUrl?: string;
  phoneNumber?: string;
  email?: string;
  operatingHours?: Record<string, string>;
  isActive?: boolean;
}

export interface LocationQueryDto extends BaseQueryParams {
  isActive?: boolean;
}

// Room DTOs
export interface CreateRoomDto {
  name: LocalizedString;
  theme?: LocalizedString;
  description?: LocalizedString;
  capacity: number;
  pricePerHour: number;
  images?: string[];
  amenities?: LocalizedString[];
  roomType?: RoomTypeEnum;
  decorStyle?: DecorStyle;
  locationId: string;
}

export interface UpdateRoomDto {
  name?: LocalizedString;
  theme?: LocalizedString;
  description?: LocalizedString;
  capacity?: number;
  pricePerHour?: number;
  images?: string[];
  amenities?: LocalizedString[];
  roomType?: RoomTypeEnum;
  decorStyle?: DecorStyle;
  isActive?: boolean;
}

export interface RoomQueryDto extends BaseQueryParams {
  locationId?: string;
  isActive?: boolean;
  minCapacity?: number;
  maxCapacity?: number;
  minPrice?: number;
  maxPrice?: number;
  roomType?: RoomTypeEnum;
  decorStyle?: DecorStyle;
}

export interface RoomAvailabilityQueryDto {
  locationId?: string;
  roomId?: string;
  date: string; // ISO date string
  duration?: number; // in minutes, default to 60
}

export interface AvailableTimeSlot {
  startTime: string; // ISO string
  endTime: string; // ISO string
  available: boolean;
  price: number;
}

// Room Availability Check DTOs
export interface CheckRoomAvailabilityQueryDto {
  startTime: string; // ISO string for the desired start of the booking
  endTime: string; // ISO string for the desired end of the booking
  excludeBookingId?: string; // Optional booking ID to exclude from conflict checks (for updates)
  // roomId is typically part of the path parameters, e.g., /rooms/:roomId/availability
}

export interface TimeSlotDto {
  startTime: string; // ISO string
  endTime: string; // ISO string
  isAvailable: boolean;
  reason?:
    | 'booked'
    | 'override_unavailable'
    | 'outside_operating_hours'
    | 'unavailable'; // More specific reasons
  price?: number; // Optional: price for this specific slot if dynamic
}

export interface RoomAvailabilityResponseDto {
  roomId: string;
  requestedStartTime: string; // ISO string
  requestedEndTime: string; // ISO string
  isGenerallyAvailable: boolean; // Is the overall requested slot available?
  reason?: string; // If not generally available, a general reason
  // Optional: Can also return a list of slots if we want to show a timeline view
  // timeSlots?: TimeSlotDto[];
}

// Alternative Time Slots DTOs for Smart Suggestions
export interface AlternativeTimeSlotDto {
  startTime: string; // ISO string
  endTime: string; // ISO string
  durationMinutes: number;
  price: number;
  reason?: string; // Why this slot is suggested (e.g., "closest_time", "same_duration")
}

export interface FindAlternativeTimeSlotsQueryDto {
  startTime: string; // ISO string for requested start time
  endTime: string; // ISO string for requested end time
  searchRangeHours?: number; // How many hours before/after to search (default: 4)
  maxSuggestions?: number; // Maximum number of suggestions to return (default: 5)
  preferredDuration?: boolean; // Whether to prioritize slots with same duration (default: true)
}

export interface AlternativeTimeSlotsResponseDto {
  roomId: string;
  requestedStartTime: string;
  requestedEndTime: string;
  requestedDurationMinutes: number;
  alternatives: AlternativeTimeSlotDto[];
  message?: string; // User-friendly message about the suggestions
}

// Booking DTOs
export interface CreateBookingDto {
  roomId: string;
  startTime: string; // ISO string
  endTime: string; // ISO string
  numberOfGuests: number;
  notes?: string;

  // For guest bookings (when user is not authenticated)
  guestName?: string;
  guestEmail?: string;
  guestPhone?: string;
}

export interface UpdateBookingDto {
  startTime?: string;
  endTime?: string;
  numberOfGuests?: number;
  notes?: string;
  adminNotes?: string;
  status?: BookingStatus;
}

export interface AdminUpdateBookingDto {
  startTime?: string;
  endTime?: string;
  numberOfGuests?: number;
  notes?: string;
  adminNotes?: string;
  status?: BookingStatus;
}

export interface BookingQueryDto extends BaseQueryParams {
  userId?: string;
  locationId?: string;
  roomId?: string;
  status?: BookingStatus;
  dateRange?: DateRange;
  guestEmail?: string;
  search?: string; // General search field for booking reference, guest name, email, etc.
  bookingReference?: string; // Specific search by booking reference
}

export interface CancelBookingDto {
  reason?: string;
}

// Admin booking management DTOs
export interface AdminUpdateBookingStatusDto {
  status: BookingStatus;
  adminNotes?: string;
}

// Payment DTOs
export interface CreatePaymentDto {
  bookingId: string;
  returnUrl: string;
  cancelUrl: string;
}

export interface PaymentCallbackDto {
  transactionId: string;
  status: PaymentStatus;
  amount: number;
  metadata?: Record<string, any>;
}

export interface PaymentQueryDto extends BaseQueryParams {
  bookingId?: string;
  status?: PaymentStatus;
  dateRange?: DateRange;
}

// Availability Override DTOs
export interface CreateAvailabilityOverrideDto {
  roomId: string;
  date: string; // ISO date string
  startTime: string; // ISO string
  endTime: string; // ISO string
  isAvailable: boolean;
  reason?: string;
}

export interface UpdateAvailabilityOverrideDto {
  startTime?: string;
  endTime?: string;
  isAvailable?: boolean;
  reason?: string;
}

export interface AvailabilityOverrideQueryDto extends BaseQueryParams {
  roomId?: string;
  dateRange?: DateRange;
  isAvailable?: boolean;
}

// Dashboard/Analytics DTOs
export interface DashboardStatsDto {
  totalBookings: number;
  totalRevenue: number;
  activeUsers: number;
  occupancyRate: number;
  recentBookings: Array<{
    id: string;
    bookingReference: string;
    roomName: LocalizedString;
    startTime: string;
    status: BookingStatus;
    totalPrice: number;
  }>;
}

export interface RevenueAnalyticsDto {
  period: 'daily' | 'weekly' | 'monthly';
  data: Array<{
    date: string;
    revenue: number;
    bookingCount: number;
  }>;
}

// Enhanced Analytics DTOs
export interface DashboardAnalyticsDto {
  totalLocations: number;
  totalRooms: number;
  totalBookings: number;
  totalRevenue: number;
  todayBookings: number;
  occupancyRate: number;
  activeUsers: number;
  thisMonthRevenue: number;
  thisMonthBookings: number;
  previousMonthRevenue: number;
  previousMonthBookings: number;
  averageBookingDuration: number;
  roomTypeDistribution: RoomTypeStatsDto[];
}

export interface RoomTypeStatsDto {
  roomType: RoomTypeEnum;
  count: number;
  bookings: number;
  revenue: number;
  occupancyRate: number;
}

export interface BookingTrendDto {
  date: string;
  bookings: number;
  revenue: number;
  occupancyRate: number;
}

export interface PopularRoomDto {
  roomId: string;
  roomName: string;
  locationName: string;
  bookingCount: number;
  revenue: number;
  occupancyRate: number;
}

export interface PeakHoursDto {
  hour: number;
  bookingCount: number;
  revenue: number;
}

export interface AnalyticsQueryDto {
  days?: number;
  limit?: number;
  period?: 'daily' | 'weekly' | 'monthly';
  roomType?: RoomTypeEnum;
  locationId?: string;
}

// File Upload DTOs
export interface UploadImageDto {
  file: File | Buffer;
  fileName: string;
  mimeType: string;
}

export interface ImageResponseDto {
  url: string;
  publicId: string;
}
