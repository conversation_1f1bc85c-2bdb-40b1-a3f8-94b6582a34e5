import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CloudinaryService } from '../cloudinary/cloudinary.service';
import {
  CreateLocationDto,
  UpdateLocationDto,
  LocationQueryDto,
} from 'shared-types';

@Injectable()
export class LocationsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly cloudinaryService: CloudinaryService,
  ) {}

  async create(createLocationDto: CreateLocationDto) {
    const location = await this.prisma.location.create({
      data: {
        name: createLocationDto.name as any,
        address: createLocationDto.address,
        description: createLocationDto.description as any,
        imageUrl: createLocationDto.imageUrl,
        phoneNumber: createLocationDto.phoneNumber,
        email: createLocationDto.email,
        operatingHours: createLocationDto.operatingHours as any,
      },
    });

    return location;
  }

  async findAll(query?: LocationQueryDto) {
    const where: any = {
      isActive: true, // Only show active locations by default
    };

    if (query?.isActive !== undefined) {
      // Convert string to boolean if needed (from URL query parameters)
      const isActiveValue =
        typeof query.isActive === 'string'
          ? query.isActive === 'true'
          : query.isActive;
      where.isActive = isActiveValue;
    }

    // Remove search functionality as it's causing Prisma JSON path errors
    // and will not be needed in the frontend

    // Convert string query parameters to numbers
    const page = query?.page ? parseInt(query.page.toString(), 10) : 1;
    const limit = query?.limit ? parseInt(query.limit.toString(), 10) : 20;

    // Ensure page and limit are valid numbers
    const safePage = page > 0 ? page : 1;
    const safeLimit = limit > 0 && limit <= 100 ? limit : 20; // Cap at 100 for performance

    const skip = (safePage - 1) * safeLimit;
    const take = safeLimit;

    const locations = await this.prisma.location.findMany({
      where,
      orderBy: query?.sortBy
        ? { [query.sortBy]: query.sortOrder || 'asc' }
        : { createdAt: 'desc' },
      skip,
      take,
    });

    const total = await this.prisma.location.count({ where });

    return {
      items: locations,
      total,
      page: safePage,
      limit: safeLimit,
      totalPages: Math.ceil(total / safeLimit),
    };
  }

  async findOne(id: string) {
    const location = await this.prisma.location.findUnique({
      where: { id },
      include: {
        rooms: {
          where: { isActive: true },
          select: {
            id: true,
            name: true,
            capacity: true,
            pricePerHour: true,
            isActive: true,
          },
        },
      },
    });

    if (!location) {
      throw new NotFoundException(`Location with ID ${id} not found`);
    }

    return location;
  }

  async update(id: string, updateLocationDto: UpdateLocationDto) {
    const existingLocation = await this.prisma.location.findUnique({
      where: { id },
    });

    if (!existingLocation) {
      throw new NotFoundException(`Location with ID ${id} not found`);
    }

    const updateData: any = {};

    if (updateLocationDto.name !== undefined) {
      updateData.name = updateLocationDto.name;
    }
    if (updateLocationDto.address !== undefined) {
      updateData.address = updateLocationDto.address;
    }
    if (updateLocationDto.description !== undefined) {
      updateData.description = updateLocationDto.description;
    }
    if (updateLocationDto.imageUrl !== undefined) {
      updateData.imageUrl = updateLocationDto.imageUrl;
    }
    if (updateLocationDto.phoneNumber !== undefined) {
      updateData.phoneNumber = updateLocationDto.phoneNumber;
    }
    if (updateLocationDto.email !== undefined) {
      updateData.email = updateLocationDto.email;
    }
    if (updateLocationDto.operatingHours !== undefined) {
      updateData.operatingHours = updateLocationDto.operatingHours;
    }
    if (updateLocationDto.isActive !== undefined) {
      updateData.isActive = updateLocationDto.isActive;
    }

    const location = await this.prisma.location.update({
      where: { id },
      data: updateData,
    });

    return location;
  }

  async remove(id: string) {
    const existingLocation = await this.prisma.location.findUnique({
      where: { id },
    });

    if (!existingLocation) {
      throw new NotFoundException(`Location with ID ${id} not found`);
    }

    // Soft delete by setting isActive to false
    const location = await this.prisma.location.update({
      where: { id },
      data: { isActive: false },
    });

    return location;
  }

  async getLocationRooms(locationId: string) {
    const location = await this.prisma.location.findUnique({
      where: { id: locationId },
    });

    if (!location) {
      throw new NotFoundException(`Location with ID ${locationId} not found`);
    }

    const rooms = await this.prisma.room.findMany({
      where: {
        locationId,
        isActive: true,
      },
      orderBy: { name: 'asc' },
    });

    return rooms;
  }

  async getLocationOperatingHours(locationId: string) {
    const location = await this.prisma.location.findUnique({
      where: { id: locationId },
      select: {
        id: true,
        name: true,
        operatingHours: true,
      },
    });

    if (!location) {
      throw new NotFoundException(`Location with ID ${locationId} not found`);
    }

    return location;
  }

  async uploadLocationImage(locationId: string, file: Express.Multer.File) {
    const location = await this.prisma.location.findUnique({
      where: { id: locationId },
    });

    if (!location) {
      throw new NotFoundException(
        `Location with ID "${locationId}" not found.`,
      );
    }

    if (!file) {
      throw new BadRequestException('No image file provided for upload.');
    }

    try {
      // Delete existing image if it exists
      if (location.imageUrl) {
        const match = location.imageUrl.match(
          /upload\/(?:v\d+\/)?(?:[^/]+\/)*([^.]+)/,
        );
        if (match && match[0]) {
          const publicIdWithFolder = match[0]
            .substring(match[0].indexOf('upload/') + 'upload/'.length)
            .split('.')[0];
          try {
            await this.cloudinaryService.deleteImage(publicIdWithFolder);
          } catch (deleteError) {
            console.error(
              'Failed to delete old image from Cloudinary:',
              deleteError,
            );
          }
        }
      }

      // Upload new image
      const result = await this.cloudinaryService.uploadImage(
        file,
        `locations/${locationId}`,
      );

      if (!result || !result.secure_url) {
        throw new InternalServerErrorException(
          'Failed to upload image to Cloudinary.',
        );
      }

      // Update location with new image URL
      return this.prisma.location.update({
        where: { id: locationId },
        data: { imageUrl: result.secure_url },
      });
    } catch (error) {
      const message =
        error instanceof Error ? error.message : 'Could not upload image.';
      throw new InternalServerErrorException(
        'Failed to upload image.',
        message,
      );
    }
  }
}
