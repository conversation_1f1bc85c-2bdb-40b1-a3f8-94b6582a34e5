{"extends": "../../tsconfig.base.json", "compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "moduleResolution": "node", "esModuleInterop": true, "allowJs": true, "noEmitOnError": false, "paths": {"@api/*": ["src/*"], "@shared-types/*": ["../../packages/shared-types/src/*"], "shared-types": ["../../packages/shared-types/src/index"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}