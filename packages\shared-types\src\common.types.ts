// Localized content type for internationalization
export interface LocalizedString {
  vi: string; // Vietnamese (primary)
  en: string; // English (secondary)
}

// Database Enums
export enum UserRole {
  CUSTOMER = 'CUSTOMER',
  ADMIN = 'ADMIN',
  SUPER_ADMIN = 'SUPER_ADMIN',
}

export enum BookingStatus {
  PENDING_PAYMENT = 'PENDING_PAYMENT',
  CONFIRMED = 'CONFIRMED',
  CANCELLED_BY_USER = 'CANCELLED_BY_USER',
  CANCELLED_BY_ADMIN = 'CANCELLED_BY_ADMIN',
  COMPLETED = 'COMPLETED',
  NO_SHOW = 'NO_SHOW',
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  SUCCESSFUL = 'SUCCESSFUL',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
}

export enum RoomTypeEnum {
  STANDARD = 'STANDARD',
  RUBY = 'RUBY',
  SAPPHIRE = 'SAPPHIRE',
  QUEENS_EYES = 'QUEENS_EYES',
  OPAL = 'OPAL',
  PEARL = 'PEARL',
  HALL = 'HALL',
}

export enum DecorStyle {
  MODERN = 'MODERN',
  CLASSIC = 'CLASSIC',
}

// Sort Order Enum for query parameters
export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Common utility types
export interface TimeSlot {
  startTime: string; // ISO string
  endTime: string; // ISO string
}

export interface OperatingHours {
  monday?: string;
  tuesday?: string;
  wednesday?: string;
  thursday?: string;
  friday?: string;
  saturday?: string;
  sunday?: string;
}

// Query/Filter types
export interface DateRange {
  startDate: string; // ISO date string
  endDate: string; // ISO date string
}

export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface SortParams {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface BaseQueryParams extends PaginationParams, SortParams {
  search?: string;
}
