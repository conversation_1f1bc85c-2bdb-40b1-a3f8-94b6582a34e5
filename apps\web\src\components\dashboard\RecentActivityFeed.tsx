import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '../ui/card';
import {
  Calendar,
  UserPlus,
  UserCheck,
  XCircle,
  Clock,
  DollarSign,
  Building2,
  Sparkles,
} from 'lucide-react';

interface ActivityItem {
  id: string;
  type:
    | 'booking'
    | 'user_registration'
    | 'booking_cancellation'
    | 'system_update';
  title: string;
  description: string;
  timestamp: string;
  metadata?: {
    amount?: number;
    roomType?: string;
    userRole?: string;
  };
}

interface RecentActivityFeedProps {
  activities: ActivityItem[];
  loading?: boolean;
}

const ActivityIcon: React.FC<{ type: ActivityItem['type'] }> = ({ type }) => {
  switch (type) {
    case 'booking':
      return <Calendar className="w-4 h-4 text-green-600" />;
    case 'user_registration':
      return <UserPlus className="w-4 h-4 text-blue-600" />;
    case 'booking_cancellation':
      return <XCircle className="w-4 h-4 text-red-600" />;
    case 'system_update':
      return <Sparkles className="w-4 h-4 text-purple-600" />;
    default:
      return <Clock className="w-4 h-4 text-gray-600" />;
  }
};

const formatRelativeTime = (timestamp: string): string => {
  const now = new Date();
  const time = new Date(timestamp);
  const diffMs = now.getTime() - time.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMins < 1) return 'Vừa xong';
  if (diffMins < 60) return `${diffMins} phút trước`;
  if (diffHours < 24) return `${diffHours} giờ trước`;
  if (diffDays < 7) return `${diffDays} ngày trước`;

  return time.toLocaleDateString('vi-VN');
};

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    notation: 'compact',
    maximumFractionDigits: 0,
  }).format(amount);
};

// Loading skeleton for individual activity items
const ActivityItemSkeleton: React.FC<{ index: number }> = ({ index }) => (
  <div
    className="flex items-start space-x-3 animate-pulse opacity-0 animate-in fade-in slide-in-from-left duration-300"
    style={{
      animationDelay: `${index * 100}ms`,
      animationFillMode: 'forwards',
    }}
  >
    <div className="w-8 h-8 bg-gray-200 rounded-full flex-shrink-0"></div>
    <div className="flex-1 space-y-1">
      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
    </div>
    <div className="h-3 bg-gray-200 rounded w-16 flex-shrink-0"></div>
  </div>
);

const RecentActivityFeed: React.FC<RecentActivityFeedProps> = ({
  activities,
  loading = false,
}) => {
  if (loading) {
    return (
      <Card className="transition-all duration-300">
        <CardHeader>
          <CardTitle className="text-lg font-semibold animate-pulse">
            <div className="h-6 w-40 bg-gray-200 rounded"></div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <ActivityItemSkeleton key={i} index={i} />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="transition-all duration-300 animate-in fade-in slide-in-from-left duration-500">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-gray-800 transition-all duration-200">
          Hoạt động gần đây
        </CardTitle>
      </CardHeader>
      <CardContent>
        {activities.length === 0 ? (
          <div className="text-center py-8 text-gray-500 animate-in fade-in duration-300">
            <Clock className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <p>Chưa có hoạt động nào gần đây</p>
          </div>
        ) : (
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {activities.map((activity, index) => (
              <div
                key={activity.id}
                className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-all duration-200 transform hover:translate-x-1 opacity-0 animate-in fade-in slide-in-from-left"
                style={{
                  animationDelay: `${index * 50}ms`,
                  animationDuration: '400ms',
                  animationFillMode: 'forwards',
                }}
              >
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center transition-all duration-200 transform hover:scale-110">
                  <ActivityIcon type={activity.type} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate transition-all duration-200">
                    {activity.title}
                  </p>
                  <p className="text-xs text-gray-600 mt-1 transition-all duration-200">
                    {activity.description}
                  </p>
                  {activity.metadata?.amount && (
                    <p className="text-xs font-medium text-green-600 mt-1 transition-all duration-200">
                      {formatCurrency(activity.metadata.amount)}
                    </p>
                  )}
                </div>
                <div className="flex-shrink-0">
                  <span className="text-xs text-gray-500 transition-all duration-200">
                    {formatRelativeTime(activity.timestamp)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RecentActivityFeed;
