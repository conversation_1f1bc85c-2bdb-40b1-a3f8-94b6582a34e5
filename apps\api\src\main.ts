import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  app.enableCors(); // Enable CORS for frontend requests
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // Strip away any properties that don't have any decorators
      transform: true, // Transform payloads to DTO instances
      forbidNonWhitelisted: true, // Throw an error if non-whitelisted values are provided
      transformOptions: {
        enableImplicitConversion: true, // Allow conversion of primitive types
      },
    }),
  );

  const port = process.env.PORT || 3001;
  await app.listen(port);
  console.log(`Application is running on: ${await app.getUrl()}`);
}

bootstrap().catch((error) => {
  console.error('Error starting application:', error);
  process.exit(1);
});
