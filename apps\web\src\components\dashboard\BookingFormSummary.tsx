'use client';

import {
  MapPin,
  Clock,
  Users,
  Mail,
  Phone,
  User,
  DollarSign,
  FileText,
} from 'lucide-react';
import { Location, Room, LocalizedString, UserResponseDto } from 'shared-types';

interface BookingFormData {
  locationId: string;
  roomId: string;
  startTime: string;
  endTime: string;
  numberOfGuests: number;
  isGuestBooking: boolean;
  userId?: string;
  guestName?: string;
  guestEmail?: string;
  guestPhone?: string;
  notes?: string;
}

interface BookingFormSummaryProps {
  formData: BookingFormData;
  selectedLocation: Location | null;
  selectedRoom: Room | null;
  selectedUser: UserResponseDto | null;
  onUpdate: (updates: Partial<BookingFormData>) => void;
}

// Helper function to get localized text
const getLocalizedText = (
  localizedString: LocalizedString | undefined | null
): string => {
  if (!localizedString) return '';
  return localizedString.vi || localizedString.en || '';
};

const BookingFormSummary: React.FC<BookingFormSummaryProps> = ({
  formData,
  selectedLocation,
  selectedRoom,
  selectedUser,
  onUpdate,
}) => {
  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  };

  const formatDateTime = (dateTimeString: string): string => {
    if (!dateTimeString) return '';

    const date = new Date(dateTimeString);
    return new Intl.DateTimeFormat('vi-VN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const formatTime = (dateTimeString: string): string => {
    if (!dateTimeString) return '';

    const date = new Date(dateTimeString);
    return new Intl.DateTimeFormat('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const formatDate = (dateTimeString: string): string => {
    if (!dateTimeString) return '';

    const date = new Date(dateTimeString);
    return new Intl.DateTimeFormat('vi-VN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  const calculateDuration = (): {
    hours: number;
    minutes: number;
    totalPrice: number;
  } => {
    if (!formData.startTime || !formData.endTime || !selectedRoom) {
      return { hours: 0, minutes: 0, totalPrice: 0 };
    }

    const start = new Date(formData.startTime);
    const end = new Date(formData.endTime);
    const durationMs = end.getTime() - start.getTime();
    const totalMinutes = Math.floor(durationMs / (1000 * 60));
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    const durationHours = durationMs / (1000 * 60 * 60);
    const totalPrice = durationHours * selectedRoom.pricePerHour;

    return { hours, minutes, totalPrice };
  };

  const { hours, minutes, totalPrice } = calculateDuration();

  const formatDuration = (): string => {
    if (hours > 0 && minutes > 0) {
      return `${hours} giờ ${minutes} phút`;
    } else if (hours > 0) {
      return `${hours} giờ`;
    } else if (minutes > 0) {
      return `${minutes} phút`;
    }
    return '0 phút';
  };

  return (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-h3 font-semibold text-gray-900 mb-2">
          Xác nhận thông tin đặt phòng
        </h2>
        <p className="text-base text-gray-600">
          Kiểm tra lại thông tin trước khi tạo đặt phòng
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Column - Booking Details */}
        <div className="space-y-6">
          {/* Location & Room Information */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <MapPin className="w-5 h-5" />
              Chi nhánh & Phòng
            </h3>

            {selectedLocation && selectedRoom ? (
              <div className="space-y-4">
                {/* Location */}
                <div className="flex items-start gap-4">
                  {selectedLocation.imageUrl && (
                    <img
                      src={selectedLocation.imageUrl}
                      alt={getLocalizedText(selectedLocation.name)}
                      className="w-16 h-16 rounded-lg object-cover flex-shrink-0"
                    />
                  )}
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">
                      {getLocalizedText(selectedLocation.name)}
                    </h4>
                    <p className="text-sm text-gray-600 mt-1">
                      {selectedLocation.address}
                    </p>
                  </div>
                </div>

                {/* Room */}
                <div className="border-t border-gray-200 pt-4">
                  <div className="flex items-start gap-4">
                    {selectedRoom.images && selectedRoom.images.length > 0 && (
                      <img
                        src={selectedRoom.images[0]}
                        alt={getLocalizedText(selectedRoom.name)}
                        className="w-16 h-16 rounded-lg object-cover flex-shrink-0"
                      />
                    )}
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">
                        {getLocalizedText(selectedRoom.name)}
                      </h4>
                      <div className="flex items-center gap-4 mt-1 text-sm text-gray-600">
                        <span className="flex items-center gap-1">
                          <Users className="w-4 h-4" />
                          Sức chứa: {selectedRoom.capacity} người
                        </span>
                        <span className="flex items-center gap-1">
                          <DollarSign className="w-4 h-4" />
                          {formatPrice(selectedRoom.pricePerHour)}/giờ
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-gray-500">Chưa chọn chi nhánh và phòng</p>
            )}
          </div>

          {/* Date & Time Information */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Clock className="w-5 h-5" />
              Thời gian đặt phòng
            </h3>

            {formData.startTime && formData.endTime ? (
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm text-gray-600">Ngày:</span>
                    <div className="font-medium text-gray-900">
                      {formatDate(formData.startTime)}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600">Thời lượng:</span>
                    <div className="font-medium text-gray-900">
                      {formatDuration()}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm text-gray-600">Bắt đầu:</span>
                    <div className="font-medium text-gray-900">
                      {formatTime(formData.startTime)}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600">Kết thúc:</span>
                    <div className="font-medium text-gray-900">
                      {formatTime(formData.endTime)}
                    </div>
                  </div>
                </div>

                <div>
                  <span className="text-sm text-gray-600">Số khách:</span>
                  <div className="font-medium text-gray-900 flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    {formData.numberOfGuests} người
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-gray-500">Chưa chọn thời gian</p>
            )}
          </div>

          {/* Customer Information */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <User className="w-5 h-5" />
              Thông tin khách hàng
            </h3>

            {formData.isGuestBooking ? (
              <div className="space-y-3">
                <div className="text-sm text-blue-600 bg-blue-50 px-3 py-2 rounded-lg">
                  Đặt phòng khách lẻ (Guest)
                </div>

                {formData.guestName && (
                  <div>
                    <span className="text-sm text-gray-600">Họ và tên:</span>
                    <div className="font-medium text-gray-900">
                      {formData.guestName}
                    </div>
                  </div>
                )}

                {formData.guestEmail && (
                  <div>
                    <span className="text-sm text-gray-600">Email:</span>
                    <div className="font-medium text-gray-900 flex items-center gap-1">
                      <Mail className="w-4 h-4" />
                      {formData.guestEmail}
                    </div>
                  </div>
                )}

                {formData.guestPhone && (
                  <div>
                    <span className="text-sm text-gray-600">Điện thoại:</span>
                    <div className="font-medium text-gray-900 flex items-center gap-1">
                      <Phone className="w-4 h-4" />
                      {formData.guestPhone}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-3">
                <div className="text-sm text-green-600 bg-green-50 px-3 py-2 rounded-lg">
                  Khách hàng có tài khoản
                </div>

                {selectedUser ? (
                  <div className="space-y-3">
                    <div>
                      <span className="text-sm text-gray-600">Họ và tên:</span>
                      <div className="font-medium text-gray-900">
                        {selectedUser.name || 'Chưa có tên'}
                      </div>
                    </div>

                    <div>
                      <span className="text-sm text-gray-600">Email:</span>
                      <div className="font-medium text-gray-900 flex items-center gap-1">
                        <Mail className="w-4 h-4" />
                        {selectedUser.email}
                      </div>
                    </div>

                    {selectedUser.phoneNumber && (
                      <div>
                        <span className="text-sm text-gray-600">
                          Điện thoại:
                        </span>
                        <div className="font-medium text-gray-900 flex items-center gap-1">
                          <Phone className="w-4 h-4" />
                          {selectedUser.phoneNumber}
                        </div>
                      </div>
                    )}

                    <div className="grid grid-cols-2 gap-4 pt-2 border-t border-gray-200">
                      <div>
                        <span className="text-sm text-gray-600">Vai trò:</span>
                        <div className="font-medium text-gray-900">
                          {selectedUser.role === 'CUSTOMER'
                            ? 'Khách hàng'
                            : selectedUser.role === 'ADMIN'
                              ? 'Nhân viên'
                              : selectedUser.role === 'SUPER_ADMIN'
                                ? 'Quản trị viên'
                                : 'Không xác định'}
                        </div>
                      </div>
                      <div>
                        <span className="text-sm text-gray-600">
                          Số đặt phòng:
                        </span>
                        <div className="font-medium text-gray-900">
                          {selectedUser.bookingCount || 0} lần
                        </div>
                      </div>
                    </div>

                    {selectedUser.totalSpent && selectedUser.totalSpent > 0 && (
                      <div>
                        <span className="text-sm text-gray-600">
                          Tổng chi tiêu:
                        </span>
                        <div className="font-medium text-gray-900">
                          {new Intl.NumberFormat('vi-VN', {
                            style: 'currency',
                            currency: 'VND',
                          }).format(selectedUser.totalSpent)}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-600"></div>
                    <span className="ml-2 text-gray-600">
                      Đang tải thông tin khách hàng...
                    </span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Right Column - Summary & Notes */}
        <div className="space-y-6">
          {/* Pricing Summary */}
          <div className="bg-gold-50 border border-gold-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              Tóm tắt giá
            </h3>

            {selectedRoom && formData.startTime && formData.endTime ? (
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Giá phòng:</span>
                  <span className="text-gray-900">
                    {formatPrice(selectedRoom.pricePerHour)}/giờ
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Thời lượng:</span>
                  <span className="text-gray-900">{formatDuration()}</span>
                </div>

                <div className="border-t border-gold-300 pt-3">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold text-gray-900">
                      Tổng cộng:
                    </span>
                    <span className="text-xl font-bold text-gold-600">
                      {formatPrice(totalPrice)}
                    </span>
                  </div>
                </div>

                <div className="text-xs text-gray-600 bg-white px-3 py-2 rounded border border-gold-200">
                  <div className="font-medium mb-1">Chi tiết tính giá:</div>
                  <div>
                    {formatPrice(selectedRoom.pricePerHour)} ×{' '}
                    {formatDuration()} = {formatPrice(totalPrice)}
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-gray-500">Chưa thể tính giá</p>
            )}
          </div>

          {/* Notes */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Ghi chú
            </h3>

            <div>
              <label
                htmlFor="finalNotes"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Ghi chú cho đặt phòng:
              </label>
              <textarea
                id="finalNotes"
                value={formData.notes || ''}
                onChange={e => onUpdate({ notes: e.target.value })}
                rows={4}
                className="w-full px-3 py-3 border border-gray-300 rounded-lg text-black focus:ring-2 focus:ring-gold-500 focus:border-gold-500"
                placeholder="Nhập ghi chú đặc biệt cho đặt phòng này..."
              />
            </div>
          </div>

          {/* Important Notes */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h4 className="font-medium text-blue-900 mb-3">Lưu ý quan trọng</h4>
            <ul className="text-sm text-blue-800 space-y-2">
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
                <span>Đặt phòng sẽ được tạo với trạng thái "Đã xác nhận"</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
                <span>Thông tin khách hàng sẽ được lưu trữ để liên hệ</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
                <span>Có thể chỉnh sửa hoặc hủy đặt phòng sau khi tạo</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
                <span>
                  Giá cuối cùng có thể thay đổi dựa trên chính sách hiện tại
                </span>
              </li>
            </ul>
          </div>

          {/* Confirmation Status */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
            <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-8 h-8 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-green-900 mb-2">
              Sẵn sàng tạo đặt phòng
            </h4>
            <p className="text-green-700">
              Tất cả thông tin đã được điền đầy đủ. Nhấn "Tạo đặt phòng" để hoàn
              tất.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingFormSummary;
