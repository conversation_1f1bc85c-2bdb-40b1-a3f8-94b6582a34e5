'use client';

import React from 'react';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import { PopularRoomDto } from 'shared-types';
import { analyticsService } from '../../../services/analytics.service';

interface PopularRoomsChartProps {
  data: PopularRoomDto[];
  height?: number;
  metric?: 'bookingCount' | 'revenue' | 'occupancyRate';
  layout?: 'horizontal' | 'vertical';
}

const PopularRoomsChart: React.FC<PopularRoomsChartProps> = ({
  data,
  height = 400,
  metric = 'bookingCount',
  layout = 'horizontal',
}) => {
  const chartData = data.map((item, index) => ({
    ...item,
    displayName: `${item.roomName} (${item.locationName})`,
    value: item[metric],
    color: getBarColor(index),
  }));

  function getBarColor(index: number): string {
    const colors = [
      '#065F46', // Queens Eyes green
      '#8B2635', // Ruby red
      '#1E3A8A', // Sapphire blue
      '#7C3AED', // Opal purple
      '#374151', // <PERSON> gray
      '#1F2937', // Hall dark
      '#987947', // Gold
      '#059669', // Emerald
      '#DC2626', // Red
      '#2563EB', // Blue
    ];
    return colors[index % colors.length];
  }

  const formatValue = (value: number, metricType: string) => {
    switch (metricType) {
      case 'revenue':
        return analyticsService.formatCurrency(value);
      case 'occupancyRate':
        return `${value.toFixed(1)}%`;
      default:
        return value.toString();
    }
  };

  const getMetricLabel = (metricType: string) => {
    switch (metricType) {
      case 'bookingCount':
        return 'Số đặt phòng';
      case 'revenue':
        return 'Doanh thu';
      case 'occupancyRate':
        return 'Tỷ lệ lấp đầy';
      default:
        return 'Giá trị';
    }
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (!active || !payload || !payload.length) return null;

    const data = payload[0].payload;

    return (
      <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg max-w-xs">
        <p className="text-sm font-medium text-gray-800 mb-2">
          {data.roomName}
        </p>
        <p className="text-xs text-gray-600 mb-3">{data.locationName}</p>
        <div className="space-y-1">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Số đặt phòng:</span>
            <span className="text-sm font-semibold text-gray-800">
              {data.bookingCount}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Doanh thu:</span>
            <span className="text-sm font-semibold text-gray-800">
              {analyticsService.formatCurrency(data.revenue)}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Tỷ lệ lấp đầy:</span>
            <span className="text-sm font-semibold text-gray-800">
              {data.occupancyRate.toFixed(1)}%
            </span>
          </div>
        </div>
      </div>
    );
  };

  if (layout === 'horizontal') {
    return (
      <div style={{ height: `${height}px` }} className="w-full">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            layout="horizontal"
            data={chartData}
            margin={{
              top: 20,
              right: 30,
              left: 100,
              bottom: 20,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis
              type="number"
              stroke="#6b7280"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={value =>
                metric === 'revenue'
                  ? analyticsService.formatNumber(value)
                  : value
              }
            />
            <YAxis
              type="category"
              dataKey="displayName"
              stroke="#6b7280"
              fontSize={11}
              tickLine={false}
              axisLine={false}
              width={90}
              tick={{ textAnchor: 'end' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar dataKey="value" radius={[0, 4, 4, 0]}>
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>
    );
  }

  return (
    <div style={{ height: `${height}px` }} className="w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 80,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="displayName"
            stroke="#6b7280"
            fontSize={11}
            tickLine={false}
            axisLine={false}
            angle={-45}
            textAnchor="end"
            height={80}
            interval={0}
          />
          <YAxis
            stroke="#6b7280"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            tickFormatter={value =>
              metric === 'revenue'
                ? analyticsService.formatNumber(value)
                : value
            }
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="value" radius={[4, 4, 0, 0]}>
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default PopularRoomsChart;
