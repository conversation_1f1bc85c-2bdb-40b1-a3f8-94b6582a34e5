import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class BookingReferenceGenerator {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Generate a unique booking reference in the format: QB-YYMMDD-XXX
   * Where:
   * - QB = Queen Booking (brand prefix)
   * - YYMMDD = Year Month Day (6 digits)
   * - XXX = Sequential counter for the day (3 digits, padded with zeros)
   *
   * Examples: QB-240115-001, QB-240115-002, QB-241225-001
   */
  async generateBookingReference(): Promise<string> {
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2); // Last 2 digits of year
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const datePrefix = `${year}${month}${day}`;

    // Get today's booking count to generate sequential number
    const startOfDay = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
    );
    const endOfDay = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate() + 1,
    );

    const todayBookingCount = await this.prisma.booking.count({
      where: {
        createdAt: {
          gte: startOfDay,
          lt: endOfDay,
        },
      },
    });

    // Sequential number starts from 1, padded to 3 digits
    const sequentialNumber = (todayBookingCount + 1)
      .toString()
      .padStart(3, '0');

    const bookingReference = `QB-${datePrefix}-${sequentialNumber}`;

    // Check if this reference already exists (edge case handling)
    const existingBooking = await this.prisma.booking.findUnique({
      where: { bookingReference },
    });

    if (existingBooking) {
      // If it exists, try with next number
      const nextSequentialNumber = (todayBookingCount + 2)
        .toString()
        .padStart(3, '0');
      return `QB-${datePrefix}-${nextSequentialNumber}`;
    }

    return bookingReference;
  }

  /**
   * Alternative format: QB1234 (Airline style - shorter)
   * 6 characters total: QB + 4 random alphanumeric characters
   */
  async generateShortBookingReference(): Promise<string> {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let reference: string;
    let attempts = 0;
    const maxAttempts = 10;

    do {
      let randomPart = '';
      for (let i = 0; i < 4; i++) {
        randomPart += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      reference = `QB${randomPart}`;

      // Check if this reference already exists
      const existingBooking = await this.prisma.booking.findUnique({
        where: { bookingReference: reference },
      });

      if (!existingBooking) {
        break;
      }

      attempts++;
    } while (attempts < maxAttempts);

    if (attempts >= maxAttempts) {
      throw new Error(
        'Unable to generate unique booking reference after multiple attempts',
      );
    }

    return reference;
  }

  /**
   * Elegant format: QB240115A (Brand + Date + Letter)
   * Where the letter increments for each booking on the same day
   */
  async generateElegantBookingReference(): Promise<string> {
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2);
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const datePrefix = `${year}${month}${day}`;

    // Get today's booking count to determine the letter
    const startOfDay = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
    );
    const endOfDay = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate() + 1,
    );

    const todayBookingCount = await this.prisma.booking.count({
      where: {
        createdAt: {
          gte: startOfDay,
          lt: endOfDay,
        },
      },
    });

    // Convert number to letter (A=0, B=1, etc.)
    const letterIndex = todayBookingCount % 26;
    const letter = String.fromCharCode(65 + letterIndex); // A-Z

    const bookingReference = `QB${datePrefix}${letter}`;

    // Check if this reference already exists
    const existingBooking = await this.prisma.booking.findUnique({
      where: { bookingReference },
    });

    if (existingBooking) {
      // If we've used all letters for today, add a number
      const nextLetter = String.fromCharCode(
        65 + ((todayBookingCount + 1) % 26),
      );
      return `QB${datePrefix}${nextLetter}`;
    }

    return bookingReference;
  }
}
