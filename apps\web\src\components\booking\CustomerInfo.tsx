'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Mail,
  Phone,
  UserIcon,
  UserPlus,
  LogOut,
} from 'lucide-react';
import type { Location, LocalizedString, Room, User } from 'shared-types';
import { Button } from '../ui/button';

interface CustomerInfoProps {
  selectedLocation: Location;
  selectedRoom: Room;
  user: User | null;
  onBack: () => void;
  onContinue: (data: CustomerInfoFormData) => void;
  onLoginRedirect: () => void;
  onLogout: () => void;
}

export interface CustomerInfoFormData {
  isGuestBooking: boolean;
  userId?: string;
  guestName?: string;
  guestEmail?: string;
  guestPhone?: string;
  notes?: string;
}

interface FormErrors {
  guestName?: string;
  guestEmail?: string;
  guestPhone?: string;
  isGuestBooking?: string;
}

export function CustomerInfo({
  selectedLocation,
  selectedRoom,
  user,
  onBack,
  onContinue,
  onLoginRedirect,
  onLogout,
}: CustomerInfoProps) {
  const [formData, setFormData] = useState<CustomerInfoFormData>({
    isGuestBooking: true,
  });
  const [errors, setErrors] = useState<FormErrors>({});

  const handleBookingTypeChange = (isGuest: boolean) => {
    // If switching to user booking and not logged in, redirect to login
    if (!isGuest && !user) {
      onLoginRedirect();
      return;
    }

    setFormData(prev => ({
      ...prev,
      isGuestBooking: isGuest,
      userId: isGuest ? undefined : user?.id,
      guestName: '',
      guestEmail: '',
      guestPhone: '',
    }));
  };

  const handleGuestInfoChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    // Clear related error
    setErrors(prev => ({
      ...prev,
      [field]: undefined,
    }));
  };

  const handleSubmit = () => {
    const newErrors: FormErrors = {};

    if (!formData.isGuestBooking && !user) {
      newErrors.isGuestBooking = 'Please log in to continue';
    }

    if (formData.isGuestBooking) {
      if (!formData.guestName?.trim()) {
        newErrors.guestName = 'Name is required';
      }
      if (!formData.guestEmail) {
        newErrors.guestEmail = 'Email is required';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.guestEmail)) {
        newErrors.guestEmail = 'Invalid email format';
      }
      if (!formData.guestPhone) {
        newErrors.guestPhone = 'Phone number is required';
      }
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Clear any existing errors
    setErrors({});
    onContinue(formData);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="ghost"
          size="icon"
          onClick={onBack}
          className="text-gold-600 hover:text-gold-700 hover:bg-gold-50"
        >
          <ArrowLeft className="h-6 w-6" />
        </Button>
        <h2 className="text-2xl md:text-3xl font-semibold text-gold-900">
          Thông tin đặt phòng
        </h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-[1fr,2fr] gap-8">
        {/* Selected Location & Room Card */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="rounded-xl overflow-hidden backdrop-blur-md relative h-[450px] ring-1 ring-gold-200/30 shadow-[0_0_15px_-3px_rgba(234,179,8,0.2)]"
        >
          <div className="relative h-full">
            {selectedRoom.images?.[0] ? (
              <img
                src={selectedRoom.images[0]}
                alt={selectedRoom.name.vi}
                className="w-full h-1/2 object-cover"
              />
            ) : (
              <div className="w-full h-1/2 bg-gradient-to-br from-gold-100 to-gold-50 flex items-center justify-center">
                <UserIcon className="w-12 h-12 text-gold-400" />
              </div>
            )}

            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent">
              <div className="absolute bottom-0 left-0 right-0 p-6 text-white space-y-4">
                <div>
                  <h3 className="text-xl md:text-2xl font-semibold drop-shadow-lg mb-2">
                    {(selectedRoom.name as LocalizedString).vi}
                  </h3>
                  <h4 className="text-lg text-gold-300 mb-3">
                    {(selectedLocation.name as LocalizedString).vi}
                  </h4>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <UserIcon className="w-5 h-5 text-gold-300 shrink-0" />
                    <span className="text-sm sm:text-base font-light">
                      {selectedRoom.capacity} người
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-gold-300 shrink-0" />
                    <span className="text-sm sm:text-base font-light">
                      {selectedLocation.email}
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-gold-300 shrink-0" />
                    <span className="text-sm sm:text-base font-light">
                      {selectedLocation.phoneNumber}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Customer Information Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/80 backdrop-blur-sm rounded-xl p-8 ring-1 ring-gold-200/30 shadow-[0_0_15px_-3px_rgba(234,179,8,0.2)]"
        >
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-gold-900 mb-6">
              Thông tin khách hàng
            </h3>

            {/* Booking Type Selection */}
            <div className="space-y-4 mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Loại đặt phòng <span className="text-red-500">*</span>
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Guest Booking */}
                <div
                  className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                    formData.isGuestBooking
                      ? 'border-gold-500 bg-gold-50 ring-2 ring-gold-200'
                      : 'border-gray-300 bg-white hover:border-gray-400'
                  }`}
                  onClick={() => handleBookingTypeChange(true)}
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        formData.isGuestBooking
                          ? 'bg-gold-600 text-white'
                          : 'bg-gray-100 text-gray-400'
                      }`}
                    >
                      <UserPlus className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <h3
                        className={`font-medium ${
                          formData.isGuestBooking
                            ? 'text-gold-700'
                            : 'text-gray-900'
                        }`}
                      >
                        Khách lẻ (Guest)
                      </h3>
                      <p className="text-sm text-gray-600">
                        Đặt phòng không cần tài khoản
                      </p>
                    </div>
                  </div>
                </div>

                {/* User Booking */}
                <div
                  className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                    !formData.isGuestBooking
                      ? 'border-gold-500 bg-gold-50 ring-2 ring-gold-200'
                      : 'border-gray-300 bg-white hover:border-gray-400'
                  }`}
                  onClick={() => handleBookingTypeChange(false)}
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        !formData.isGuestBooking
                          ? 'bg-gold-600 text-white'
                          : 'bg-gray-100 text-gray-400'
                      }`}
                    >
                      <UserIcon className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <h3
                        className={`font-medium ${
                          !formData.isGuestBooking
                            ? 'text-gold-700'
                            : 'text-gray-900'
                        }`}
                      >
                        {user ? 'Tài khoản của bạn' : 'Đăng nhập'}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {user
                          ? 'Đặt phòng với tài khoản hiện tại'
                          : 'Đặt phòng với tài khoản'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Guest Information Form or User Info */}
            {formData.isGuestBooking ? (
              <div className="space-y-6">
                <div>
                  <label
                    htmlFor="guestName"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Họ và tên <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="guestName"
                    value={formData.guestName || ''}
                    onChange={e =>
                      handleGuestInfoChange('guestName', e.target.value)
                    }
                    className={`w-full px-3 py-3 border rounded-lg text-black focus:ring-2 focus:ring-gold-500 focus:border-gold-500 ${
                      errors.guestName ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Nhập họ và tên của bạn"
                  />
                  {errors.guestName && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.guestName}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    htmlFor="guestEmail"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Email <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type="email"
                      id="guestEmail"
                      value={formData.guestEmail || ''}
                      onChange={e =>
                        handleGuestInfoChange('guestEmail', e.target.value)
                      }
                      className={`w-full pl-10 pr-3 py-3 border rounded-lg text-black focus:ring-2 focus:ring-gold-500 focus:border-gold-500 ${
                        errors.guestEmail ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Nhập địa chỉ email"
                    />
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  </div>
                  {errors.guestEmail && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.guestEmail}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    htmlFor="guestPhone"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Số điện thoại <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type="tel"
                      id="guestPhone"
                      value={formData.guestPhone || ''}
                      onChange={e =>
                        handleGuestInfoChange('guestPhone', e.target.value)
                      }
                      className={`w-full pl-10 pr-3 py-3 border rounded-lg text-black focus:ring-2 focus:ring-gold-500 focus:border-gold-500 ${
                        errors.guestPhone ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Nhập số điện thoại"
                    />
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  </div>
                  {errors.guestPhone && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.guestPhone}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    htmlFor="notes"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Ghi chú thêm
                  </label>
                  <textarea
                    id="notes"
                    value={formData.notes || ''}
                    onChange={e =>
                      handleGuestInfoChange('notes', e.target.value)
                    }
                    rows={3}
                    className="w-full px-3 py-3 border border-gray-300 rounded-lg text-black focus:ring-2 focus:ring-gold-500 focus:border-gold-500"
                    placeholder="Nhập ghi chú thêm cho đặt phòng (không bắt buộc)"
                  />
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {user ? (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-6"
                  >
                    <div className="bg-gold-50 rounded-lg p-6 border border-gold-200">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <h4 className="text-lg font-medium text-gold-900">
                            {user.name}
                          </h4>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Mail className="w-4 h-4" />
                            <span>{user.email}</span>
                          </div>
                          {user.phoneNumber && (
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <Phone className="w-4 h-4" />
                              <span>{user.phoneNumber}</span>
                            </div>
                          )}
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={onLogout}
                          className="text-gray-500 hover:text-red-600 hover:bg-red-50"
                        >
                          <LogOut className="w-4 h-4 mr-2" />
                          Đăng xuất
                        </Button>
                      </div>
                    </div>

                    <div>
                      <label
                        htmlFor="notes"
                        className="block text-sm font-medium text-gray-700 mb-2"
                      >
                        Ghi chú thêm
                      </label>
                      <textarea
                        id="notes"
                        value={formData.notes || ''}
                        onChange={e =>
                          handleGuestInfoChange('notes', e.target.value)
                        }
                        rows={3}
                        className="w-full px-3 py-3 border border-gray-300 rounded-lg text-black focus:ring-2 focus:ring-gold-500 focus:border-gold-500"
                        placeholder="Nhập ghi chú thêm cho đặt phòng (không bắt buộc)"
                      />
                    </div>
                  </motion.div>
                ) : (
                  <div className="space-y-4">
                    <p className="text-center text-gray-600">
                      Vui lòng đăng nhập để tiếp tục đặt phòng
                    </p>
                    <div className="flex justify-center">
                      <Button
                        className="bg-gold-600 hover:bg-gold-700 text-white px-8 py-3 rounded-lg"
                        onClick={onLoginRedirect}
                      >
                        Đăng nhập
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Continue Button */}
            {(formData.isGuestBooking || user) && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="mt-8"
              >
                <Button
                  onClick={handleSubmit}
                  className="w-full bg-gold-600 hover:bg-gold-500 text-white"
                  size="lg"
                >
                  Tiếp tục
                </Button>
              </motion.div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
