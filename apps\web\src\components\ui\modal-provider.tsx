'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import { AlertDialog, ConfirmDialog, AlertType } from './dialog';

interface AlertOptions {
  title: string;
  message: string;
  type?: AlertType;
  confirmText?: string;
}

interface ConfirmOptions {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'danger';
}

interface ModalContextType {
  showAlert: (options: AlertOptions) => Promise<void>;
  showConfirm: (options: ConfirmOptions) => Promise<boolean>;
}

const ModalContext = createContext<ModalContextType | undefined>(undefined);

interface ModalProviderProps {
  children: React.ReactNode;
}

export const ModalProvider: React.FC<ModalProviderProps> = ({ children }) => {
  const [alertState, setAlertState] = useState<{
    isOpen: boolean;
    options: AlertOptions | null;
    resolve: (() => void) | null;
  }>({
    isOpen: false,
    options: null,
    resolve: null,
  });

  const [confirmState, setConfirmState] = useState<{
    isOpen: boolean;
    options: ConfirmOptions | null;
    resolve: ((value: boolean) => void) | null;
  }>({
    isOpen: false,
    options: null,
    resolve: null,
  });

  const showAlert = useCallback((options: AlertOptions): Promise<void> => {
    return new Promise(resolve => {
      setAlertState({
        isOpen: true,
        options,
        resolve,
      });
    });
  }, []);

  const showConfirm = useCallback(
    (options: ConfirmOptions): Promise<boolean> => {
      return new Promise(resolve => {
        setConfirmState({
          isOpen: true,
          options,
          resolve,
        });
      });
    },
    []
  );

  const handleAlertClose = useCallback(() => {
    if (alertState.resolve) {
      alertState.resolve();
    }
    setAlertState({
      isOpen: false,
      options: null,
      resolve: null,
    });
  }, [alertState.resolve]);

  const handleConfirmClose = useCallback(() => {
    if (confirmState.resolve) {
      confirmState.resolve(false);
    }
    setConfirmState({
      isOpen: false,
      options: null,
      resolve: null,
    });
  }, [confirmState.resolve]);

  const handleConfirmConfirm = useCallback(() => {
    if (confirmState.resolve) {
      confirmState.resolve(true);
    }
    setConfirmState({
      isOpen: false,
      options: null,
      resolve: null,
    });
  }, [confirmState.resolve]);

  const contextValue: ModalContextType = {
    showAlert,
    showConfirm,
  };

  return (
    <ModalContext.Provider value={contextValue}>
      {children}

      {alertState.options && (
        <AlertDialog
          isOpen={alertState.isOpen}
          onClose={handleAlertClose}
          title={alertState.options.title}
          message={alertState.options.message}
          type={alertState.options.type}
          confirmText={alertState.options.confirmText}
        />
      )}

      {confirmState.options && (
        <ConfirmDialog
          isOpen={confirmState.isOpen}
          onClose={handleConfirmClose}
          onConfirm={handleConfirmConfirm}
          title={confirmState.options.title}
          message={confirmState.options.message}
          confirmText={confirmState.options.confirmText}
          cancelText={confirmState.options.cancelText}
          variant={confirmState.options.variant}
        />
      )}
    </ModalContext.Provider>
  );
};

export const useModal = (): ModalContextType => {
  const context = useContext(ModalContext);
  if (context === undefined) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};

// Convenience functions that match browser API
export const useDialogs = () => {
  const { showAlert, showConfirm } = useModal();

  const alert = useCallback(
    async (
      message: string,
      title: string = 'Thông báo',
      type: AlertType = 'info'
    ) => {
      await showAlert({ title, message, type });
    },
    [showAlert]
  );

  const confirm = useCallback(
    async (message: string, title: string = 'Xác nhận') => {
      return await showConfirm({ title, message, variant: 'default' });
    },
    [showConfirm]
  );

  const confirmDelete = useCallback(
    async (message: string, title: string = 'Xác nhận xóa') => {
      return await showConfirm({
        title,
        message,
        variant: 'danger',
        confirmText: 'Xóa',
        cancelText: 'Hủy bỏ',
      });
    },
    [showConfirm]
  );

  return { alert, confirm, confirmDelete };
};
