'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { UserRole } from 'shared-types';
import { usersService } from '../../../../services/users.service';
import { Button } from '../../../../components/ui/button';
import { Input } from '../../../../components/ui/input';
import { Label } from '../../../../components/ui/label';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from '../../../../components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../components/ui/select';
import { ArrowLeft, UserPlus } from 'lucide-react';
import Link from 'next/link';

interface NewUserFormData {
  email: string;
  name: string;
  phoneNumber: string;
  role: UserRole;
  isActive: boolean;
  sendWelcomeEmail: boolean;
}

const NewUserPage = () => {
  const router = useRouter();
  const [formData, setFormData] = useState<NewUserFormData>({
    email: '',
    name: '',
    phoneNumber: '',
    role: UserRole.CUSTOMER,
    isActive: true,
    sendWelcomeEmail: true,
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleRoleChange = (value: string) => {
    setFormData(prev => ({ ...prev, role: value as UserRole }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.email.trim()) {
      newErrors.email = 'Email là bắt buộc';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email không hợp lệ';
    }

    if (!formData.name.trim()) {
      newErrors.name = 'Tên là bắt buộc';
    }

    if (formData.phoneNumber && !/^[0-9+\-\s()]+$/.test(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Số điện thoại không hợp lệ';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Create the AdminCreateUserDto from form data
      const createUserData = {
        email: formData.email,
        name: formData.name || undefined,
        phoneNumber: formData.phoneNumber || undefined,
        role: formData.role,
        isActive: formData.isActive,
        sendWelcomeEmail: formData.sendWelcomeEmail,
      };

      // Call the API to create user
      await usersService.createUser(createUserData);

      // Redirect back to users list on success
      router.push('/dashboard/users');
    } catch (error) {
      console.error('Error creating user:', error);

      // Handle specific backend validation errors
      if (error instanceof Error) {
        const errorMessage = error.message;

        if (errorMessage.includes('Email đã được sử dụng')) {
          setErrors({
            email: 'Email này đã được sử dụng bởi tài khoản khác',
          });
        } else if (errorMessage.includes('Số điện thoại đã được sử dụng')) {
          setErrors({
            phoneNumber: 'Số điện thoại này đã được sử dụng bởi tài khoản khác',
          });
        } else {
          setErrors({
            general:
              errorMessage ||
              'Có lỗi xảy ra khi tạo người dùng. Vui lòng thử lại.',
          });
        }
      } else {
        setErrors({
          general: 'Có lỗi xảy ra khi tạo người dùng. Vui lòng thử lại.',
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const getRoleLabel = (role: UserRole): string => {
    return usersService.getRoleLabel(role);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/dashboard/users">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Thêm người dùng mới
          </h1>
          <p className="text-gray-900 mt-1">
            Tạo tài khoản mới cho người dùng và gán vai trò phù hợp
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-900">
              <UserPlus className="h-5 w-5" />
              Thông tin người dùng
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {errors.general && (
                <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
                  {errors.general}
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-gray-900">
                    Email <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                    className={`text-black ${errors.email ? 'border-red-500' : ''}`}
                  />
                  {errors.email && (
                    <p className="text-sm text-red-500">{errors.email}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="name" className="text-gray-900">
                    Tên đầy đủ <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Nguyễn Văn A"
                    className={`text-black ${errors.name ? 'border-red-500' : ''}`}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500">{errors.name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phoneNumber" className="text-gray-900">
                    Số điện thoại
                  </Label>
                  <Input
                    id="phoneNumber"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                    placeholder="+84 123 456 789"
                    className={`text-black ${errors.phoneNumber ? 'border-red-500' : ''}`}
                  />
                  {errors.phoneNumber && (
                    <p className="text-sm text-red-500">{errors.phoneNumber}</p>
                  )}
                  <p className="text-xs text-gray-500">
                    Tùy chọn - Nếu nhập, số điện thoại phải là duy nhất trong hệ
                    thống
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role" className="text-gray-900">
                    Vai trò <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={formData.role}
                    onValueChange={handleRoleChange}
                  >
                    <SelectTrigger className="text-black">
                      <span className="text-black">
                        {formData.role
                          ? getRoleLabel(formData.role)
                          : 'Chọn vai trò'}
                      </span>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={UserRole.CUSTOMER}>
                        <span className="text-black">
                          {getRoleLabel(UserRole.CUSTOMER)}
                        </span>
                      </SelectItem>
                      <SelectItem value={UserRole.ADMIN}>
                        <span className="text-black">
                          {getRoleLabel(UserRole.ADMIN)}
                        </span>
                      </SelectItem>
                      <SelectItem value={UserRole.SUPER_ADMIN}>
                        <span className="text-black">
                          {getRoleLabel(UserRole.SUPER_ADMIN)}
                        </span>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    id="isActive"
                    name="isActive"
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-gold-600 focus:ring-gold-500"
                  />
                  <Label htmlFor="isActive" className="text-sm text-gray-900">
                    Kích hoạt tài khoản ngay lập tức
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    id="sendWelcomeEmail"
                    name="sendWelcomeEmail"
                    type="checkbox"
                    checked={formData.sendWelcomeEmail}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-gold-600 focus:ring-gold-500"
                  />
                  <Label
                    htmlFor="sendWelcomeEmail"
                    className="text-sm text-gray-900"
                  >
                    Gửi email chào mừng với mật khẩu tạm thời
                  </Label>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <h4 className="font-medium text-blue-900 mb-2">Lưu ý:</h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>
                    • Mật khẩu tạm thời sẽ được tạo tự động và gửi qua email
                  </li>
                  <li>
                    • Người dùng sẽ cần đăng nhập và thay đổi mật khẩu lần đầu
                  </li>
                  <li>
                    • Tài khoản có thể được kích hoạt/vô hiệu hóa sau khi tạo
                  </li>
                </ul>
              </div>

              <div className="flex gap-3 pt-6 border-t">
                <Button
                  type="submit"
                  disabled={loading}
                  className="bg-gold-600 hover:bg-gold-700"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Đang tạo...
                    </>
                  ) : (
                    <>
                      <UserPlus className="h-4 w-4 mr-2" />
                      Tạo người dùng
                    </>
                  )}
                </Button>
                <Link href="/dashboard/users">
                  <Button variant="outline" type="button">
                    Hủy
                  </Button>
                </Link>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default NewUserPage;
