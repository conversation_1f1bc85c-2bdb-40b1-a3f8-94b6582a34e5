'use client';

import { BookingStatus } from 'shared-types';

interface StatusBadgeProps {
  status: BookingStatus;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  size = 'md',
  className = '',
}) => {
  const getStatusConfig = (status: BookingStatus) => {
    switch (status) {
      case BookingStatus.PENDING_PAYMENT:
        return {
          label: 'Chờ thanh toán',
          bgColor: 'bg-yellow-100',
          textColor: 'text-yellow-800',
          borderColor: 'border-yellow-300',
        };
      case BookingStatus.CONFIRMED:
        return {
          label: 'Đã xác nhận',
          bgColor: 'bg-green-100',
          textColor: 'text-green-800',
          borderColor: 'border-green-300',
        };
      case BookingStatus.COMPLETED:
        return {
          label: '<PERSON><PERSON><PERSON> thành',
          bgColor: 'bg-blue-100',
          textColor: 'text-blue-800',
          borderColor: 'border-blue-300',
        };
      case BookingStatus.CANCELLED_BY_USER:
        return {
          label: 'Khách hủy',
          bgColor: 'bg-red-100',
          textColor: 'text-red-800',
          borderColor: 'border-red-300',
        };
      case BookingStatus.CANCELLED_BY_ADMIN:
        return {
          label: 'Admin hủy',
          bgColor: 'bg-red-100',
          textColor: 'text-red-800',
          borderColor: 'border-red-300',
        };
      case BookingStatus.NO_SHOW:
        return {
          label: 'Không đến',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          borderColor: 'border-gray-300',
        };
      default:
        return {
          label: status,
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          borderColor: 'border-gray-300',
        };
    }
  };

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-xs';
      case 'lg':
        return 'px-4 py-2 text-sm';
      default:
        return 'px-3 py-1 text-sm';
    }
  };

  const config = getStatusConfig(status);
  const sizeClasses = getSizeClasses(size);

  return (
    <span
      className={`
        inline-flex items-center
        ${sizeClasses}
        ${config.bgColor}
        ${config.textColor}
        ${config.borderColor}
        border rounded-full
        font-medium
        transition-colors duration-200
        ${className}
      `}
    >
      {config.label}
    </span>
  );
};

export default StatusBadge;
