import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, UserRole } from 'shared-types';

export interface AuthState {
  // State
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  isInitialized: boolean;

  // Actions
  setUser: (user: User) => void;
  setToken: (token: string) => void;
  setAuth: (user: User, token: string) => void;
  clearAuth: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  initializeAuth: () => void;

  // Helper methods
  hasRole: (role: UserRole) => boolean;
  isAdmin: () => boolean;
  isCustomer: () => boolean;
}

// Helper function to sync token with localStorage
const syncTokenToLocalStorage = (token: string | null) => {
  if (typeof window !== 'undefined') {
    if (token) {
      localStorage.setItem('auth-token', token);
    } else {
      localStorage.removeItem('auth-token');
    }
  }
};

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: true, // Start with loading true until initialized
      error: null,
      isInitialized: false,

      // Actions
      setUser: (user: User) => {
        set({
          user,
          isAuthenticated: true,
          error: null,
        });
      },

      setToken: (token: string) => {
        syncTokenToLocalStorage(token);
        set({ token });
      },

      setAuth: (user: User, token: string) => {
        syncTokenToLocalStorage(token);
        set({
          user,
          token,
          isAuthenticated: true,
          isLoading: false,
          error: null,
          isInitialized: true,
        });
      },

      clearAuth: () => {
        syncTokenToLocalStorage(null);
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
          isInitialized: true,
        });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error, isLoading: false });
      },

      initializeAuth: () => {
        const state = get();
        if (state.token && state.user) {
          // Sync existing persisted token to localStorage
          syncTokenToLocalStorage(state.token);
          set({
            isLoading: false,
            isInitialized: true,
            isAuthenticated: true,
          });
        } else {
          set({
            isLoading: false,
            isInitialized: true,
            isAuthenticated: false,
          });
        }
      },

      // Helper methods
      hasRole: (role: UserRole) => {
        const { user } = get();
        return user?.role === role;
      },

      isAdmin: () => {
        const { user } = get();
        return (
          user?.role === UserRole.ADMIN || user?.role === UserRole.SUPER_ADMIN
        );
      },

      isCustomer: () => {
        const { user } = get();
        return user?.role === UserRole.CUSTOMER;
      },
    }),
    {
      name: 'queen-auth-store',
      partialize: state => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
