'use client';

import React, { useState, useEffect } from 'react';
import { Clock, AlertTriangle, X } from 'lucide-react';
import { availabilityService } from '../../services/availability.service';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Modal, ModalHeader, ModalBody, ModalFooter } from '../ui/modal';

interface QuickBlockModalProps {
  isOpen: boolean;
  onClose: () => void;
  roomId: string;
  date: string;
  startTime: string;
  endTime: string;
  onSuccess?: () => void;
}

const BLOCK_REASONS = [
  { value: 'maintenance', label: 'Bảo trì phòng' },
  { value: 'cleaning', label: 'Vệ sinh phòng' },
  { value: 'private_event', label: 'Sự kiện riêng' },
  { value: 'walk_in', label: 'Khách vãng lai' },
  { value: 'staff_break', label: 'Nghỉ giải lao' },
  { value: 'technical_issue', label: 'Sự cố kỹ thuật' },
  { value: 'other', label: 'Lý do khác' },
];

const BLOCK_DURATIONS = [
  { value: 'single', label: 'Chỉ khung giờ này (30 phút)' },
  { value: 'extended', label: 'Mở rộng thêm 2 giờ' },
  { value: 'custom', label: 'Tùy chỉnh thời gian kết thúc' },
];

// Generate time options for custom end time (30-minute intervals from start time)
const generateEndTimeOptions = (startTime: string): string[] => {
  const options = [];
  const start = new Date(`2024-01-01T${startTime}:00`);

  let current = new Date(start);
  current.setMinutes(current.getMinutes() + 30); // Start from 30 minutes after start time

  // Generate options up to 23:30 with 30-minute intervals
  while (current.getHours() < 24) {
    const timeStr = current.toTimeString().slice(0, 5);
    options.push(timeStr);
    current.setMinutes(current.getMinutes() + 30);

    // Prevent infinite loop
    if (options.length > 47) break; // Max 24 hours worth of 30-min slots
  }

  return options;
};

export const QuickBlockModal: React.FC<QuickBlockModalProps> = ({
  isOpen,
  onClose,
  roomId,
  date,
  startTime,
  endTime,
  onSuccess,
}) => {
  const [reason, setReason] = useState<string>('walk_in');
  const [customReason, setCustomReason] = useState<string>('');
  const [blockDuration, setBlockDuration] = useState<string>('single'); // 'single', 'extended', 'custom'
  const [customEndTime, setCustomEndTime] = useState<string>(endTime);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [endTimeOptions, setEndTimeOptions] = useState<string[]>([]);

  // Generate end time options when modal opens
  useEffect(() => {
    if (isOpen) {
      const options = generateEndTimeOptions(startTime);
      setEndTimeOptions(options);
      setCustomEndTime(endTime);
    }
  }, [isOpen, startTime, endTime]);

  // Helper functions to get current display labels
  const getCurrentBlockDurationLabel = (): string => {
    const duration = BLOCK_DURATIONS.find(d => d.value === blockDuration);
    return duration ? duration.label : 'Chọn thời lượng chặn';
  };

  const getCurrentReasonLabel = (): string => {
    const reasonOption = BLOCK_REASONS.find(r => r.value === reason);
    return reasonOption ? reasonOption.label : 'Chọn lý do chặn';
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const finalReason =
        reason === 'other'
          ? customReason
          : BLOCK_REASONS.find(r => r.value === reason)?.label ||
            'Chặn bởi nhân viên';

      let finalEndTime = endTime;
      if (blockDuration === 'extended') {
        // Extend by 2 hours
        const endTimeObj = new Date(`${date}T${endTime}:00`);
        endTimeObj.setHours(endTimeObj.getHours() + 2);
        finalEndTime = endTimeObj.toTimeString().slice(0, 5);
      } else if (blockDuration === 'custom') {
        finalEndTime = customEndTime;
      }

      await availabilityService.quickBlockTimeSlot(
        roomId,
        date,
        startTime,
        finalEndTime,
        finalReason
      );

      if (onSuccess) {
        onSuccess();
      }

      onClose();
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Lỗi khi chặn khung giờ';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setReason('walk_in');
      setCustomReason('');
      setBlockDuration('single');
      setCustomEndTime(endTime);
      setError(null);
      onClose();
    }
  };

  const formatDate = (dateStr: string): string => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('vi-VN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getBlockDurationLabel = (): string => {
    switch (blockDuration) {
      case 'single':
        return `${startTime} - ${endTime} (30 phút)`;
      case 'extended':
        const endTimeObj = new Date(`${date}T${endTime}:00`);
        endTimeObj.setHours(endTimeObj.getHours() + 2);
        const extendedEndTime = endTimeObj.toTimeString().slice(0, 5);
        return `${startTime} - ${extendedEndTime} (2.5 giờ)`;
      case 'custom':
        return `${startTime} - ${customEndTime}`;
      default:
        return '';
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="md">
      <ModalHeader>
        <div className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-orange-500" />
          <h2 className="text-h5 font-semibold text-gray-900">
            Chặn Khung Giờ
          </h2>
        </div>
        <p className="text-sm text-gray-600 mt-2">
          Chặn khung giờ này để ngăn không cho khách hàng đặt phòng.
        </p>
      </ModalHeader>

      <ModalBody>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Time Slot Info */}
          <div className="bg-gray-50 p-4 rounded-md">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="font-medium text-gray-900">
                Thông tin khung giờ
              </span>
            </div>
            <div className="text-sm text-gray-600">
              <p>
                <strong>Ngày:</strong> {formatDate(date)}
              </p>
              <p>
                <strong>Thời gian:</strong> {getBlockDurationLabel()}
              </p>
            </div>
          </div>

          {/* Block Duration */}
          <div className="space-y-2">
            <Label htmlFor="block-duration">Thời lượng chặn</Label>
            <Select value={blockDuration} onValueChange={setBlockDuration}>
              <SelectTrigger>
                <span className="text-black">
                  {getCurrentBlockDurationLabel()}
                </span>
              </SelectTrigger>
              <SelectContent>
                {BLOCK_DURATIONS.map(duration => (
                  <SelectItem key={duration.value} value={duration.value}>
                    {duration.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Custom End Time */}
          {blockDuration === 'custom' && (
            <div className="space-y-2">
              <Label htmlFor="custom-end-time">Thời gian kết thúc</Label>
              <div className="relative">
                <Select value={customEndTime} onValueChange={setCustomEndTime}>
                  <SelectTrigger className="pl-10">
                    <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <SelectValue placeholder="Chọn thời gian kết thúc" />
                  </SelectTrigger>
                  <SelectContent>
                    {endTimeOptions.length === 0 ? (
                      <div className="py-2 px-4 text-sm text-gray-500">
                        Không có tùy chọn thời gian khả dụng
                      </div>
                    ) : (
                      endTimeOptions.map(time => (
                        <SelectItem key={time} value={time}>
                          <span className="text-black">{time}</span>
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              <p className="text-xs text-gray-500">
                Khung giờ từ {startTime} đến {customEndTime} sẽ bị chặn.
              </p>
            </div>
          )}

          {/* Block Reason */}
          <div className="space-y-2">
            <Label htmlFor="block-reason">Lý do chặn</Label>
            <Select value={reason} onValueChange={setReason}>
              <SelectTrigger>
                <span className="text-black">{getCurrentReasonLabel()}</span>
              </SelectTrigger>
              <SelectContent>
                {BLOCK_REASONS.map(reasonOption => (
                  <SelectItem
                    key={reasonOption.value}
                    value={reasonOption.value}
                  >
                    {reasonOption.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Custom Reason */}
          {reason === 'other' && (
            <div className="space-y-2">
              <Label htmlFor="custom-reason">Lý do cụ thể</Label>
              <Input
                id="custom-reason"
                type="text"
                value={customReason}
                onChange={e => setCustomReason(e.target.value)}
                placeholder="Nhập lý do cụ thể..."
                className="text-black"
                required
              />
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}
        </form>
      </ModalBody>

      <ModalFooter>
        <Button
          type="button"
          variant="outline"
          onClick={handleClose}
          disabled={loading}
        >
          Hủy
        </Button>
        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={loading || (reason === 'other' && !customReason.trim())}
          className="bg-orange-500 hover:bg-orange-600 text-white"
        >
          {loading ? 'Đang chặn...' : 'Chặn khung giờ'}
        </Button>
      </ModalFooter>
    </Modal>
  );
};
