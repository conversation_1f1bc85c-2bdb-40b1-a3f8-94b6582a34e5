<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON><PERSON> nhận đặt phòng</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Roboto+Condensed:wght@400;500;600;700&display=swap');
    
    /* Reset and Base Styles */
    body {
      margin: 0;
      padding: 0;
      background-color: #f9f6f0; /* gold-50 */
      font-family: 'Roboto Condensed', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #4b5563; /* gray-600 */
    }

    /* Container */
    .container {
      max-width: 600px;
      margin: 40px auto;
      background: linear-gradient(to bottom, #ffffff, #f9f6f0);
      border: 1px solid #e7dbc3; /* gold-200 */
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(152, 121, 71, 0.15); /* gold-600 with opacity */
    }

    /* Header */
    .header {
      background: linear-gradient(135deg, #4e3921 0%, #674e2d 100%); /* gold-900 to gold-800 */
      color: #ab8d59; /* queens-gold */
      padding: 40px 20px;
      text-align: center;
      border-bottom: 3px solid #ab8d59; /* queens-gold */
    }

    .header h1 {
      margin: 0;
      font-size: 2.25rem; /* text-h2 */
      font-weight: 700;
      letter-spacing: 1.5px;
      text-transform: uppercase;
      color: #d4af37; /* Brighter gold for contrast */
      text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    /* Content */
    .content {
      padding: 40px 30px;
      color: #4b5563; /* gray-600 */
    }

    .content h2 {
      color: #4e3921; /* gold-900 */
      font-size: 1.5rem; /* text-h4 */
      font-weight: 600;
      margin: 0 0 25px 0;
    }

    /* Booking Details */
    .booking-details {
      background: #ffffff;
      border: 1px solid #dbc9a5; /* gold-300 */
      border-radius: 8px;
      padding: 25px;
      margin: 25px 0;
      box-shadow: 0 2px 10px rgba(219, 201, 165, 0.2); /* gold-300 with opacity */
    }

    .booking-details table {
      width: 100%;
      border-collapse: collapse;
    }

    .booking-details th {
      text-align: left;
      color: #806439; /* gold-700 */
      font-weight: 500;
      padding: 12px 0;
      border-bottom: 1px solid #e7dbc3; /* gold-200 */
      font-size: 0.875rem; /* text-sm */
    }

    .booking-details td {
      padding: 12px 0;
      color: #1f2937; /* gray-800 */
      font-size: 1rem; /* text-base */
    }

    .booking-details strong {
      color: #674e2d; /* gold-800 */
      font-weight: 600;
    }

    /* Price Row */
    .price-row td {
      font-size: 1.25rem; /* text-h5 */
      color: #4e3921; /* gold-900 */
      font-weight: 700;
      padding-top: 20px;
    }

    /* Button */
    .button-container {
      text-align: center;
      margin: 35px 0;
    }

    .button {
      background: linear-gradient(135deg, #ab8d59 0%, #987947 100%); /* queens-gold to gold-600 */
      color: #ffffff;
      padding: 16px 32px;
      text-decoration: none;
      border-radius: 8px;
      font-weight: 600;
      font-size: 1rem;
      display: inline-block;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      box-shadow: 0 2px 8px rgba(152, 121, 71, 0.3); /* gold-600 with opacity */
    }

    /* Contact Info */
    .contact-info {
      background: #fdfbf6; /* Lighter than gold-50 */
      border-radius: 8px;
      padding: 20px;
      margin: 25px 0;
      border: 1px solid #e7dbc3; /* gold-200 */
    }

    .contact-info p {
      margin: 10px 0;
    }

    .contact-info strong {
      color: #674e2d; /* gold-800 */
    }

    /* Footer */
    .footer {
      background: linear-gradient(135deg, #1f2937 0%, #374151 100%); /* gray-800 to gray-700 */
      color: #bda476; /* gold-400 */
      text-align: center;
      padding: 30px 20px;
      font-size: 0.875rem; /* text-sm */
    }

    .footer p {
      margin: 5px 0;
    }

    .footer a {
      color: #ab8d59; /* queens-gold */
      text-decoration: none;
      font-weight: 500;
    }

    .logo {
      max-height: 60px;
      margin-bottom: 15px;
    }

    /* Responsive Design */
    @media only screen and (max-width: 480px) {
      .container {
        margin: 20px auto;
        border-radius: 8px;
      }

      .header h1 {
        font-size: 1.875rem;
      }

      .content {
        padding: 30px 20px;
      }

      .booking-details {
        padding: 20px 15px;
      }

      .button {
        padding: 14px 28px;
        font-size: 0.875rem;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <!-- Optional: <img src="URL_TO_YOUR_LOGO_HOSTED_ONLINE" alt="Queen Booking System Logo" class="logo"> -->
      <h1>Đặt phòng thành công!</h1>
    </div>
    <div class="content">
      <h2>Chào {{userName}},</h2>
      <p>Cảm ơn bạn đã đặt phòng tại Queen Booking System. Chúng tôi rất vui được phục vụ bạn.</p>
      <p>Dưới đây là thông tin chi tiết về đặt phòng của bạn:</p>

      <div class="booking-details">
        <table>
          <tr>
            <th style="width:150px;">Mã đặt phòng:</th>
            <td><strong>{{bookingReference}}</strong></td>
          </tr>
          <tr>
            <th>Địa điểm:</th>
            <td>{{locationName}}</td>
          </tr>
          <tr>
            <th>Phòng:</th>
            <td>{{roomName}}</td>
          </tr>
          <tr>
            <th>Thời gian bắt đầu:</th>
            <td>{{startTime}}</td>
          </tr>
          <tr>
            <th>Thời gian kết thúc:</th>
            <td>{{endTime}}</td>
          </tr>
          <tr class="price-row">
            <th>Tổng cộng:</th>
            <td><strong>{{totalPrice}}</strong></td>
          </tr>
        </table>
      </div>

      <div class="contact-info">
        <p><strong>Địa chỉ:</strong> {{locationAddress}}</p>
        <p><strong>Điện thoại liên hệ:</strong> {{locationPhoneNumber}}</p>
      </div>

      <% if (bookingDetailsLink) { %>
      <div class="button-container">
        <a href="<%- bookingDetailsLink %>" class="button">Xem chi tiết đặt phòng</a>
      </div>
      <% } %>

      <p style="margin-top:30px;">Chúng tôi mong được đón tiếp bạn!</p>
      <p>Trân trọng,<br>Đội ngũ Queen Booking System</p>
    </div>
    <div class="footer">
      <p>&copy; {{currentYear}} Queen Booking System. Mọi quyền được bảo lưu.</p>
      <p><a href="YOUR_WEBSITE_URL">Website</a> | <a href="YOUR_CONTACT_EMAIL">Liên hệ</a></p>
    </div>
  </div>
</body>
</html> 