'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { MapPin, Clock, Users } from 'lucide-react';
import type { Location, LocalizedString, OperatingHours } from 'shared-types';

interface LocationWithRoomCount extends Location {
  roomCount: number;
}

interface LocationSelectionProps {
  locations: LocationWithRoomCount[];
  loading: boolean;
  onLocationSelect: (location: LocationWithRoomCount) => void;
}

// Animation variants
const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08,
      delayChildren: 0.3,
    },
  },
};

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

const shimmerAnimation = {
  initial: {
    backgroundPosition: '200% 0',
    opacity: 0.4,
  },
  animate: {
    backgroundPosition: ['-200% 0', '200% 0'],
    opacity: [0.4, 0.8, 0.4],
    transition: {
      duration: 4,
      ease: 'linear',
      repeat: Infinity,
      opacity: {
        duration: 2,
        ease: 'easeInOut',
        repeat: Infinity,
      },
    },
  },
};

const locationCardVariants = {
  initial: {
    opacity: 0,
    y: 20,
  },
  rest: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
  hover: {
    opacity: 1,
    y: 0,
    scale: 1.02,
    transition: {
      duration: 0.15,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

export function LocationSelection({
  locations,
  loading,
  onLocationSelect,
}: LocationSelectionProps) {
  return (
    <>
      {/* Location Grid */}
      <motion.div
        key="locations-grid"
        className="grid gap-8 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 max-w-7xl mx-auto"
        variants={staggerContainer}
        initial="hidden"
        animate="visible"
        exit={{
          opacity: 0,
          x: '-100%',
          transition: {
            duration: 0.4,
            ease: [0.25, 0.46, 0.45, 0.94],
            when: 'afterChildren',
            staggerChildren: 0.05,
            staggerDirection: -1,
          },
        }}
      >
        {loading
          ? // Loading Skeleton
            Array.from({ length: 3 }).map((_, i) => (
              <motion.div
                key={i}
                className="rounded-xl h-[420px] bg-gradient-to-r from-gold-50 via-gold-100/50 to-gold-50 queens-treatment"
                animate={{
                  backgroundPosition: ['200% 0', '-200% 0'],
                }}
                transition={{
                  duration: 2,
                  ease: 'linear',
                  repeat: Infinity,
                }}
              />
            ))
          : locations.map(location => (
              <motion.div
                key={location.id}
                variants={locationCardVariants}
                initial="initial"
                animate="rest"
                whileHover="hover"
                className="rounded-xl overflow-hidden backdrop-blur-md relative group h-[350px] sm:h-[400px] md:h-[450px] ring-1 ring-gold-200/30 shadow-[0_0_15px_-3px_rgba(234,179,8,0.2)] cursor-pointer"
                onClick={() => onLocationSelect(location)}
                style={{
                  background: 'transparent',
                }}
              >
                {/* Location Image with Overlay */}
                <div className="relative h-full w-full overflow-hidden">
                  {location.imageUrl ? (
                    <motion.img
                      src={location.imageUrl}
                      alt={location.name.vi}
                      className="w-full h-full object-cover"
                      whileHover={{
                        scale: 1.05,
                        transition: { duration: 0.15 },
                      }}
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-gold-100 to-gold-50 flex items-center justify-center">
                      <MapPin className="w-12 h-12 text-gold-400" />
                    </div>
                  )}

                  {/* Content Container with Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent">
                    <div className="absolute bottom-0 left-0 right-0 p-6 text-white space-y-4">
                      <h3 className="text-xl md:text-2xl font-semibold drop-shadow-lg mb-3">
                        {(location.name as LocalizedString).vi}
                      </h3>

                      <div className="space-y-3 text-white/90">
                        <motion.div
                          className="flex items-center gap-3"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 }}
                        >
                          <MapPin className="w-5 h-5 text-gold-300 shrink-0" />
                          <span className="text-sm sm:text-base font-light line-clamp-2">
                            {location.address}
                          </span>
                        </motion.div>
                        <motion.div
                          className="flex items-center gap-3"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.2 }}
                        >
                          <Clock className="w-5 h-5 text-gold-300 shrink-0" />
                          <span className="text-sm sm:text-base font-light">
                            {location.operatingHours
                              ? (location.operatingHours as OperatingHours)
                                  .monday || '09:00 - 23:00'
                              : '09:00 - 23:00'}
                          </span>
                        </motion.div>
                        <motion.div
                          className="flex items-center gap-3"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.3 }}
                        >
                          <Users className="w-5 h-5 text-gold-300 shrink-0" />
                          <span className="text-sm sm:text-base font-light">
                            {location.roomCount || 0} phòng
                          </span>
                        </motion.div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
      </motion.div>
    </>
  );
}
