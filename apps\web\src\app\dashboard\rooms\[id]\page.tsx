'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Building,
  Users,
  DollarSign,
  Tag,
  Info,
  Image as ImageIcon,
  ShieldCheck,
  ShieldOff,
} from 'lucide-react';
import { roomsService } from '../../../../services/rooms.service';
import { locationsService } from '../../../../services/locations.service';
import { Room, Location, RoomTypeEnum, LocalizedString } from 'shared-types';
import { Button } from '../../../../components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '../../../../components/ui/card';
import { useDialogs } from '../../../../components/ui/modal-provider';
import {
  AvailabilityCalendar,
  QuickBlockModal,
} from '../../../../components/availability';
import { TimeSlot } from '../../../../services/availability.service';

interface RoomWithLocationInfo extends Room {
  locationInfo?: {
    id: string;
    name: LocalizedString | string;
  };
}

export default function RoomDetailPage() {
  const router = useRouter();
  const params = useParams();
  const roomId = params.id as string;
  const { alert, confirmDelete } = useDialogs();

  const [room, setRoom] = useState<RoomWithLocationInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Availability management state
  const [showQuickBlockModal, setShowQuickBlockModal] = useState(false);
  const [selectedSlot, setSelectedSlot] = useState<{
    slot: TimeSlot;
    date: string;
  } | null>(null);

  // Key to force re-render AvailabilityCalendar for refresh
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    if (!roomId) return;

    const fetchRoomDetails = async () => {
      setLoading(true);
      setError(null);
      try {
        const locationsResponse = await locationsService.getLocations({
          page: 1,
          limit: 100,
        });
        let foundRoom: RoomWithLocationInfo | null = null;

        for (const loc of locationsResponse.items) {
          try {
            const roomsInLocation = await roomsService.getRooms(loc.id, {});
            const targetRoom = roomsInLocation.find(r => r.id === roomId);
            if (targetRoom) {
              foundRoom = {
                ...targetRoom,
                locationInfo: {
                  id: loc.id,
                  name: loc.name as LocalizedString | string,
                },
              };
              break;
            }
          } catch (err) {
            console.warn(`Error fetching rooms for location ${loc.id}:`, err);
          }
        }

        if (foundRoom) {
          setRoom(foundRoom);
        } else {
          setError('Không tìm thấy thông tin phòng.');
        }
      } catch (err) {
        console.error('Error fetching room details:', err);
        setError(
          err instanceof Error
            ? err.message
            : 'Lỗi không xác định khi tải thông tin phòng.'
        );
      } finally {
        setLoading(false);
      }
    };

    fetchRoomDetails();
  }, [roomId]);

  const handleDeleteRoom = async () => {
    if (!room || !room.locationInfo?.id) return;

    const confirmed = await confirmDelete(
      `Bạn có chắc chắn muốn xóa phòng "${roomsService.getRoomDisplayName(room)}"?\n\nHành động này không thể hoàn tác.`,
      'Xác nhận xóa phòng'
    );

    if (!confirmed) return;

    try {
      await roomsService.deleteRoom(room.locationInfo.id, room.id);
      await alert('Đã xóa phòng thành công.', 'Thành công', 'success');
      router.push('/dashboard/rooms');
    } catch (err) {
      console.error('Error deleting room:', err);
      await alert('Có lỗi xảy ra khi xóa phòng.', 'Lỗi', 'error');
    }
  };

  // Availability management handlers
  const handleSlotClick = (slot: TimeSlot, date: string) => {
    if (slot.isAvailable) {
      // Open quick block modal for available slots
      setSelectedSlot({ slot, date });
      setShowQuickBlockModal(true);
    } else if (slot.reason === 'booked') {
      // Show booking details for booked slots
      const statusMessage = 'Khung giờ này đã được đặt bởi khách hàng.';

      alert(
        `Khung giờ: ${slot.startTime} - ${slot.endTime}\nTrạng thái: ${statusMessage}`,
        'Thông tin khung giờ',
        'info'
      );
    }
    // Note: Blocked slots are now handled internally by AvailabilityCalendar
  };

  const handleQuickBlock = async (
    date: string,
    startTime: string,
    endTime: string
  ) => {
    await alert(
      `Đã chặn thành công khung giờ ${startTime} - ${endTime} vào ngày ${new Date(date).toLocaleDateString('vi-VN')}.`,
      'Chặn thành công',
      'success'
    );
    // Trigger refresh by re-mounting the AvailabilityCalendar component
    setRefreshKey(prev => prev + 1);
  };

  const handleUnblock = async (
    date: string,
    startTime: string,
    endTime: string
  ) => {
    await alert(
      `Đã bỏ chặn thành công khung giờ ${startTime} - ${endTime} vào ngày ${new Date(date).toLocaleDateString('vi-VN')}.`,
      'Bỏ chặn thành công',
      'success'
    );
    // Trigger refresh by re-mounting the AvailabilityCalendar component
    setRefreshKey(prev => prev + 1);
  };

  const handleQuickBlockModalClose = () => {
    setShowQuickBlockModal(false);
    setSelectedSlot(null);
  };

  const handleQuickBlockSuccess = async () => {
    if (selectedSlot) {
      await handleQuickBlock(
        selectedSlot.date,
        selectedSlot.slot.startTime,
        selectedSlot.slot.endTime
      );
    }
  };

  // Get location operating hours for availability calendar
  const getLocationOperatingHours = () => {
    // Try to get operating hours from the location data
    // This would come from the location API response
    return undefined; // For now, let the calendar use default hours
  };

  const getLocationDisplayName = (
    locationName: LocalizedString | string | undefined
  ): string => {
    if (!locationName) return 'N/A';
    if (typeof locationName === 'string') return locationName;
    return locationName?.vi || locationName?.en || 'Chi nhánh không tên';
  };

  const getRoomTypeClasses = (roomType?: RoomTypeEnum): string => {
    const baseClasses =
      'px-3 py-1 rounded-full text-sm font-medium inline-block';
    switch (roomType) {
      case RoomTypeEnum.QUEENS_EYES:
        return `${baseClasses} bg-queens-50 text-queens-primary border border-queens-200`;
      case RoomTypeEnum.RUBY:
        return `${baseClasses} bg-ruby-50 text-ruby-primary border border-ruby-200`;
      case RoomTypeEnum.SAPPHIRE:
        return `${baseClasses} bg-sapphire-50 text-sapphire-primary border border-sapphire-200`;
      case RoomTypeEnum.OPAL:
        return `${baseClasses} bg-opal-50 text-opal-primary border border-opal-200`;
      case RoomTypeEnum.PEARL:
        return `${baseClasses} bg-pearl-50 text-pearl-primary border border-pearl-200`;
      case RoomTypeEnum.HALL:
        return `${baseClasses} bg-hall-50 text-hall-primary border border-hall-200`;
      case RoomTypeEnum.STANDARD:
      default:
        return `${baseClasses} bg-standard-50 text-standard-primary border border-standard-200`;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-queens-gold"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6 p-4 md:p-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/rooms')}
            className="p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-h1 font-bold text-gray-900">Lỗi</h1>
        </div>
        <Card className="border-red-500 bg-red-50">
          <CardContent className="p-6 text-center">
            <Info className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-700 text-lg">{error}</p>
            <Button
              onClick={() => router.push('/dashboard/rooms')}
              className="mt-6 bg-queens-gold hover:bg-queens-gold/90 text-white font-medium shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
            >
              Quay Lại Danh Sách Phòng
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!room) {
    return (
      <div className="space-y-6 p-4 md:p-6 text-center">
        <Info className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600 mb-4">
          Không tìm thấy thông tin chi tiết cho phòng này.
        </p>
        <Button
          onClick={() => router.push('/dashboard/rooms')}
          className="bg-queens-gold hover:bg-queens-gold/90 text-white font-medium shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
        >
          Quay Lại Danh Sách Phòng
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-4 md:p-6">
      {/* Header with Back Button, Title, and Actions */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/rooms')}
            className="p-2 h-9 w-9 flex-shrink-0"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="min-w-0">
            <h1
              className="text-h2 font-bold text-gray-900 truncate"
              title={roomsService.getRoomDisplayName(room)}
            >
              {roomsService.getRoomDisplayName(room)}
            </h1>
            <p className="text-base text-gray-600 truncate">
              Chi tiết phòng tại{' '}
              {getLocationDisplayName(room.locationInfo?.name)}
            </p>
          </div>
        </div>
        <div className="flex gap-2 flex-shrink-0">
          <Button
            onClick={() => router.push(`/dashboard/rooms/${room.id}/edit`)}
            variant="outline"
            className="gap-2 bg-queens-gold hover:bg-queens-gold/90 text-white font-medium shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
          >
            <Edit className="h-4 w-4" />
            Chỉnh Sửa
          </Button>
          <Button onClick={handleDeleteRoom} variant="delete" className="gap-2">
            <Trash2 className="h-4 w-4" />
            Xóa Phòng
          </Button>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column: Main Details */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-h5 text-gray-900">
                <Info className="h-5 w-5 text-queens-gold" />
                Thông Tin Cơ Bản
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">
                    Tên phòng (VI)
                  </label>
                  <p className="text-base text-gray-900 font-semibold">
                    {room.name?.vi || 'N/A'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">
                    Tên phòng (EN)
                  </label>
                  <p className="text-base text-gray-900 font-semibold">
                    {room.name?.en || 'N/A'}
                  </p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">
                  Chi nhánh
                </label>
                <p className="text-base text-gray-900 font-semibold flex items-center gap-2">
                  <Building className="h-4 w-4 text-gray-500 flex-shrink-0" />
                  {getLocationDisplayName(room.locationInfo?.name)}
                </p>
              </div>

              {room.roomType && (
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">
                    Loại phòng
                  </label>
                  <div className={getRoomTypeClasses(room.roomType)}>
                    {roomsService.getRoomTypeDisplayName(room.roomType)}
                  </div>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">
                  Phong cách trang trí
                </label>
                <div className="px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 inline-block">
                  {roomsService.getDecorStyleDisplayName(room.decorStyle)}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-h5 text-gray-900">
                <Users className="h-5 w-5 text-queens-gold" />
                Sức Chứa & Giá
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">
                  Sức chứa tối đa
                </label>
                <p className="text-lg text-gray-900 font-semibold">
                  {room.capacity} người
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">
                  Giá mỗi giờ
                </label>
                <p className="text-lg text-queens-gold font-bold">
                  {roomsService.formatPrice(room.pricePerHour)}
                </p>
              </div>
            </CardContent>
          </Card>

          {(room.description?.vi || room.description?.en) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-h5 text-gray-900">
                  <Info className="h-5 w-5 text-queens-gold" />
                  Mô Tả Phòng
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {room.description?.vi && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">
                      Mô tả (VI)
                    </label>
                    <p className="text-base text-gray-700 whitespace-pre-line">
                      {room.description.vi}
                    </p>
                  </div>
                )}
                {room.description?.en && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <label className="block text-sm font-medium text-gray-500 mb-1">
                      Mô tả (EN)
                    </label>
                    <p className="text-base text-gray-700 whitespace-pre-line">
                      {room.description.en}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column: Status, Images, Amenities */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-h5 text-gray-900">
                {room.isActive ? (
                  <ShieldCheck className="h-5 w-5 text-green-600" />
                ) : (
                  <ShieldOff className="h-5 w-5 text-red-600" />
                )}
                Trạng Thái Hoạt Động
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div
                className={`inline-block px-3 py-1 text-sm font-medium rounded-full ${room.isActive ? 'bg-green-100 text-green-800 border border-green-300' : 'bg-red-100 text-red-800 border border-red-300'}`}
              >
                {room.isActive ? 'Đang hoạt động' : 'Ngừng hoạt động'}
              </div>
              <p className="text-xs text-gray-500 mt-2">
                {room.isActive
                  ? 'Phòng này sẵn sàng cho đặt phòng.'
                  : 'Phòng này hiện không hoạt động và không thể đặt.'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-h5 text-gray-900">
                <ImageIcon className="h-5 w-5 text-queens-gold" />
                Hình Ảnh ({room.images?.length || 0})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {room.images && room.images.length > 0 ? (
                <div className="grid grid-cols-2 gap-2">
                  {room.images.slice(0, 4).map((img, index) => (
                    <div
                      key={index}
                      className="aspect-square bg-gray-100 rounded-md overflow-hidden border border-gray-200"
                    >
                      <img
                        src={img}
                        alt={`Room image ${index + 1}`}
                        className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                        onError={(
                          e: React.SyntheticEvent<HTMLImageElement, Event>
                        ) => {
                          const target = e.target as HTMLImageElement;
                          target.onerror = null; // prevent infinite loop if placeholder also fails
                          target.src =
                            'https://via.placeholder.com/150/F3EDE1/AB8D59?text=Queen+Karaoke'; // Gold-100 bg, Gold-500 text
                        }}
                      />
                    </div>
                  ))}
                  {room.images.length > 4 && (
                    <div className="col-span-2 text-center text-sm text-gray-500 pt-2">
                      + {room.images.length - 4} hình ảnh khác
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-4">
                  <ImageIcon className="h-10 w-10 text-gray-300 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">
                    Chưa có hình ảnh nào cho phòng này.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {room.amenities && room.amenities.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-h5 text-gray-900">
                  <Tag className="h-5 w-5 text-queens-gold" />
                  Tiện Nghi
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {room.amenities.map((amenity, index) => (
                    <li
                      key={index}
                      className="text-sm text-gray-700 flex items-center gap-2"
                    >
                      <span className="bg-gold-100 text-gold-700 p-1 rounded-full">
                        <Tag className="h-3 w-3" />
                      </span>
                      {typeof amenity === 'string'
                        ? amenity
                        : amenity.vi || amenity.en || 'N/A'}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Availability Management Section */}
      {room.isActive && (
        <div className="mt-8">
          <AvailabilityCalendar
            key={refreshKey}
            roomId={room.id}
            locationOperatingHours={getLocationOperatingHours()}
            onSlotClick={handleSlotClick}
            onQuickBlock={handleQuickBlock}
            onUnblock={handleUnblock}
          />
        </div>
      )}

      {/* Quick Block Modal */}
      {showQuickBlockModal && selectedSlot && (
        <QuickBlockModal
          isOpen={showQuickBlockModal}
          onClose={handleQuickBlockModalClose}
          roomId={room.id}
          date={selectedSlot.date}
          startTime={selectedSlot.slot.startTime}
          endTime={selectedSlot.slot.endTime}
          onSuccess={handleQuickBlockSuccess}
        />
      )}
    </div>
  );
}
