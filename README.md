# Queen Karaoke Booking System

A modern, production-ready monorepo for managing karaoke room bookings across multiple locations with bilingual support (Vietnamese/English).

## 🏗️ Architecture

This is a **npm workspace-based monorepo** containing:

- **`apps/api`** - NestJS Backend API with PostgreSQL & Prisma
- **`apps/web`** - Next.js Frontend with TypeScript & Tailwind CSS
- **`packages/shared-types`** - Shared TypeScript types and interfaces

## 🚀 Tech Stack

### Backend (`apps/api`)

- **NestJS** - Node.js framework
- **TypeScript** - Type safety
- **Prisma** - Database ORM
- **PostgreSQL** - Primary database
- **Redis** - Session store & caching
- **Passport.js** - Authentication
- **PayOS.vn** - Payment gateway

### Frontend (`apps/web`)

- **Next.js 15** - React framework with App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **Radix UI** - Accessible components
- **Zustand** - State management
- **next-international** - Internationalization

### Shared (`packages/shared-types`)

- **TypeScript** - Shared types, interfaces, and DTOs
- Common enums and API response types

## 📁 Project Structure

```
queen-booking-system/
├── apps/
│   ├── api/                    # NestJS Backend
│   │   ├── src/
│   │   ├── prisma/
│   │   ├── test/
│   │   └── env.example
│   └── web/                    # Next.js Frontend
│       ├── src/
│       │   └── app/           # App Router pages
│       ├── public/
│       └── env.example
├── packages/
│   └── shared-types/          # Shared TypeScript types
│       ├── src/
│       └── dist/              # Built output
├── cursor-md/                 # Project documentation
└── docker-compose.yml         # Development environment
```

## 🛠️ Development Setup

### Prerequisites

- **Node.js** 20+ and npm
- **PostgreSQL** 14+
- **Redis** 6+
- **Docker & Docker Compose** (optional, for easy setup)

### Quick Start

1. **Clone and install dependencies:**

   ```bash
   git clone <repository-url>
   cd queen-booking-system
   npm install
   ```

2. **Set up environment variables:**

   ```bash
   # Copy example files
   cp apps/api/env.example apps/api/.env
   cp apps/web/env.example apps/web/.env

   # Edit the .env files with your configuration
   ```

3. **Set up the database:**

   ```bash
   # Start PostgreSQL and Redis (if using Docker)
   docker-compose up -d postgres redis

   # Run database migrations
   npm run prisma:migrate:dev

   # Generate Prisma client
   npm run prisma:generate
   ```

4. **Build shared types:**

   ```bash
   npm run build:shared
   ```

5. **Start development servers:**

   ```bash
   # Start both API and Web in parallel
   npm run dev

   # Or start individually
   npm run dev-api   # API on http://localhost:3001
   npm run dev-web   # Web on http://localhost:3000
   ```

### Available Scripts

| Script                       | Description                        |
| ---------------------------- | ---------------------------------- |
| `npm run dev`                | Start both API and Web in parallel |
| `npm run dev-api`            | Start only the API server          |
| `npm run dev-web`            | Start only the Web server          |
| `npm run build-all`          | Build all apps and packages        |
| `npm run build:shared`       | Build shared-types package         |
| `npm run build:api`          | Build API application              |
| `npm run build:web`          | Build Web application              |
| `npm run lint`               | Lint all workspaces                |
| `npm run format`             | Format code with Prettier          |
| `npm run prisma:generate`    | Generate Prisma client             |
| `npm run prisma:migrate:dev` | Run database migrations            |
| `npm run prisma:studio`      | Open Prisma Studio                 |

## 🐳 Docker Development

For a complete development environment with PostgreSQL and Redis:

```bash
# Start all services
npm run start-all

# Stop all services
npm run stop-all
```

## 🌐 Environment Configuration

### API Environment (`apps/api/.env`)

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/queendb?schema=public"

# JWT Secrets
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_REFRESH_SECRET="your-super-secret-refresh-jwt-key-here"

# Redis
REDIS_HOST="localhost"
REDIS_PORT=6379

# PayOS Payment Gateway
PAYOS_CLIENT_ID="your-payos-client-id"
PAYOS_API_KEY="your-payos-api-key"
PAYOS_CHECKSUM_KEY="your-payos-checksum-key"

# Application
NODE_ENV="development"
PORT=3001
FRONTEND_URL="http://localhost:3000"
```

### Web Environment (`apps/web/.env`)

```env
# API Configuration
NEXT_PUBLIC_API_BASE_URL="http://localhost:3001/api"

# Application
NEXT_PUBLIC_APP_NAME="Queen Karaoke"
NODE_ENV="development"

# PayOS (Public keys only)
NEXT_PUBLIC_PAYOS_CLIENT_ID="your-payos-client-id"

# Feature Flags
NEXT_PUBLIC_ENABLE_GUEST_BOOKING=true
```

## 🎯 Key Features

- **Multi-location Management** - Support for multiple karaoke venues
- **Room Booking System** - Real-time availability and booking management
- **Guest & User Bookings** - Support for both registered users and walk-in guests
- **Payment Integration** - PayOS.vn payment gateway integration
- **Bilingual Support** - Vietnamese (primary) and English
- **Admin Dashboard** - Comprehensive management interface
- **Real-time Updates** - WebSocket support for live booking updates
- **Mobile Responsive** - Optimized for all device sizes

## 📊 Database Schema

The system uses PostgreSQL with Prisma ORM. Key entities include:

- **Users** - Customer and admin accounts
- **Locations** - Physical karaoke venues
- **Rooms** - Individual karaoke rooms with pricing
- **Bookings** - Reservation records with payment tracking
- **Payments** - Payment transaction records
- **Audit Logs** - System activity tracking

## 🔐 Security Features

- JWT-based authentication with refresh tokens
- Role-based access control (Customer, Admin, Super Admin)
- Input validation and sanitization
- CORS protection
- Rate limiting
- Secure password hashing with bcrypt

## 📱 API Documentation

API documentation will be available at:

- **Development**: `http://localhost:3001/api/docs`
- **Swagger/OpenAPI** specifications included

## 🚀 Deployment

The system is designed for self-hosted VPS deployment with:

- **Docker containerization**
- **Nginx reverse proxy**
- **PostgreSQL database**
- **Redis caching**
- **SSL/TLS encryption**

Detailed deployment instructions are available in `cursor-md/DEPLOYMENT.md`.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support and questions:

- Create an issue in the repository
- Email: <EMAIL>
- Documentation: `cursor-md/` directory

---

**Queen Karaoke Booking System** - Making karaoke bookings simple and efficient! 🎤
