{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["apps/web/src/*"], "@api/*": ["apps/api/src/*"], "@shared-types/*": ["packages/shared-types/src/*"], "shared-types": ["packages/shared-types/src/index"], "shared-types/*": ["packages/shared-types/src/*"]}, "incremental": true, "plugins": [{"name": "next"}]}, "exclude": ["node_modules", ".next", "dist", "build"]}