import {
  IsOptional,
  IsString,
  IsEnum,
  IsInt,
  Min,
  IsDateString,
  IsUUID,
} from 'class-validator';
import { BookingStatus, SortOrder } from '@shared-types/common.types';

export class BookingQueryDto {
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number;

  @IsOptional()
  @IsInt()
  @Min(1)
  limit?: number;

  @IsOptional()
  @IsString()
  sortBy?: string; // e.g., 'createdAt', 'startTime', 'totalPrice'

  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder;

  // Fields for filtering
  @IsOptional()
  @IsUUID()
  locationId?: string;

  @IsOptional()
  @IsUUID()
  roomId?: string;

  @IsOptional()
  @IsUUID()
  userId?: string; // For admins querying by user

  @IsOptional()
  @IsEnum(BookingStatus)
  status?: BookingStatus;

  @IsOptional()
  @IsDateString()
  bookingDateFrom?: string; // YYYY-MM-DD

  @IsOptional()
  @IsDateString()
  bookingDateTo?: string; // YYYY-MM-DD

  @IsOptional()
  @IsString()
  guestEmail?: string; // For searching guest bookings by email

  @IsOptional()
  @IsString()
  search?: string; // General search for booking reference, guest name, email, etc.

  @IsOptional()
  @IsString()
  bookingReference?: string; // For searching by booking reference
}
