'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  Booking,
  BookingStatus,
  PaymentStatus,
  AdminUpdateBookingStatusDto,
  UserRole,
} from 'shared-types';
import { bookingsService } from '../../../../services/bookings.service';
import StatusBadge from '../../../../components/ui/status-badge';
import {
  ArrowLeft,
  Edit,
  Save,
  X,
  Clock,
  MapPin,
  Users,
  Phone,
  Mail,
  Calendar,
  AlertCircle,
  CreditCard,
  CheckCircle,
  UserCheck,
  Receipt,
  CalendarDays,
} from 'lucide-react';

const BookingDetailPage = () => {
  const router = useRouter();
  const params = useParams();
  const bookingId = params.id as string;

  const [booking, setBooking] = useState<Booking | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [editingStatus, setEditingStatus] = useState(false);
  const [newStatus, setNewStatus] = useState<BookingStatus | ''>('');
  const [adminNotes, setAdminNotes] = useState('');

  // Fetch booking details
  useEffect(() => {
    const fetchBooking = async () => {
      try {
        setLoading(true);
        const response = await bookingsService.getBookingByIdAdmin(bookingId);
        setBooking(response);
        setNewStatus(response.status);
        setAdminNotes(response.adminNotes || '');
      } catch (error) {
        console.error('Error fetching booking:', error);
        // TODO: Add error notification
      } finally {
        setLoading(false);
      }
    };

    if (bookingId) {
      fetchBooking();
    }
  }, [bookingId]);

  // Handle status update
  const handleUpdateBooking = async () => {
    if (!booking || !newStatus) return;

    try {
      setUpdating(true);

      const updateData: AdminUpdateBookingStatusDto = {
        status: newStatus as BookingStatus,
        adminNotes: adminNotes.trim() || undefined,
      };

      await bookingsService.updateBookingAdmin(booking.id, updateData);

      // Refresh booking data
      const updatedBooking =
        await bookingsService.getBookingByIdAdmin(bookingId);
      setBooking(updatedBooking);
      setEditingStatus(false);

      // TODO: Add success notification
    } catch (error) {
      console.error('Error updating booking:', error);
      // TODO: Add error notification
    } finally {
      setUpdating(false);
    }
  };

  // Cancel editing
  const handleCancelEdit = () => {
    if (booking) {
      setNewStatus(booking.status);
      setAdminNotes(booking.adminNotes || '');
    }
    setEditingStatus(false);
  };

  // Format date and time
  const formatDateTime = (dateTime: string) => {
    const date = new Date(dateTime);
    return {
      date: date.toLocaleDateString('vi-VN', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }),
      time: date.toLocaleTimeString('vi-VN', {
        hour: '2-digit',
        minute: '2-digit',
      }),
    };
  };

  // Get localized string
  const getLocalizedString = (
    str: { vi?: string; en?: string } | string | undefined
  ): string => {
    if (typeof str === 'string') return str;
    return str?.vi || str?.en || 'N/A';
  };

  // Calculate duration from API or fallback to calculation
  const getDuration = () => {
    if (!booking) return '0 giờ';
    // Use durationMinutes from API if available
    if (booking.durationMinutes) {
      const hours = Math.floor(booking.durationMinutes / 60);
      const minutes = booking.durationMinutes % 60;
      if (hours > 0 && minutes > 0) {
        return `${hours} giờ ${minutes} phút`;
      } else if (hours > 0) {
        return `${hours} giờ`;
      } else {
        return `${minutes} phút`;
      }
    }
    // Fallback to calculation
    const start = new Date(booking.startTime);
    const end = new Date(booking.endTime);
    const hours = Math.round(
      (end.getTime() - start.getTime()) / (1000 * 60 * 60)
    );
    return `${hours} giờ`;
  };

  // Format price with currency from booking
  const formatPrice = (price: number, currency?: string) => {
    const currencyCode = currency || booking?.currency || 'VND';
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: currencyCode,
    }).format(price);
  };

  // Get payment status badge
  const getPaymentStatusConfig = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.PENDING:
        return {
          label: 'Chờ thanh toán',
          bgColor: 'bg-yellow-100',
          textColor: 'text-yellow-800',
          borderColor: 'border-yellow-300',
          icon: Clock,
        };
      case PaymentStatus.SUCCESSFUL:
        return {
          label: 'Đã thanh toán',
          bgColor: 'bg-green-100',
          textColor: 'text-green-800',
          borderColor: 'border-green-300',
          icon: CheckCircle,
        };
      case PaymentStatus.FAILED:
        return {
          label: 'Thanh toán thất bại',
          bgColor: 'bg-red-100',
          textColor: 'text-red-800',
          borderColor: 'border-red-300',
          icon: X,
        };
      case PaymentStatus.REFUNDED:
        return {
          label: 'Đã hoàn tiền',
          bgColor: 'bg-blue-100',
          textColor: 'text-blue-800',
          borderColor: 'border-blue-300',
          icon: Receipt,
        };
      default:
        return {
          label: status,
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          borderColor: 'border-gray-300',
          icon: CreditCard,
        };
    }
  };

  // Get user role display
  const getUserRoleDisplay = (role: string) => {
    switch (role) {
      case UserRole.CUSTOMER:
        return 'Khách hàng';
      case UserRole.ADMIN:
        return 'Nhân viên';
      case UserRole.SUPER_ADMIN:
        return 'Quản trị viên';
      default:
        return 'Không xác định';
    }
  };

  // Format booking date
  const formatBookingDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const statusOptions = [
    { value: BookingStatus.PENDING_PAYMENT, label: 'Chờ thanh toán' },
    { value: BookingStatus.CONFIRMED, label: 'Đã xác nhận' },
    { value: BookingStatus.COMPLETED, label: 'Hoàn thành' },
    { value: BookingStatus.CANCELLED_BY_USER, label: 'Khách hủy' },
    { value: BookingStatus.CANCELLED_BY_ADMIN, label: 'Admin hủy' },
    { value: BookingStatus.NO_SHOW, label: 'Không đến' },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gold-600"></div>
        <span className="ml-3 text-gray-900">
          Đang tải thông tin đặt phòng...
        </span>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="mx-auto h-12 w-12 text-red-400 mb-4" />
        <h3 className="text-h4 font-medium text-gray-900 mb-2">
          Không tìm thấy đặt phòng
        </h3>
        <p className="text-gray-900 mb-6">
          Đặt phòng này có thể đã bị xóa hoặc không tồn tại.
        </p>
        <button
          onClick={() => router.push('/dashboard/bookings')}
          className="bg-gold-600 hover:bg-gold-700 text-white px-6 py-3 rounded-lg transition-colors"
        >
          Quay lại danh sách
        </button>
      </div>
    );
  }

  const startDateTime = formatDateTime(booking.startTime);
  const endDateTime = formatDateTime(booking.endTime);
  const customerName =
    booking.user?.name || booking.guestName || 'Khách vãng lai';
  const customerEmail = booking.user?.email || booking.guestEmail || '';
  const customerPhone = booking.user?.phoneNumber || booking.guestPhone || '';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.push('/dashboard/bookings')}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-h1 font-semibold text-gray-900">
              {booking.bookingReference}
            </h1>
          </div>
        </div>

        <div className="flex items-center gap-3">
          {!editingStatus ? (
            <button
              onClick={() => setEditingStatus(true)}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 flex items-center gap-2 transition-colors"
            >
              <Edit className="w-4 h-4" />
              Chỉnh sửa
            </button>
          ) : (
            <div className="flex items-center gap-2">
              <button
                onClick={handleCancelEdit}
                disabled={updating}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <X className="w-4 h-4" />
                Hủy
              </button>
              <button
                onClick={handleUpdateBooking}
                disabled={updating || !newStatus}
                className="bg-gold-600 hover:bg-gold-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <Save className="w-4 h-4" />
                {updating ? 'Đang lưu...' : 'Lưu'}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Booking Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-h3 font-semibold text-gray-900">
                Thông tin đặt phòng
              </h2>
              <StatusBadge status={booking.status} size="lg" />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <MapPin className="w-5 h-5 text-gold-600 mt-0.5" />
                  <div>
                    <div className="text-sm font-medium text-gray-700">
                      Cơ sở
                    </div>
                    <div className="text-gray-900">
                      {getLocalizedString(booking.location?.name)}
                    </div>
                    <div className="text-sm text-gray-600">
                      {booking.location?.address}
                    </div>
                    {booking.location?.description && (
                      <div className="text-sm text-gray-600 mt-1">
                        {getLocalizedString(booking.location.description)}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Users className="w-5 h-5 text-gold-600 mt-0.5" />
                  <div>
                    <div className="text-sm font-medium text-gray-700">
                      Phòng
                    </div>
                    <div className="text-gray-900">
                      {getLocalizedString(booking.room?.name)}
                    </div>
                    <div className="text-sm text-gray-600">
                      Sức chứa: {booking.room?.capacity} người
                      {booking.room?.roomType && (
                        <span> | Loại: {booking.room.roomType}</span>
                      )}
                    </div>
                    {booking.room?.theme && (
                      <div className="text-sm text-gray-600">
                        Chủ đề: {getLocalizedString(booking.room.theme)}
                      </div>
                    )}
                    {booking.room?.description && (
                      <div className="text-sm text-gray-600 mt-1">
                        {getLocalizedString(booking.room.description)}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <CalendarDays className="w-5 h-5 text-gold-600 mt-0.5" />
                  <div>
                    <div className="text-sm font-medium text-gray-700">
                      Ngày đặt phòng
                    </div>
                    <div className="text-gray-900">
                      {formatBookingDate(booking.bookingDate)}
                    </div>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Calendar className="w-5 h-5 text-gold-600 mt-0.5" />
                  <div>
                    <div className="text-sm font-medium text-gray-700">
                      Ngày sử dụng
                    </div>
                    <div className="text-gray-900">{startDateTime.date}</div>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Clock className="w-4 h-4 text-gold-600 flex-shrink-0" />
                  <div>
                    <div className="text-sm font-medium text-gray-700">
                      Thời gian
                    </div>
                    <div className="text-gray-900">
                      {startDateTime.time} - {endDateTime.time} ({getDuration()}
                      )
                    </div>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Users className="w-5 h-5 text-gold-600 mt-0.5" />
                  <div>
                    <div className="text-sm font-medium text-gray-700">
                      Số khách
                    </div>
                    <div className="text-gray-900">
                      {booking.numberOfGuests} người
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Customer Information */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-h3 font-semibold text-gray-900 mb-4">
              Thông tin khách hàng
            </h2>

            <div className="space-y-4">
              <div>
                <div className="text-sm font-medium text-gray-700 mb-1">
                  Họ tên
                </div>
                <div className="text-gray-900">{customerName}</div>
              </div>

              {customerEmail && (
                <div className="flex items-center gap-3">
                  <Mail className="w-4 h-4 text-gold-600" />
                  <div>
                    <div className="text-sm font-medium text-gray-700">
                      Email
                    </div>
                    <div className="text-gray-900">{customerEmail}</div>
                  </div>
                </div>
              )}

              {customerPhone && (
                <div className="flex items-center gap-3">
                  <Phone className="w-4 h-4 text-gold-600" />
                  <div>
                    <div className="text-sm font-medium text-gray-700">
                      Số điện thoại
                    </div>
                    <div className="text-gray-900">{customerPhone}</div>
                  </div>
                </div>
              )}

              <div className="flex items-center gap-3">
                <UserCheck className="w-4 h-4 text-gold-600" />
                <div>
                  <div className="text-sm font-medium text-gray-700">
                    Loại khách hàng
                  </div>
                  <div className="text-gray-900">
                    {booking.user ? 'Tài khoản đã đăng ký' : 'Khách vãng lai'}
                    {booking.user?.role && (
                      <span className="text-sm text-gray-600 ml-2">
                        ({getUserRoleDisplay(booking.user.role)})
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {booking.user?.lastLogin && (
                <div className="text-sm text-gray-600">
                  Đăng nhập lần cuối:{' '}
                  {new Date(booking.user.lastLogin).toLocaleDateString('vi-VN')}
                </div>
              )}
            </div>
          </div>

          {/* Notes */}
          {booking.notes && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-h3 font-semibold text-gray-900 mb-4">
                Ghi chú của khách hàng
              </h2>
              <p className="text-gray-900 bg-gray-50 p-4 rounded-lg">
                {booking.notes}
              </p>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Payment Information */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-h3 font-semibold text-gray-900 mb-4">
              Thông tin thanh toán
            </h2>

            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-700">Giá phòng/giờ:</span>
                <span className="font-medium text-gray-900">
                  {formatPrice(booking.room?.pricePerHour || 0)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-700">Thời gian:</span>
                <span className="font-medium text-gray-900">
                  {getDuration()}
                </span>
              </div>
              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-medium text-gray-900">
                    Tổng cộng:
                  </span>
                  <span className="text-lg font-semibold text-gold-600">
                    {formatPrice(booking.totalPrice, booking.currency)}
                  </span>
                </div>
              </div>

              {/* Payment Status and Details */}
              {booking.payment && (
                <div className="border-t border-gray-200 pt-4 mt-4">
                  <h3 className="text-base font-medium text-gray-700 mb-3">
                    Trạng thái thanh toán
                  </h3>

                  {/* Payment Status Badge */}
                  <div className="mb-3">
                    {(() => {
                      const config = getPaymentStatusConfig(
                        booking.payment.status
                      );
                      const IconComponent = config.icon;
                      return (
                        <div
                          className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium border ${config.bgColor} ${config.textColor} ${config.borderColor}`}
                        >
                          <IconComponent className="w-4 h-4" />
                          {config.label}
                        </div>
                      );
                    })()}
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Số tiền:</span>
                      <span className="font-medium text-gray-900">
                        {formatPrice(
                          booking.payment.amount,
                          booking.payment.currency
                        )}
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-600">Cổng thanh toán:</span>
                      <span className="font-medium text-gray-900">
                        {booking.payment.paymentGateway}
                      </span>
                    </div>

                    {booking.payment.transactionId && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Mã giao dịch:</span>
                        <span className="font-medium text-gray-900 font-mono text-xs">
                          {booking.payment.transactionId}
                        </span>
                      </div>
                    )}

                    {booking.payment.paymentMethod && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Phương thức:</span>
                        <span className="font-medium text-gray-900">
                          {booking.payment.paymentMethod}
                        </span>
                      </div>
                    )}

                    {booking.payment.paidAt && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">
                          Thời gian thanh toán:
                        </span>
                        <span className="font-medium text-gray-900">
                          {new Date(booking.payment.paidAt).toLocaleString(
                            'vi-VN'
                          )}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Status Management */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-h3 font-semibold text-gray-900 mb-4">
              Quản lý trạng thái
            </h2>

            {!editingStatus ? (
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Trạng thái hiện tại
                  </label>
                  <div className="mt-1">
                    <StatusBadge status={booking.status} size="lg" />
                  </div>
                </div>
                <button
                  onClick={() => setEditingStatus(true)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 flex items-center justify-center gap-2 transition-colors"
                >
                  <Edit className="w-4 h-4" />
                  Thay đổi trạng thái
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Trạng thái mới
                  </label>
                  <select
                    value={newStatus}
                    onChange={e =>
                      setNewStatus(e.target.value as BookingStatus)
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-gold-500 text-gray-900"
                    disabled={updating}
                  >
                    <option value="">Chọn trạng thái</option>
                    {statusOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ghi chú admin
                  </label>
                  <textarea
                    value={adminNotes}
                    onChange={e => setAdminNotes(e.target.value)}
                    placeholder="Thêm ghi chú cho đặt phòng này..."
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-gold-500 resize-none text-gray-900"
                    disabled={updating}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Booking History */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-h3 font-semibold text-gray-900 mb-4">
              Lịch sử đặt phòng
            </h2>

            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-700">Ngày tạo:</span>
                <span className="font-medium text-gray-900">
                  {new Date(booking.createdAt).toLocaleDateString('vi-VN')}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-700">Cập nhật cuối:</span>
                <span className="font-medium text-gray-900">
                  {new Date(booking.updatedAt).toLocaleDateString('vi-VN')}
                </span>
              </div>
            </div>
          </div>

          {/* Admin Notes Display */}
          {booking.adminNotes && !editingStatus && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-h3 font-semibold text-gray-900 mb-4">
                Ghi chú admin
              </h2>
              <p className="text-gray-900 bg-gold-50 p-4 rounded-lg border-l-4 border-gold-400">
                {booking.adminNotes}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BookingDetailPage;
