# Queen Karaoke Booking System - User Stories

**Note on Bilingual Support:** All user-facing content (text, labels, messages, emails, etc.) described in the user stories below should be available in both Vietnamese (primary) and English, based on the user's selected language.

## 0. General User Stories

- **US0.1: Language Selection**
  - **As a** user (public, customer, or admin),
  - **I want to** be able to select my preferred language (Vietnamese or English) for the user interface,
  - **So that I can** understand and interact with the system effectively in my chosen language.
  - **Acceptance Criteria:**
    - A clear language switcher/selector is available on all pages (e.g., in the header or footer).
    - The selected language persists across sessions (e.g., using local storage or user profile setting for registered users).
    - All UI text, labels, buttons, and system messages are displayed in the selected language.
    - Default language is Vietnamese.

## 1. Public Users (Booking Flow)

- **US1.1: Select Location**

  - **As a** public user,
  - **I want to** see a list of available karaoke locations (e.g., <PERSON><PERSON><PERSON>ng Phú, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Nẵng),
  - **So that I can** choose the location most convenient for me.
  - **Acceptance Criteria:**
    - The home page or a dedicated booking page displays the list of locations.
    - Each location is clearly identifiable by name.
    - Users can click or select a location to proceed.

- **US1.2: View Available Rooms**

  - **As a** public user,
  - **I want to** view the available themed rooms for a selected location, including details like capacity, theme/decoration style, and pricing per hour/slot,
  - **So that I can** choose a room that fits my party size, preferences, and budget.
  - **Acceptance Criteria:**
    - After selecting a location, a list of rooms for that location is displayed.
    - Each room listing shows its name, theme description, maximum capacity, and price.
    - High-quality images of each room's decoration style are visible.
    - A filter or sort option by capacity or price might be available.

- **US1.3: Choose Date and Time Slot**

  - **As a** public user,
  - **I want to** select a specific date and an available time slot for my chosen room,
  - **So that I can** reserve the room for my desired time.
  - **Acceptance Criteria:**
    - A calendar interface allows date selection.
    - Available time slots for the selected date and room are clearly displayed (e.g., 1-hour or 2-hour slots).
    - Booked or unavailable slots are visually distinct and unselectable.
    - The system prevents booking for past dates/times.
    - The total price based on the selected slot duration is displayed.

- **US1.4: Provide Booking Details**

  - **As a** public user,
  - **I want to** provide my contact information (name, phone number, email) during the booking process,
  - **So that** the system can confirm my booking and send reminders.
  - **Acceptance Criteria:**
    - A form to input name, phone number, and email address.
    - Basic validation for phone number and email format.

- **US1.5: Review Booking and Proceed to Payment**

  - **As a** public user,
  - **I want to** review all my booking details (location, room, date, time, total price, my contact info) before payment,
  - **So that I can** confirm everything is correct.
  - **Acceptance Criteria:**
    - A summary page displays all booking details.
    - A clear call-to-action button to proceed to payment.
    - Option to go back and modify details.

- **US1.6: Checkout via PayOS.vn**

  - **As a** public user,
  - **I want to** securely pay for my booking using the PayOS.vn payment gateway,
  - **So that I can** complete my reservation.
  - **Acceptance Criteria:**
    - Seamless redirection or integration with PayOS.vn.
    - Secure transmission of payment details.
    - Clear indication of payment success or failure.

- **US1.7: Receive Booking Confirmation**
  - **As a** public user,
  - **I want to** receive an immediate booking confirmation on-screen and via email after successful payment,
  - **So that I have** proof of my reservation and its details.
  - **Acceptance Criteria:**
    - A confirmation page is displayed post-payment with booking ID and details.
    - An automated email confirmation is sent to the provided email address, including booking ID, location, room, date, time, price paid, and a QR code/booking reference.

## 2. Registered Customers (Account & Booking Management)

- **US2.1: Create Account**

  - **As a** new user,
  - **I want to** create an account using my email and a password,
  - **So that I can** manage my bookings and potentially receive special offers.
  - **Acceptance Criteria:**
    - A registration form for email, password (with confirmation), name, and phone number.
    - Password strength indicator.
    - Email verification step (optional but recommended).
    - Successful account creation and login.

- **US2.2: Login to Account**

  - **As a** registered customer,
  - **I want to** log in to my account using my email and password,
  - **So that I can** access my account dashboard.
  - **Acceptance Criteria:**
    - Login form for email and password.
    - "Forgot Password" functionality.
    - Secure login process (e.g., protection against brute-force attacks).

- **US2.3: View Upcoming Bookings**

  - **As a** registered customer,
  - **I want to** see a list of my upcoming karaoke bookings with all relevant details (date, time, location, room),
  - **So that I can** keep track of my reservations.
  - **Acceptance Criteria:**
    - A dedicated section in the user dashboard lists upcoming bookings.
    - Each booking shows location, room name, date, time, and booking status (e.g., Confirmed).

- **US2.4: View Past Bookings**

  - **As a** registered customer,
  - **I want to** see a history of my past karaoke bookings,
  - **So that I can** recall previous visits or rebook similar experiences.
  - **Acceptance Criteria:**
    - A dedicated section lists past bookings.
    - Each past booking shows relevant details.

- **US2.5: Manage (Cancel/Modify) Bookings (if applicable)**

  - **As a** registered customer,
  - **I want to** be able to cancel or request modification for an upcoming booking (subject to policy, e.g., 24 hours in advance),
  - **So that I can** adjust my plans if needed.
  - **Acceptance Criteria:**
    - Clear options to cancel or request modification for eligible bookings.
    - System clearly communicates cancellation/modification policies and any associated fees.
    - Confirmation of cancellation/modification request.

- **US2.6: Update Profile Information**

  - **As a** registered customer,
  - **I want to** update my profile information (e.g., name, phone number, password),
  - **So that my** account details are always current.
  - **Acceptance Criteria:**
    - A section to edit profile details.
    - Secure password change functionality (current password required).

- **US2.7: Receive Email Reminders**
  - **As a** registered customer,
  - **I want to** receive email reminders for my upcoming bookings (e.g., 24 hours before),
  - **So that I don't** forget my reservation.
  - **Acceptance Criteria:**
    - Automated email reminders are sent to the customer's registered email address.
    - Reminder email includes booking details and a link to view the booking.

## 3. Admins (CRUD, Analytics, Security)

- **US3.1: Secure Admin Login**

  - **As an** administrator,
  - **I want to** log in to a secure admin panel using dedicated credentials,
  - **So that I can** manage the karaoke booking system.
  - **Acceptance Criteria:**
    - Separate login page/portal for administrators.
    - Strong authentication mechanisms (e.g., two-factor authentication is a plus).
    - Access restricted to authorized admin accounts only.

- **US3.2: Manage Locations (CRUD)**

  - **As an** administrator,
  - **I want to** create, read, update, and delete karaoke locations,
  - **So that the** list of available locations is accurate and up-to-date.
  - **Acceptance Criteria:**
    - Admin interface to add new locations (name, address, description, images).
    - List all existing locations with options to edit or delete.

- **US3.3: Manage Rooms (CRUD)**

  - **As an** administrator,
  - **I want to** create, read, update, and delete rooms for each location, including their capacity, theme, pricing, and images,
  - **So that room** information displayed to users is accurate.
  - **Acceptance Criteria:**
    - Admin interface to add new rooms, associating them with a specific location.
    - Fields for room name, description, theme, capacity, price per slot/hour, images.
    - List all rooms with options to edit or delete.

- **US3.4: Manage Time Slot Availability (CRUD)**

  - **As an** administrator,
  - **I want to** define and manage standard time slots, mark rooms as unavailable for specific dates/times (e.g., for maintenance), or create special event slots,
  - **So that the** booking calendar accurately reflects room availability.
  - **Acceptance Criteria:**
    - Interface to define default operating hours and slot durations per location/room type.
    - Ability to block out specific dates/times for a room.
    - Ability to create custom availability or special event pricing for certain slots.

- **US3.5: Manage Bookings (CRUD)**

  - **As an** administrator,
  - **I want to** view, search, filter, manually create, update (e.g., change room, time), and cancel customer bookings,
  - **So that I can** assist customers and manage reservations effectively.
  - **Acceptance Criteria:**
    - A comprehensive list of all bookings with search and filter capabilities (by date, customer, status, location, room).
    - Ability to view full details of any booking.
    - Functionality to manually add a new booking.
    - Options to modify existing bookings (with logging of changes).
    - Option to cancel bookings (with reason, if applicable).
    - Clear indication of payment status for each booking.

- **US3.6: View Booking Analytics**

  - **As an** administrator,
  - **I want to** view booking analytics, such as occupancy rates per location/room, revenue per room/location, popular time slots, and booking trends,
  - **So that I can** make informed business decisions.
  - **Acceptance Criteria:**
    - Dashboard displaying key metrics (e.g., total bookings, total revenue, average occupancy).
    - Charts and reports for:
      - Occupancy rates (daily, weekly, monthly) by location and room.
      - Revenue generated by location and room.
      - Most popular rooms and time slots.
      - Booking trends over time.
    - Ability to filter analytics by date range and location.

- **US3.7: Manage Customer Accounts (View, Block/Unblock)**

  - **As an** administrator,
  - **I want to** view customer account details and have the ability to block or unblock customer accounts if necessary,
  - **So that I can** manage user access and handle problematic accounts.
  - **Acceptance Criteria:**
    - Search and view list of registered customers.
    - View customer profile information and booking history.
    - Option to temporarily or permanently block a user account.

- **US3.8: Configure System Settings**

  - **As an** administrator,
  - **I want to** configure system-wide settings, such as payment gateway API keys, email notification templates (basic), and booking policies (e.g., cancellation window),
  - **So that I can** tailor the system to business needs.
  - **Acceptance Criteria:**
    - A settings area in the admin panel.
    - Secure input for API keys and sensitive configurations.
    - Basic editor or options to customize content of email templates (confirmation, reminder).
    - Fields to define booking rules like minimum notice for cancellation.

- **US3.9: View Audit Logs (Security)**
  - **As an** administrator,
  - **I want to** view audit logs for critical actions within the admin panel (e.g., who changed a booking, who modified a room detail),
  - **So that there** is accountability and a trail for security reviews.
  - **Acceptance Criteria:**
    - Logs capturing important events: admin logins, CRUD operations on core entities (rooms, bookings, locations), setting changes.
    - Log entries should include timestamp, admin user involved, action performed, and affected entity.
