{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.3", "@radix-ui/react-dropdown-menu": "^2.1.3", "@radix-ui/react-form": "^0.1.0", "@radix-ui/react-select": "^2.1.3", "@radix-ui/react-toast": "^1.2.3", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.15.0", "lucide-react": "^0.469.0", "next": "15.3.3", "next-international": "^1.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "shared-types": "file:../../packages/shared-types", "tailwind-merge": "^2.6.0", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}