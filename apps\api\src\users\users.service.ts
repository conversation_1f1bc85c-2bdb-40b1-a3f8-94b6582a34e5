import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  UserRole,
  BookingStatus,
  UserResponseDto,
  UserDetailResponseDto,
} from 'shared-types';
import type {
  AdminCreateUserDto,
  AdminUpdateUserDto,
  UpdateUserRoleDto,
  UpdateUserStatusDto,
  UserListResponseDto,
  UserStatsDto,
  LocalizedString,
  AdminChangePasswordDto,
} from 'shared-types';
import * as bcrypt from 'bcrypt';
import { generateRandomPassword } from '../utils/password.util';

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}

  async findOne(id: string): Promise<UserResponseDto | null> {
    const user = await this.prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        name: true,
        phoneNumber: true,
        role: true,
        isActive: true,
        emailVerified: true,
        lastLogin: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            bookings: true,
          },
        },
        bookings: {
          select: {
            totalPrice: true,
          },
        },
      },
    });

    if (!user) return null;

    const totalSpent = user.bookings.reduce(
      (sum, booking) => sum + (booking.totalPrice?.toNumber() || 0),
      0,
    );

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      phoneNumber: user.phoneNumber,
      role: user.role as UserRole,
      isActive: user.isActive,
      emailVerified: user.emailVerified,
      lastLogin: user.lastLogin?.toISOString(),
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
      bookingCount: user._count.bookings,
      totalSpent,
    };
  }

  async findByEmail(email: string) {
    return await this.prisma.user.findUnique({
      where: { email },
    });
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    search?: string,
    role?: UserRole,
    isActive?: boolean,
    emailVerified?: boolean,
  ): Promise<UserListResponseDto> {
    const skip = (page - 1) * limit;

    const where = {
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' as const } },
          { email: { contains: search, mode: 'insensitive' as const } },
        ],
      }),
      ...(role && { role }),
      ...(isActive !== undefined && { isActive }),
      ...(emailVerified !== undefined && { emailVerified }),
    };

    const [users, total] = await Promise.all([
      this.prisma.user.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          email: true,
          name: true,
          phoneNumber: true,
          role: true,
          isActive: true,
          emailVerified: true,
          lastLogin: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              bookings: true,
            },
          },
          bookings: {
            select: {
              totalPrice: true,
            },
          },
        },
      }),
      this.prisma.user.count({ where }),
    ]);

    const usersWithStats = users.map((user) => {
      const totalSpent = user.bookings.reduce(
        (sum, booking) => sum + (booking.totalPrice?.toNumber() || 0),
        0,
      );

      return {
        id: user.id,
        email: user.email,
        name: user.name,
        phoneNumber: user.phoneNumber,
        role: user.role as UserRole,
        isActive: user.isActive,
        emailVerified: user.emailVerified,
        lastLogin: user.lastLogin?.toISOString(),
        createdAt: user.createdAt.toISOString(),
        updatedAt: user.updatedAt.toISOString(),
        bookingCount: user._count.bookings,
        totalSpent,
      };
    });

    return {
      users: usersWithStats,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findByIdWithDetails(id: string): Promise<UserDetailResponseDto | null> {
    const user = await this.prisma.user.findUnique({
      where: { id },
      include: {
        bookings: {
          take: 10,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            bookingReference: true,
            startTime: true,
            endTime: true,
            status: true,
            totalPrice: true,
            location: {
              select: {
                name: true,
              },
            },
            room: {
              select: {
                name: true,
              },
            },
          },
        },
        auditLogs: {
          take: 10,
          orderBy: { timestamp: 'desc' },
          select: {
            id: true,
            action: true,
            timestamp: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        _count: {
          select: {
            bookings: true,
          },
        },
      },
    });

    if (!user) return null;

    const totalSpent = user.bookings.reduce(
      (sum, booking) => sum + (booking.totalPrice?.toNumber() || 0),
      0,
    );

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      phoneNumber: user.phoneNumber,
      role: user.role as UserRole,
      isActive: user.isActive,
      emailVerified: user.emailVerified,
      lastLogin: user.lastLogin?.toISOString(),
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
      bookingCount: user._count.bookings,
      totalSpent,
      recentBookings: user.bookings.map((booking) => ({
        id: booking.id,
        bookingReference: booking.bookingReference,
        locationName: booking.location.name as unknown as LocalizedString,
        roomName: booking.room.name as unknown as LocalizedString,
        startTime: booking.startTime.toISOString(),
        endTime: booking.endTime.toISOString(),
        status: booking.status as BookingStatus,
        totalPrice: booking.totalPrice.toNumber(),
      })),
      auditLogs: user.auditLogs.map((log) => ({
        id: log.id,
        action: log.action,
        createdAt: log.timestamp.toISOString(),
        performedByUser: log.user
          ? {
              id: log.user.id,
              name: log.user.name,
              email: log.user.email,
            }
          : undefined,
      })),
    };
  }

  async createUser(
    createUserDto: AdminCreateUserDto,
  ): Promise<UserResponseDto> {
    // Check if user already exists
    const existingUser = await this.findByEmail(createUserDto.email);
    if (existingUser) {
      throw new ConflictException('Email đã được sử dụng');
    }

    // Check if phone number already exists (if provided)
    if (createUserDto.phoneNumber) {
      const existingPhoneUser = await this.prisma.user.findUnique({
        where: { phoneNumber: createUserDto.phoneNumber },
      });
      if (existingPhoneUser) {
        throw new ConflictException('Số điện thoại đã được sử dụng');
      }
    }

    // Generate random password for admin-created users
    const tempPassword = generateRandomPassword();
    const hashedPassword = await bcrypt.hash(tempPassword, 10);

    const user = await this.prisma.user.create({
      data: {
        email: createUserDto.email,
        passwordHash: hashedPassword,
        name: createUserDto.name,
        phoneNumber: createUserDto.phoneNumber,
        role: createUserDto.role,
        isActive: createUserDto.isActive ?? true,
        emailVerified: false, // Admin-created users need to verify their email
      },
      select: {
        id: true,
        email: true,
        name: true,
        phoneNumber: true,
        role: true,
        isActive: true,
        emailVerified: true,
        lastLogin: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // TODO: Send welcome email with temporary password if sendWelcomeEmail is true

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      phoneNumber: user.phoneNumber,
      role: user.role as UserRole,
      isActive: user.isActive,
      emailVerified: user.emailVerified,
      lastLogin: user.lastLogin?.toISOString(),
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
      bookingCount: 0,
      totalSpent: 0,
    };
  }

  async updateUser(
    id: string,
    updateUserDto: AdminUpdateUserDto,
  ): Promise<UserResponseDto> {
    const existingUser = await this.findOne(id);
    if (!existingUser) {
      throw new NotFoundException('Không tìm thấy người dùng');
    }

    const updatedUser = await this.prisma.user.update({
      where: { id },
      data: updateUserDto,
      select: {
        id: true,
        email: true,
        name: true,
        phoneNumber: true,
        role: true,
        isActive: true,
        emailVerified: true,
        lastLogin: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            bookings: true,
          },
        },
        bookings: {
          select: {
            totalPrice: true,
          },
        },
      },
    });

    const totalSpent = updatedUser.bookings.reduce(
      (sum, booking) => sum + (booking.totalPrice?.toNumber() || 0),
      0,
    );

    return {
      id: updatedUser.id,
      email: updatedUser.email,
      name: updatedUser.name,
      phoneNumber: updatedUser.phoneNumber,
      role: updatedUser.role as UserRole,
      isActive: updatedUser.isActive,
      emailVerified: updatedUser.emailVerified,
      lastLogin: updatedUser.lastLogin?.toISOString(),
      createdAt: updatedUser.createdAt.toISOString(),
      updatedAt: updatedUser.updatedAt.toISOString(),
      bookingCount: updatedUser._count.bookings,
      totalSpent,
    };
  }

  async updateUserRole(
    id: string,
    updateRoleDto: UpdateUserRoleDto,
    currentUserId: string,
  ): Promise<UserResponseDto> {
    // Prevent self-role changes
    if (id === currentUserId) {
      throw new ForbiddenException('Không thể thay đổi vai trò của chính mình');
    }

    const existingUser = await this.findOne(id);
    if (!existingUser) {
      throw new NotFoundException('Không tìm thấy người dùng');
    }

    // Update user with role - directly update in database since AdminUpdateUserDto doesn't include role
    const updatedUser = await this.prisma.user.update({
      where: { id },
      data: { role: updateRoleDto.role },
      select: {
        id: true,
        email: true,
        name: true,
        phoneNumber: true,
        role: true,
        isActive: true,
        emailVerified: true,
        lastLogin: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            bookings: true,
          },
        },
        bookings: {
          select: {
            totalPrice: true,
          },
        },
      },
    });

    const totalSpent = updatedUser.bookings.reduce(
      (sum, booking) => sum + (booking.totalPrice?.toNumber() || 0),
      0,
    );

    return {
      id: updatedUser.id,
      email: updatedUser.email,
      name: updatedUser.name,
      phoneNumber: updatedUser.phoneNumber,
      role: updatedUser.role as UserRole,
      isActive: updatedUser.isActive,
      emailVerified: updatedUser.emailVerified,
      lastLogin: updatedUser.lastLogin?.toISOString(),
      createdAt: updatedUser.createdAt.toISOString(),
      updatedAt: updatedUser.updatedAt.toISOString(),
      bookingCount: updatedUser._count.bookings,
      totalSpent,
    };
  }

  async updateUserStatus(
    id: string,
    updateStatusDto: UpdateUserStatusDto,
    currentUserId: string,
  ): Promise<UserResponseDto> {
    // Prevent self-deactivation
    if (id === currentUserId && !updateStatusDto.isActive) {
      throw new ForbiddenException(
        'Không thể vô hiệu hóa tài khoản của chính mình',
      );
    }

    const existingUser = await this.findOne(id);
    if (!existingUser) {
      throw new NotFoundException('Không tìm thấy người dùng');
    }

    return this.updateUser(id, { isActive: updateStatusDto.isActive });
  }

  async deleteUser(id: string, currentUserId: string): Promise<void> {
    // Prevent self-deletion
    if (id === currentUserId) {
      throw new ForbiddenException('Không thể xóa tài khoản của chính mình');
    }

    const existingUser = await this.findOne(id);
    if (!existingUser) {
      throw new NotFoundException('Không tìm thấy người dùng');
    }

    // Check if user has any bookings
    const bookingCount = await this.prisma.booking.count({
      where: { userId: id },
    });

    if (bookingCount > 0) {
      throw new ConflictException(
        'Không thể xóa người dùng có lịch sử đặt phòng',
      );
    }

    await this.prisma.user.delete({
      where: { id },
    });
  }

  async getUserStats(): Promise<UserStatsDto> {
    const [
      totalUsers,
      activeUsers,
      verifiedUsers,
      usersByRole,
      recentRegistrations,
    ] = await Promise.all([
      this.prisma.user.count(),
      this.prisma.user.count({ where: { isActive: true } }),
      this.prisma.user.count({ where: { emailVerified: true } }),
      this.prisma.user.groupBy({
        by: ['role'],
        _count: true,
      }),
      this.prisma.user.groupBy({
        by: ['createdAt'],
        _count: true,
        where: {
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
    ]);

    return {
      totalUsers,
      activeUsers,
      inactiveUsers: totalUsers - activeUsers,
      verifiedUsers,
      unverifiedUsers: totalUsers - verifiedUsers,
      usersByRole: usersByRole.map((item) => ({
        role: item.role as UserRole,
        count: item._count,
      })),
      recentRegistrations: recentRegistrations.map((item) => ({
        date: item.createdAt.toISOString().split('T')[0],
        count: item._count,
      })),
    };
  }

  async adminChangePassword(
    id: string,
    changePasswordDto: AdminChangePasswordDto,
    currentUserId: string,
  ): Promise<{ message: string; newPassword?: string }> {
    // Prevent changing own password through admin function
    if (id === currentUserId) {
      throw new ForbiddenException(
        'Không thể thay đổi mật khẩu của chính mình qua chức năng admin',
      );
    }

    const existingUser = await this.findOne(id);
    if (!existingUser) {
      throw new NotFoundException('Không tìm thấy người dùng');
    }

    let newPassword: string;

    if (changePasswordDto.generateRandom || !changePasswordDto.newPassword) {
      // Generate random password
      newPassword = generateRandomPassword();
    } else {
      // Use provided password
      newPassword = changePasswordDto.newPassword;
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update user password
    await this.prisma.user.update({
      where: { id },
      data: { passwordHash: hashedPassword },
    });

    return {
      message: 'Mật khẩu đã được cập nhật thành công',
      newPassword:
        changePasswordDto.generateRandom || !changePasswordDto.newPassword
          ? newPassword
          : undefined,
    };
  }

  async adminVerifyEmail(userId: string): Promise<UserResponseDto> {
    const user = await this.findOne(userId);
    if (!user) {
      throw new NotFoundException('Không tìm thấy người dùng');
    }

    if (user.emailVerified) {
      throw new ConflictException('Email đã được xác thực trước đó');
    }

    const updatedUser = await this.prisma.user.update({
      where: { id: userId },
      data: {
        emailVerified: true,
        emailVerificationToken: null,
        emailVerificationTokenExpiresAt: null,
      },
      select: {
        id: true,
        email: true,
        name: true,
        phoneNumber: true,
        role: true,
        isActive: true,
        emailVerified: true,
        lastLogin: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return {
      ...updatedUser,
      role: updatedUser.role as UserRole,
      lastLogin: updatedUser.lastLogin?.toISOString(),
      createdAt: updatedUser.createdAt.toISOString(),
      updatedAt: updatedUser.updatedAt.toISOString(),
    };
  }
}
