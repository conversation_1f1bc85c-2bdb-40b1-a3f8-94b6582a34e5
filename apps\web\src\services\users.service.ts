import type {
  UserResponseDto,
  UserDetailResponseDto,
  UserListResponseDto,
  UserStatsDto,
  AdminCreateUserDto,
  AdminUpdateUserDto,
  UpdateUserRoleDto,
  UpdateUserStatusDto,
  UserRole,
} from 'shared-types';

interface AdminChangePasswordDto {
  newPassword?: string;
  generateRandom?: boolean;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export interface UserQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  role?: UserRole;
  isActive?: boolean;
  emailVerified?: boolean;
}

class UsersService {
  private async fetchWithAuth(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<Response> {
    const token = localStorage.getItem('auth-token');

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `HTTP error! status: ${response.status}`
      );
    }

    return response;
  }

  // Get all users with filtering and pagination
  async getUsers(params: UserQueryParams = {}): Promise<UserListResponseDto> {
    const searchParams = new URLSearchParams();

    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.search) searchParams.append('search', params.search);
    if (params.role) searchParams.append('role', params.role);
    if (params.isActive !== undefined)
      searchParams.append('isActive', params.isActive.toString());
    if (params.emailVerified !== undefined)
      searchParams.append('emailVerified', params.emailVerified.toString());

    const response = await this.fetchWithAuth(
      `/users?${searchParams.toString()}`
    );
    return response.json();
  }

  // Get user statistics
  async getUserStats(): Promise<UserStatsDto> {
    const response = await this.fetchWithAuth('/users/stats');
    return response.json();
  }

  // Get user by ID (basic info)
  async getUserById(id: string): Promise<UserResponseDto> {
    const response = await this.fetchWithAuth(`/users/${id}`);
    return response.json();
  }

  // Get user by ID with detailed information including bookings and audit logs
  async getUserDetails(id: string): Promise<UserDetailResponseDto> {
    const response = await this.fetchWithAuth(`/users/${id}/details`);
    return response.json();
  }

  // Create a new user (admin only)
  async createUser(userData: AdminCreateUserDto): Promise<UserResponseDto> {
    const response = await this.fetchWithAuth('/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
    return response.json();
  }

  // Update user information
  async updateUser(
    id: string,
    userData: AdminUpdateUserDto
  ): Promise<UserResponseDto> {
    const response = await this.fetchWithAuth(`/users/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(userData),
    });
    return response.json();
  }

  // Update user role (super admin only)
  async updateUserRole(
    id: string,
    roleData: UpdateUserRoleDto
  ): Promise<UserResponseDto> {
    const response = await this.fetchWithAuth(`/users/${id}/role`, {
      method: 'PATCH',
      body: JSON.stringify(roleData),
    });
    return response.json();
  }

  // Update user status (activate/deactivate)
  async updateUserStatus(
    id: string,
    statusData: UpdateUserStatusDto
  ): Promise<UserResponseDto> {
    const response = await this.fetchWithAuth(`/users/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify(statusData),
    });
    return response.json();
  }

  // Delete user (super admin only)
  async deleteUser(id: string): Promise<void> {
    await this.fetchWithAuth(`/users/${id}`, {
      method: 'DELETE',
    });
  }

  // Admin change password (super admin only)
  async adminChangePassword(
    id: string,
    passwordData: AdminChangePasswordDto
  ): Promise<{ message: string; newPassword?: string }> {
    const response = await this.fetchWithAuth(`/users/${id}/password`, {
      method: 'PATCH',
      body: JSON.stringify(passwordData),
    });
    return response.json();
  }

  // Manually verify user's email (super admin only)
  async verifyEmail(id: string): Promise<UserResponseDto> {
    const response = await this.fetchWithAuth(`/users/${id}/verify-email`, {
      method: 'PATCH',
    });
    return response.json();
  }

  // Helper methods
  getRoleLabel(role: UserRole): string {
    switch (role) {
      case 'CUSTOMER':
        return 'Khách hàng';
      case 'ADMIN':
        return 'Nhân viên';
      case 'SUPER_ADMIN':
        return 'Quản trị viên';
      default:
        return 'Không xác định';
    }
  }

  getStatusLabel(isActive: boolean): string {
    return isActive ? 'Hoạt động' : 'Bị vô hiệu hóa';
  }

  getVerificationLabel(emailVerified: boolean): string {
    return emailVerified ? 'Đã xác thực' : 'Chưa xác thực';
  }

  // Format user display name
  formatUserDisplayName(user: UserResponseDto): string {
    return user.name || user.email.split('@')[0];
  }

  // Check if user can be deleted (no bookings, not self)
  canDeleteUser(user: UserResponseDto, currentUserId: string): boolean {
    return (
      user.id !== currentUserId &&
      (user.bookingCount === 0 || user.bookingCount === undefined)
    );
  }

  // Check if user can change role (not self, super admin only)
  canChangeRole(
    user: UserResponseDto,
    currentUserId: string,
    currentUserRole: UserRole
  ): boolean {
    return user.id !== currentUserId && currentUserRole === 'SUPER_ADMIN';
  }

  // Check if user can be deactivated (not self)
  canDeactivateUser(user: UserResponseDto, currentUserId: string): boolean {
    return user.id !== currentUserId;
  }
}

export const usersService = new UsersService();
