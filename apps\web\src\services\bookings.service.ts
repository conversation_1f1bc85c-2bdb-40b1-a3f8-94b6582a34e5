import {
  BookingQueryDto,
  CreateBookingDto,
  CancelBookingDto,
  AdminUpdateBookingStatusDto,
  AdminUpdateBookingDto,
  UpdateBookingDto,
  PaginatedResponse,
  ApiResponse,
  BookingStatus,
} from 'shared-types';
import type { Booking } from 'shared-types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

class BookingsService {
  private async fetchWithAuth(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<Response> {
    const token = localStorage.getItem('auth-token');

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `HTTP error! status: ${response.status}`
      );
    }

    return response;
  }

  // User booking operations
  async createBooking(data: CreateBookingDto): Promise<ApiResponse<Booking>> {
    try {
      const response = await this.fetchWithAuth('/bookings', {
        method: 'POST',
        body: JSON.stringify(data),
      });
      const parsedResponse = await response.json();
      return parsedResponse;
    } catch (error) {
      console.error('Error creating booking:', error);
      throw error;
    }
  }

  // Admin method to create guest bookings
  async createGuestBookingByAdmin(
    data: CreateBookingDto
  ): Promise<ApiResponse<Booking>> {
    try {
      const response = await this.fetchWithAuth('/bookings/admin/guest', {
        method: 'POST',
        body: JSON.stringify(data),
      });
      return await response.json();
    } catch (error) {
      console.error('Error creating guest booking (admin):', error);
      throw error;
    }
  }

  async createUserBookingByAdmin(
    data: CreateBookingDto & { userId: string }
  ): Promise<ApiResponse<Booking>> {
    try {
      const response = await this.fetchWithAuth('/bookings/admin/user', {
        method: 'POST',
        body: JSON.stringify(data),
      });
      return await response.json();
    } catch (error) {
      console.error('Error creating user booking (admin):', error);
      throw error;
    }
  }

  async createGuestBooking(
    data: CreateBookingDto
  ): Promise<ApiResponse<Booking>> {
    try {
      const response = await fetch(`${API_BASE_URL}/bookings/guest`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating guest booking:', error);
      throw error;
    }
  }

  async getMyBookings(
    query: BookingQueryDto = {}
  ): Promise<PaginatedResponse<Booking>> {
    try {
      const params = new URLSearchParams();

      if (query.page) params.append('page', query.page.toString());
      if (query.limit) params.append('limit', query.limit.toString());
      if (query.status) params.append('status', query.status);
      if (query.search) params.append('search', query.search);
      if (query.sortBy) params.append('sortBy', query.sortBy);
      if (query.sortOrder) params.append('sortOrder', query.sortOrder);
      if (query.dateRange?.startDate)
        params.append('startDate', query.dateRange.startDate);
      if (query.dateRange?.endDate)
        params.append('endDate', query.dateRange.endDate);

      const response = await this.fetchWithAuth(
        `/bookings/my-bookings?${params.toString()}`
      );
      return await response.json();
    } catch (error) {
      console.error('Error fetching my bookings:', error);
      throw error;
    }
  }

  async getBookingById(id: string): Promise<Booking> {
    try {
      const response = await this.fetchWithAuth(`/bookings/${id}`);
      return await response.json();
    } catch (error) {
      console.error('Error fetching booking:', error);
      throw error;
    }
  }

  async cancelBooking(
    id: string,
    data: CancelBookingDto
  ): Promise<ApiResponse<Booking>> {
    try {
      const response = await this.fetchWithAuth(`/bookings/${id}`, {
        method: 'PATCH',
        body: JSON.stringify({
          status: BookingStatus.CANCELLED_BY_USER,
          cancellationReason: data.reason,
        }),
      });
      return await response.json();
    } catch (error) {
      console.error('Error cancelling booking:', error);
      throw error;
    }
  }

  // Admin booking operations
  async getAllBookings(
    query: BookingQueryDto = {}
  ): Promise<PaginatedResponse<Booking>> {
    try {
      const params = new URLSearchParams();

      if (query.page) params.append('page', query.page.toString());
      if (query.limit) params.append('limit', query.limit.toString());
      if (query.status) params.append('status', query.status);
      if (query.locationId) params.append('locationId', query.locationId);
      if (query.roomId) params.append('roomId', query.roomId);
      if (query.userId) params.append('userId', query.userId);
      if (query.search) params.append('search', query.search);
      if (query.sortBy) params.append('sortBy', query.sortBy);
      if (query.sortOrder) params.append('sortOrder', query.sortOrder);
      if (query.dateRange?.startDate)
        params.append('startDate', query.dateRange.startDate);
      if (query.dateRange?.endDate)
        params.append('endDate', query.dateRange.endDate);
      if (query.guestEmail) params.append('guestEmail', query.guestEmail);

      const response = await this.fetchWithAuth(
        `/bookings/admin/all?${params.toString()}`
      );
      return await response.json();
    } catch (error) {
      console.error('Error fetching all bookings:', error);
      throw error;
    }
  }

  async getBookingByIdAdmin(id: string): Promise<Booking> {
    try {
      const response = await this.fetchWithAuth(`/bookings/admin/${id}`);
      return await response.json();
    } catch (error) {
      console.error('Error fetching booking (admin):', error);
      throw error;
    }
  }

  async comprehensivelyUpdateBookingAdmin(
    id: string,
    data: AdminUpdateBookingDto
  ): Promise<ApiResponse<Booking>> {
    try {
      const response = await this.fetchWithAuth(
        `/bookings/admin/${id}/comprehensive`,
        {
          method: 'PATCH',
          body: JSON.stringify(data),
        }
      );
      return await response.json();
    } catch (error) {
      console.error('Error comprehensively updating booking (admin):', error);
      throw error;
    }
  }

  async updateBookingAdmin(
    id: string,
    data: AdminUpdateBookingStatusDto
  ): Promise<ApiResponse<Booking>> {
    try {
      const response = await this.fetchWithAuth(`/bookings/admin/${id}`, {
        method: 'PATCH',
        body: JSON.stringify(data),
      });
      return await response.json();
    } catch (error) {
      console.error('Error updating booking (admin):', error);
      throw error;
    }
  }

  // Helper methods for status and formatting
  getStatusColor(status: BookingStatus): string {
    switch (status) {
      case BookingStatus.PENDING_PAYMENT:
        return 'warning';
      case BookingStatus.CONFIRMED:
        return 'success';
      case BookingStatus.COMPLETED:
        return 'info';
      case BookingStatus.CANCELLED_BY_USER:
      case BookingStatus.CANCELLED_BY_ADMIN:
        return 'error';
      case BookingStatus.NO_SHOW:
        return 'error';
      default:
        return 'gray';
    }
  }

  getStatusLabel(status: BookingStatus): string {
    switch (status) {
      case BookingStatus.PENDING_PAYMENT:
        return 'Chờ thanh toán';
      case BookingStatus.CONFIRMED:
        return 'Đã xác nhận';
      case BookingStatus.COMPLETED:
        return 'Hoàn thành';
      case BookingStatus.CANCELLED_BY_USER:
        return 'Khách hủy';
      case BookingStatus.CANCELLED_BY_ADMIN:
        return 'Admin hủy';
      case BookingStatus.NO_SHOW:
        return 'Không đến';
      default:
        return status;
    }
  }

  formatBookingTime(startTime: string, endTime: string): string {
    const start = new Date(startTime);
    const end = new Date(endTime);

    const startStr = start.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
    });
    const endStr = end.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
    });

    return `${startStr} - ${endStr}`;
  }

  formatBookingDate(dateTime: string): string {
    const date = new Date(dateTime);
    return date.toLocaleDateString('vi-VN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  calculateDuration(startTime: string, endTime: string): number {
    const start = new Date(startTime);
    const end = new Date(endTime);
    return Math.round((end.getTime() - start.getTime()) / (1000 * 60 * 60)); // hours
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  }
}

export const bookingsService = new BookingsService();
export default bookingsService;
