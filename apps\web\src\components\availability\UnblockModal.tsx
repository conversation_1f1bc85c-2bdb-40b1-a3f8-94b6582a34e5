'use client';

import React, { useState, useEffect } from 'react';
import { Clock, ShieldOff, Calendar } from 'lucide-react';
import { availabilityService } from '../../services/availability.service';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Modal, ModalHeader, ModalBody, ModalFooter } from '../ui/modal';

interface UnblockModalProps {
  isOpen: boolean;
  onClose: () => void;
  roomId: string;
  date: string;
  startTime: string;
  endTime: string;
  overrideId: string;
  overrideReason?: string;
  onSuccess?: () => void;
}

const UNBLOCK_OPTIONS = [
  { value: 'full', label: 'Bỏ chặn toàn bộ khung giờ' },
  {
    value: 'partial',
    label: 'Bỏ chặn một phần (tùy chỉnh thời gian kết thúc)',
  },
];

// Generate time options for partial unblock (30-minute intervals)
const generateTimeOptions = (
  startTime: string,
  originalEndTime: string
): string[] => {
  const options = [];
  const start = new Date(`2024-01-01T${startTime}:00`);
  const originalEnd = new Date(`2024-01-01T${originalEndTime}:00`);

  let current = new Date(start);
  current.setMinutes(current.getMinutes() + 30); // Start from 30 minutes after start time

  while (current <= originalEnd) {
    const timeStr = current.toTimeString().slice(0, 5);
    options.push(timeStr);
    current.setMinutes(current.getMinutes() + 30);
  }

  return options;
};

export const UnblockModal: React.FC<UnblockModalProps> = ({
  isOpen,
  onClose,
  roomId,
  date,
  startTime,
  endTime,
  overrideId,
  overrideReason,
  onSuccess,
}) => {
  const [unblockOption, setUnblockOption] = useState<string>('full');
  const [customEndTime, setCustomEndTime] = useState<string>(endTime);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeOptions, setTimeOptions] = useState<string[]>([]);

  useEffect(() => {
    if (isOpen) {
      const options = generateTimeOptions(startTime, endTime);
      setTimeOptions(options);
      setCustomEndTime(endTime);
    }
  }, [isOpen, startTime, endTime]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (unblockOption === 'full') {
        // Full unblock - delete the override completely
        await availabilityService.deleteOverride(overrideId);
      } else {
        // Partial unblock - delete the original override and create a new one with updated time
        await availabilityService.deleteOverride(overrideId);

        // Create new override for the remaining blocked time
        if (customEndTime !== endTime) {
          const newStartDateTime = `${date}T${customEndTime}:00`;
          const newEndDateTime = `${date}T${endTime}:00`;

          await availabilityService.createOverride({
            roomId,
            date,
            startTime: newStartDateTime,
            endTime: newEndDateTime,
            isAvailable: false,
            reason: overrideReason || 'Chặn một phần sau khi bỏ chặn',
          });
        }
      }

      if (onSuccess) {
        onSuccess();
      }

      onClose();
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Lỗi khi bỏ chặn khung giờ';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setUnblockOption('full');
      setCustomEndTime(endTime);
      setError(null);
      onClose();
    }
  };

  const formatDate = (dateStr: string): string => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('vi-VN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getUnblockTimeLabel = (): string => {
    if (unblockOption === 'full') {
      return `${startTime} - ${endTime} (Toàn bộ khung giờ)`;
    } else {
      return `${startTime} - ${customEndTime} (Bỏ chặn một phần)`;
    }
  };

  const getCurrentUnblockOptionLabel = (): string => {
    const option = UNBLOCK_OPTIONS.find(opt => opt.value === unblockOption);
    return option ? option.label : 'Chọn tùy chọn bỏ chặn';
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="md">
      <ModalHeader>
        <div className="flex items-center gap-2">
          <ShieldOff className="h-5 w-5 text-green-500" />
          <h2 className="text-h5 font-semibold text-gray-900">
            Bỏ Chặn Khung Giờ
          </h2>
        </div>
        <p className="text-sm text-gray-600 mt-2">
          Chọn cách thức bỏ chặn khung giờ này để cho phép khách hàng đặt phòng.
        </p>
      </ModalHeader>

      <ModalBody>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Time Slot Info */}
          <div className="bg-gray-50 p-4 rounded-md">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="font-medium text-gray-900">
                Thông tin khung giờ bị chặn
              </span>
            </div>
            <div className="text-sm text-gray-600">
              <p>
                <strong>Ngày:</strong> {formatDate(date)}
              </p>
              <p>
                <strong>Thời gian bị chặn:</strong> {startTime} - {endTime}
              </p>
              {overrideReason && (
                <p>
                  <strong>Lý do chặn:</strong> {overrideReason}
                </p>
              )}
            </div>
          </div>

          {/* Unblock Option */}
          <div className="space-y-2">
            <Label htmlFor="unblock-option">Tùy chọn bỏ chặn</Label>
            <Select value={unblockOption} onValueChange={setUnblockOption}>
              <SelectTrigger>
                <span className="text-black">
                  {getCurrentUnblockOptionLabel()}
                </span>
              </SelectTrigger>
              <SelectContent>
                {UNBLOCK_OPTIONS.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Custom End Time for Partial Unblock */}
          {unblockOption === 'partial' && (
            <div className="space-y-2">
              <Label htmlFor="custom-end-time">Bỏ chặn đến thời gian</Label>
              <div className="relative">
                <Select value={customEndTime} onValueChange={setCustomEndTime}>
                  <SelectTrigger className="pl-10">
                    <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <SelectValue placeholder="Chọn thời gian kết thúc mới" />
                  </SelectTrigger>
                  <SelectContent>
                    {timeOptions.length === 0 ? (
                      <div className="py-2 px-4 text-sm text-gray-500">
                        Không có tùy chọn thời gian khả dụng
                      </div>
                    ) : (
                      timeOptions.map(time => (
                        <SelectItem key={time} value={time}>
                          <span className="text-black">{time}</span>
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              <p className="text-xs text-gray-500">
                Khung giờ từ {startTime} đến {customEndTime} sẽ được bỏ chặn.
                {customEndTime !== endTime && (
                  <span className="block mt-1">
                    Khung giờ từ {customEndTime} đến {endTime} sẽ vẫn bị chặn.
                  </span>
                )}
              </p>
            </div>
          )}

          {/* Preview */}
          <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
            <div className="flex items-center gap-2 mb-2">
              <Calendar className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-900">
                Xem trước thay đổi
              </span>
            </div>
            <div className="text-sm text-blue-800">
              <p>
                <strong>Sẽ bỏ chặn:</strong> {getUnblockTimeLabel()}
              </p>
              {unblockOption === 'partial' && customEndTime !== endTime && (
                <p>
                  <strong>Vẫn bị chặn:</strong> {customEndTime} - {endTime}
                </p>
              )}
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}
        </form>
      </ModalBody>

      <ModalFooter>
        <Button
          type="button"
          variant="outline"
          onClick={handleClose}
          disabled={loading}
        >
          Hủy
        </Button>
        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={
            loading ||
            (unblockOption === 'partial' && customEndTime === startTime)
          }
          className="bg-green-500 hover:bg-green-600 text-white"
        >
          {loading ? 'Đang bỏ chặn...' : 'Bỏ chặn khung giờ'}
        </Button>
      </ModalFooter>
    </Modal>
  );
};
