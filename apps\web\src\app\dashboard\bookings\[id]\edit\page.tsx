'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { UpdateBookingDto, BookingStatus } from 'shared-types';
import type { Booking as BookingDetailsDto } from 'shared-types';
import { bookingsService } from '../../../../../services/bookings.service';
import {
  Input,
  Button,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Label,
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from '../../../../../components/ui';
import { CardFooter } from '../../../../../components/ui/card';
import { useDialogs } from '../../../../../components/ui/modal-provider';
import { Loader2, Clock, Calendar } from 'lucide-react';

// Interface for form fields that may not be in UpdateBookingDto
interface BookingFormData extends Partial<UpdateBookingDto> {
  bookingDate?: string;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
}

// Helper to format date for input type="date"
const formatDateForInput = (isoDate?: string): string => {
  if (!isoDate) return '';
  try {
    return new Date(isoDate).toISOString().split('T')[0];
  } catch (e) {
    return '';
  }
};

// Helper to format time for select component (HH:MM)
const formatTimeForSelect = (isoDateTime?: string): string => {
  if (!isoDateTime) return '';
  try {
    const date = new Date(isoDateTime);
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  } catch (e) {
    return '';
  }
};

// Generate time options based on location's operating hours or fallback to default
const generateTimeOptions = (
  location: any,
  selectedDate?: string
): string[] => {
  // Fallback time options (6 AM to 11:30 PM in 30-minute intervals)
  const generateDefaultTimeOptions = () => {
    const options = [];
    for (let hour = 6; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        options.push(time);
      }
    }
    return options;
  };

  if (!location?.operatingHours || !selectedDate) {
    return generateDefaultTimeOptions();
  }

  try {
    const operatingHours =
      typeof location.operatingHours === 'string'
        ? JSON.parse(location.operatingHours)
        : location.operatingHours;

    const date = new Date(selectedDate);
    const dayNames = [
      'sunday',
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
    ];
    const dayName = dayNames[date.getDay()];

    const hoursForDay = operatingHours[dayName];
    if (!hoursForDay || !hoursForDay.includes('-')) {
      return [];
    }

    const [openTimeStr, closeTimeStr] = hoursForDay.split('-');
    const [openHour, openMinute] = openTimeStr.split(':').map(Number);
    const [closeHour, closeMinute] = closeTimeStr.split(':').map(Number);

    const options = [];
    let currentHour = openHour;
    let currentMinute = openMinute;

    const crossesMidnight =
      closeHour < openHour ||
      (closeHour === openHour && closeMinute <= openMinute);
    const endHour = crossesMidnight ? closeHour + 24 : closeHour;
    const endMinute = closeMinute;

    while (true) {
      const displayHour = currentHour >= 24 ? currentHour - 24 : currentHour;
      const timeStr = `${displayHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
      options.push(timeStr);

      const currentTotalMinutes = currentHour * 60 + currentMinute;
      const endTotalMinutes = endHour * 60 + endMinute;

      if (currentTotalMinutes >= endTotalMinutes) {
        break;
      }

      currentMinute += 30;
      if (currentMinute >= 60) {
        currentMinute = 0;
        currentHour++;
      }

      if (options.length > 48) {
        break;
      }
    }
    return options;
  } catch (error) {
    console.error('Error parsing operating hours:', error);
    return generateDefaultTimeOptions();
  }
};

const EditBookingPage = () => {
  const router = useRouter();
  const params = useParams();
  const bookingId = params.id as string;
  const { alert } = useDialogs();

  const [booking, setBooking] = useState<BookingDetailsDto | null>(null);
  const [formData, setFormData] = useState<BookingFormData>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [availableTimeOptions, setAvailableTimeOptions] = useState<string[]>(
    []
  );

  // Generate valid end time options based on start time
  const getValidEndTimeOptions = useCallback(
    (startTimeHM: string | null): string[] => {
      if (!startTimeHM || !availableTimeOptions.length) {
        return availableTimeOptions;
      }

      const startIndex = availableTimeOptions.indexOf(startTimeHM);
      if (startIndex === -1) {
        return availableTimeOptions;
      }

      // Return only times after the start time
      return availableTimeOptions.slice(startIndex + 1);
    },
    [availableTimeOptions]
  );

  // Helper function to create ISO string
  const createISOString = (dateStr: string, timeStr: string): string => {
    return `${dateStr}T${timeStr}:00`;
  };

  // Update available time options when booking is loaded or date changes
  useEffect(() => {
    if (booking && formData.bookingDate) {
      const newTimeOptions = generateTimeOptions(
        booking.location,
        formData.bookingDate
      );
      setAvailableTimeOptions(newTimeOptions);
    }
  }, [booking, formData.bookingDate]);

  // Fetch booking details
  useEffect(() => {
    if (bookingId) {
      setLoading(true);
      bookingsService
        .getBookingByIdAdmin(bookingId)
        .then(data => {
          // Extract date from startTime instead of using bookingDate
          const bookingDateFromStartTime = data.startTime
            ? data.startTime.split('T')[0]
            : undefined;

          setBooking(data);
          setFormData({
            bookingDate: bookingDateFromStartTime,
            startTime: data.startTime
              ? formatTimeForSelect(data.startTime)
              : undefined,
            endTime: data.endTime
              ? formatTimeForSelect(data.endTime)
              : undefined,
            numberOfGuests: data.numberOfGuests,
            status: data.status,
            notes: data.notes || '',
            customerName: data.user?.name || data.guestName || '',
            customerEmail: data.user?.email || data.guestEmail || '',
            customerPhone: data.user?.phoneNumber || data.guestPhone || '',
          });

          setLoading(false);
        })
        .catch(err => {
          console.error('Failed to fetch booking:', err);
          setError('Không thể tải chi tiết đặt phòng.');
          alert('Không thể tải chi tiết đặt phòng.', 'Lỗi', 'error');
          setLoading(false);
        });
    }
  }, [bookingId, alert]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: keyof UpdateBookingDto, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value as BookingStatus }));
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = e.target.value;
    setFormData(prev => ({
      ...prev,
      bookingDate: newDate,
      // Reset times when date changes
      startTime: '',
      endTime: '',
    }));
  };

  const handleStartTimeChange = (timeHM: string) => {
    setFormData(prev => {
      const newStartTime = timeHM;
      let newEndTime = prev.endTime || '';

      // Auto-set end time to 2 hours later if available
      const startIndex = availableTimeOptions.indexOf(timeHM);
      if (startIndex !== -1) {
        const typicalDurationSlots = 4; // 2 hours (30-min intervals)
        if (availableTimeOptions.length > startIndex + typicalDurationSlots) {
          newEndTime = availableTimeOptions[startIndex + typicalDurationSlots];
        } else if (availableTimeOptions.length > startIndex + 1) {
          newEndTime = availableTimeOptions[startIndex + 1];
        }
      }

      return { ...prev, startTime: newStartTime, endTime: newEndTime };
    });
  };

  const handleEndTimeChange = (timeHM: string) => {
    setFormData(prev => ({ ...prev, endTime: timeHM }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError('');

    const getIsoDateTime = (
      dateStr?: string,
      timeStr?: string
    ): string | undefined => {
      if (!dateStr || !timeStr) return undefined;
      return createISOString(dateStr, timeStr);
    };

    try {
      const updateDto: UpdateBookingDto = {
        status: formData.status as BookingStatus,
        notes: formData.notes || '',
        startTime: getIsoDateTime(formData.bookingDate, formData.startTime),
        endTime: getIsoDateTime(formData.bookingDate, formData.endTime),
        numberOfGuests: formData.numberOfGuests
          ? Number(formData.numberOfGuests)
          : undefined,
      };

      await bookingsService.comprehensivelyUpdateBookingAdmin(
        bookingId as string,
        updateDto
      );
      router.push('/dashboard/bookings');
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : 'Có lỗi xảy ra khi cập nhật đặt phòng'
      );
    } finally {
      setSaving(false);
    }
  };

  // Helper function to get current status display label
  const getCurrentStatusLabel = (): string => {
    if (!formData.status) return 'Chọn trạng thái';
    return bookingsService.getStatusLabel(formData.status as BookingStatus);
  };

  // Get current start time for filtering end times
  const validEndTimeOptions = getValidEndTimeOptions(
    formData.startTime || null
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="h-12 w-12 animate-spin text-gold-500" />
        <p className="ml-4 text-lg">Đang tải chi tiết đặt phòng...</p>
      </div>
    );
  }

  if (error && !booking) {
    return (
      <div className="container mx-auto p-4 text-center">
        <p className="text-red-500 text-xl mb-4">{error}</p>
        <Button
          onClick={() => router.push('/dashboard/bookings')}
          variant="outline"
        >
          Quay lại danh sách đặt phòng
        </Button>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="container mx-auto p-4 text-center">
        <p className="text-xl mb-4">Không tìm thấy đặt phòng.</p>
        <Button
          onClick={() => router.push('/dashboard/bookings')}
          variant="outline"
        >
          Quay lại danh sách đặt phòng
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8">
      <Card className="max-w-3xl mx-auto border-gray-200 shadow-lg">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <CardTitle className="text-2xl text-gray-700">
            Chỉnh sửa đặt phòng:{' '}
            <span className="text-gold-600 font-semibold">
              {booking.bookingReference || bookingId}
            </span>
          </CardTitle>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="p-6 space-y-6">
            {error && (
              <p className="text-red-500 bg-red-50 p-3 rounded-md">{error}</p>
            )}

            <h3 className="text-lg font-semibold text-gray-700 border-b pb-2">
              Thông tin khách hàng
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="customerName" className="text-gray-600">
                  Tên khách hàng
                </Label>
                <Input
                  id="customerName"
                  name="customerName"
                  value={formData.customerName || ''}
                  onChange={handleInputChange}
                  className="mt-1 text-black"
                  placeholder="Tên khách hàng"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Lưu ý: Thay đổi thông tin khách hàng chỉ hiển thị tại đây và
                  không cập nhật vào hệ thống
                </p>
              </div>
              <div>
                <Label htmlFor="customerEmail" className="text-gray-600">
                  Email
                </Label>
                <Input
                  id="customerEmail"
                  name="customerEmail"
                  type="email"
                  value={formData.customerEmail || ''}
                  onChange={handleInputChange}
                  className="mt-1 text-black"
                  placeholder="Email khách hàng"
                />
              </div>
              <div>
                <Label htmlFor="customerPhone" className="text-gray-600">
                  Số điện thoại
                </Label>
                <Input
                  id="customerPhone"
                  name="customerPhone"
                  value={formData.customerPhone || ''}
                  onChange={handleInputChange}
                  className="mt-1 text-black"
                  placeholder="Số điện thoại"
                />
              </div>
            </div>

            <h3 className="text-lg font-semibold text-gray-700 border-b pb-2 pt-4">
              Chi tiết đặt phòng
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="locationName" className="text-gray-600">
                  Cơ sở
                </Label>
                <Input
                  id="locationName"
                  value={
                    booking.location?.name?.vi ||
                    booking.location?.name?.en ||
                    'N/A'
                  }
                  disabled
                  className="mt-1 bg-gray-100"
                />
              </div>
              <div>
                <Label htmlFor="roomName" className="text-gray-600">
                  Phòng
                </Label>
                <Input
                  id="roomName"
                  value={
                    booking.room?.name?.vi || booking.room?.name?.en || 'N/A'
                  }
                  disabled
                  className="mt-1 bg-gray-100"
                />
              </div>
              <div>
                <Label htmlFor="bookingDate" className="text-gray-600">
                  Ngày đặt
                </Label>
                <div className="relative mt-1">
                  <Input
                    id="bookingDate"
                    name="bookingDate"
                    type="date"
                    value={formData.bookingDate || ''}
                    onChange={handleDateChange}
                    className="pl-10 text-black"
                  />
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                </div>
              </div>
              <div>
                <Label htmlFor="numberOfGuests" className="text-gray-600">
                  Số lượng khách
                </Label>
                <Input
                  id="numberOfGuests"
                  name="numberOfGuests"
                  type="number"
                  value={formData.numberOfGuests || ''}
                  onChange={handleInputChange}
                  className="mt-1 text-black"
                  min="1"
                />
              </div>
            </div>

            {/* Time Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startTime" className="text-gray-600">
                  Giờ bắt đầu
                </Label>
                <div className="relative mt-1">
                  <Select
                    value={formData.startTime || ''}
                    onValueChange={handleStartTimeChange}
                  >
                    <SelectTrigger className="pl-10 text-black">
                      <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <SelectValue placeholder="Chọn giờ bắt đầu" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableTimeOptions.length === 0 ? (
                        <div className="py-2 px-4 text-sm text-gray-500">
                          {booking.location
                            ? 'Ngày này không hoạt động'
                            : 'Vui lòng chọn ngày trước'}
                        </div>
                      ) : (
                        availableTimeOptions.map(time => (
                          <SelectItem key={`start-${time}`} value={time}>
                            <span className="text-black">{time}</span>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="endTime" className="text-gray-600">
                  Giờ kết thúc
                </Label>
                <div className="relative mt-1">
                  <Select
                    value={formData.endTime || ''}
                    onValueChange={handleEndTimeChange}
                  >
                    <SelectTrigger className="pl-10 text-black">
                      <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <SelectValue placeholder="Chọn giờ kết thúc" />
                    </SelectTrigger>
                    <SelectContent>
                      {!formData.startTime ? (
                        <div className="py-2 px-4 text-sm text-gray-500">
                          Vui lòng chọn giờ bắt đầu trước
                        </div>
                      ) : validEndTimeOptions.length === 0 ? (
                        <div className="py-2 px-4 text-sm text-gray-500">
                          Không có giờ kết thúc khả dụng
                        </div>
                      ) : (
                        validEndTimeOptions.map(time => (
                          <SelectItem key={`end-${time}`} value={time}>
                            <span className="text-black">{time}</span>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
              <div>
                <Label htmlFor="status" className="text-gray-600">
                  Trạng thái
                </Label>
                <Select
                  value={formData.status || ''}
                  onValueChange={value => handleSelectChange('status', value)}
                >
                  <SelectTrigger className="mt-1 text-black">
                    <span className="text-black">
                      {getCurrentStatusLabel()}
                    </span>
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(BookingStatus).map(s => (
                      <SelectItem key={s} value={s}>
                        <span className="text-black">
                          {bookingsService.getStatusLabel(s)}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="totalPrice" className="text-gray-600">
                  Tổng tiền
                </Label>
                <Input
                  id="totalPrice"
                  name="totalPrice"
                  type="number"
                  value={booking.totalPrice}
                  disabled
                  className="mt-1 bg-gray-100"
                />
              </div>
            </div>

            <div className="pt-4">
              <Label htmlFor="notes" className="text-gray-900">
                Ghi chú của nhân viên
              </Label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes || ''}
                onChange={handleInputChange}
                className="w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md
                           focus:outline-none focus:ring-2 focus:ring-gold-500 
                           focus:border-transparent resize-vertical text-black bg-white mt-1"
                placeholder="Thêm ghi chú cho đặt phòng này..."
                rows={4}
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-end gap-3 bg-gray-50 border-t border-gray-200 p-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={saving}
              className="hover:bg-gray-100"
            >
              Hủy
            </Button>
            <Button
              type="submit"
              disabled={saving}
              className="bg-gold-500 hover:bg-gold-600 text-white min-w-[120px]"
            >
              {saving ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              {saving ? 'Đang lưu...' : 'Lưu thay đổi'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
};

export default EditBookingPage;
