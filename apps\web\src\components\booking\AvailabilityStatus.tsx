'use client';

import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  AlertTriangle,
  Clock,
  Wifi,
  RefreshCw,
  Info,
  ArrowRight,
} from 'lucide-react';
import { Button } from '../ui/button';
import type {
  AvailabilityState,
  AlternativeTimeSlot,
} from '../../hooks/useAvailabilityCheck';
import type { RoomAvailabilityResponseDto } from 'shared-types';

interface AvailabilityStatusProps {
  availability: AvailabilityState;
  onRetry?: () => void;
  onSelectAlternative?: (startTime: string, endTime: string) => void;
  className?: string;
}

// Skeleton component for loading states
const SkeletonLine: React.FC<{ width?: string; height?: string }> = ({
  width = 'w-full',
  height = 'h-4',
}) => (
  <div className={`${width} ${height} bg-gray-200 rounded animate-pulse`} />
);

// Enhanced loading spinner
const LoadingSpinner: React.FC<{
  size?: 'sm' | 'md' | 'lg';
  color?: string;
}> = ({ size = 'md', color = 'border-gold-600' }) => {
  const sizeClasses = {
    sm: 'h-4 w-4 border-2',
    md: 'h-5 w-5 border-2',
    lg: 'h-6 w-6 border-[3px]',
  };

  return (
    <div className={`relative ${sizeClasses[size]}`}>
      <div
        className={`animate-spin rounded-full ${sizeClasses[size]} border-gray-200`}
      >
        <div
          className={`absolute inset-0 rounded-full ${sizeClasses[size]} ${color} border-t-transparent animate-spin`}
        ></div>
      </div>
    </div>
  );
};

export const AvailabilityStatus: React.FC<AvailabilityStatusProps> = ({
  availability,
  onRetry,
  onSelectAlternative,
  className = '',
}) => {
  const { status, data, error, isChecking, alternatives, suggestionsLoading } =
    availability;

  const formatTime = (isoTime: string): string => {
    try {
      const date = new Date(isoTime);
      return date.toLocaleTimeString('vi-VN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });
    } catch {
      return isoTime;
    }
  };

  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  };

  const formatDuration = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;

    if (hours > 0 && mins > 0) {
      return `${hours} giờ ${mins} phút`;
    } else if (hours > 0) {
      return `${hours} giờ`;
    } else {
      return `${mins} phút`;
    }
  };

  const getReasonLabel = (reason?: string): string => {
    switch (reason) {
      case 'earlier_time':
        return 'Sớm hơn';
      case 'later_time':
        return 'Muộn hơn';
      default:
        return 'Khả dụng';
    }
  };

  const renderAlternativeSuggestions = () => {
    if (suggestionsLoading) {
      return (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mt-3 animate-fade-in">
          <div className="flex items-center gap-3 mb-3">
            <LoadingSpinner size="sm" color="border-blue-600" />
            <span className="text-sm text-blue-700 font-medium animate-pulse">
              Đang tìm thời gian thay thế...
            </span>
          </div>

          {/* Skeleton for suggestions being loaded */}
          <div className="space-y-2">
            {[1, 2, 3].map(index => (
              <div
                key={index}
                className="bg-white border border-blue-200 rounded-md p-3"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <SkeletonLine width="w-24" height="h-4" />
                      <SkeletonLine width="w-16" height="h-6" />
                    </div>
                    <div className="flex items-center gap-4">
                      <SkeletonLine width="w-16" height="h-3" />
                      <SkeletonLine width="w-20" height="h-3" />
                    </div>
                  </div>
                  <SkeletonLine width="w-16" height="h-8" />
                </div>
              </div>
            ))}
          </div>
        </div>
      );
    }

    if (alternatives.length === 0) {
      return (
        <div className="bg-amber-50 border border-amber-200 rounded-md p-3 mt-3 animate-fade-in">
          <div className="flex items-start gap-2">
            <Info className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-amber-700">
              <p className="font-medium">Không tìm thấy thời gian thay thế</p>
              <p className="mt-1">
                Vui lòng thử chọn ngày khác hoặc liên hệ để được hỗ trợ.
              </p>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mt-3 animate-fade-in">
        <div className="flex items-center gap-2 mb-3">
          <Clock className="w-4 h-4 text-blue-600" />
          <h5 className="font-medium text-blue-800">Thời gian gợi ý</h5>
        </div>

        <div className="space-y-2">
          {alternatives.map((slot, index) => (
            <div
              key={index}
              className={`suggestion-card flex items-center justify-between bg-white border border-blue-200 rounded-md p-3 hover:border-blue-300 hover:shadow-sm transition-all duration-200`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-medium text-gray-900">
                    {formatTime(slot.startTime)} - {formatTime(slot.endTime)}
                  </span>
                  <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">
                    {getReasonLabel(slot.reason)}
                  </span>
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <span>{formatDuration(slot.durationMinutes)}</span>
                  <span className="font-medium text-green-600">
                    {formatPrice(slot.price)}
                  </span>
                </div>
              </div>

              {onSelectAlternative && (
                <Button
                  size="sm"
                  onClick={() =>
                    onSelectAlternative(slot.startTime, slot.endTime)
                  }
                  className="bg-blue-600 hover:bg-blue-700 text-white ml-3 transition-colors duration-200"
                >
                  <ArrowRight className="w-4 h-4 mr-1" />
                  Chọn
                </Button>
              )}
            </div>
          ))}
        </div>

        <p className="text-xs text-blue-600 mt-3">
          💡 Chọn một thời gian phù hợp để tự động cập nhật form đặt phòng.
        </p>
      </div>
    );
  };

  const getStatusContent = () => {
    switch (status) {
      case 'checking':
        return (
          <div className="flex items-center gap-3 animate-fade-in">
            <LoadingSpinner size="md" color="border-gold-600" />
            <div>
              <span className="text-gold-700 font-medium">
                Đang kiểm tra tình trạng phòng...
              </span>
              <div className="text-xs text-gold-600 mt-1">
                Đang xác minh thời gian khả dụng
              </div>
            </div>
          </div>
        );

      case 'available':
        return (
          <div className="flex items-start gap-3 animate-fade-in">
            <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
            <div>
              <div className="flex items-center gap-2">
                <span className="font-medium text-green-700">Phòng có sẵn</span>
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              </div>
              <p className="text-sm text-green-600 mt-1">
                Thời gian này hiện đang trống. Bạn có thể tiếp tục đặt phòng.
              </p>
            </div>
          </div>
        );

      case 'unavailable':
        return (
          <div className="space-y-3 animate-fade-in">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div>
                <span className="font-medium text-amber-700">
                  Phòng không có sẵn
                </span>
                {data?.reason && (
                  <p className="text-sm text-amber-600 mt-1">
                    {getUnavailableReasonMessage(data.reason)}
                  </p>
                )}
              </div>
            </div>
            {/* Show alternative suggestions only when unavailable */}
            {renderAlternativeSuggestions()}
          </div>
        );

      case 'error':
        return (
          <div className="space-y-3 animate-fade-in">
            <div className="flex items-start gap-3">
              <Wifi className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <span className="font-medium text-red-700">
                  Không thể kiểm tra tình trạng phòng
                </span>
                <p className="text-sm text-red-600 mt-1">
                  {error || 'Đã xảy ra lỗi khi kiểm tra. Vui lòng thử lại.'}
                </p>
              </div>
            </div>

            {onRetry && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRetry}
                className="border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400 transition-colors duration-200"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Thử lại
              </Button>
            )}

            <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
              <div className="flex items-start gap-2">
                <Info className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-blue-700">
                  <p className="font-medium">Bạn vẫn có thể tiếp tục:</p>
                  <p>Đặt phòng và chúng tôi sẽ xác nhận lại tình trạng sau.</p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const getUnavailableReasonMessage = (reason: string): string => {
    if (
      reason.toLowerCase().includes('booked') ||
      reason.toLowerCase().includes('đã được đặt')
    ) {
      return 'Thời gian này đã có khách đặt trước.';
    }

    if (
      reason.toLowerCase().includes('override') ||
      reason.toLowerCase().includes('unavailable due to')
    ) {
      return 'Phòng đang được bảo trì hoặc có sự kiện đặc biệt.';
    }

    if (
      reason.toLowerCase().includes('operating hours') ||
      reason.toLowerCase().includes('not open')
    ) {
      return 'Thời gian này nằm ngoài giờ hoạt động của cơ sở.';
    }

    return reason;
  };

  const getContainerClasses = () => {
    const baseClasses =
      'rounded-lg p-4 border transition-all duration-300 ease-in-out';

    switch (status) {
      case 'checking':
        return `${baseClasses} bg-gradient-to-r from-gold-50 to-gold-100 border-gold-200 shadow-sm`;
      case 'available':
        return `${baseClasses} bg-gradient-to-r from-green-50 to-green-100 border-green-200 shadow-sm`;
      case 'unavailable':
        return `${baseClasses} bg-gradient-to-r from-amber-50 to-amber-100 border-amber-200 shadow-sm`;
      case 'error':
        return `${baseClasses} bg-gradient-to-r from-red-50 to-red-100 border-red-200 shadow-sm`;
      default:
        return `${baseClasses} bg-gray-50 border-gray-200`;
    }
  };

  // Render placeholder skeleton when idle to prevent CLS
  if (status === 'idle') {
    return (
      <div
        className={`rounded-lg p-4 border border-gray-200 bg-gray-50 ${className} min-h-[80px] opacity-50`}
      >
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-900">
            Trạng thái phòng
          </h4>
          <div className="flex items-center gap-3">
            <div className="w-5 h-5 bg-gray-200 rounded animate-pulse"></div>
            <div className="space-y-2 flex-1">
              <SkeletonLine width="w-48" height="h-4" />
              <SkeletonLine width="w-64" height="h-3" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`${getContainerClasses()} ${className} min-h-[80px]`}>
      {/* Accessibility announcements */}
      <div
        role="status"
        aria-live="polite"
        aria-atomic="true"
        className="sr-only"
      >
        {status === 'checking' && 'Đang kiểm tra tình trạng phòng'}
        {status === 'available' && 'Phòng có sẵn cho thời gian đã chọn'}
        {status === 'unavailable' &&
          `Phòng không có sẵn. ${data?.reason || ''}`}
        {status === 'error' && `Lỗi kiểm tra tình trạng phòng. ${error || ''}`}
      </div>

      {/* Visual content */}
      <div className="space-y-1">
        <h4 className="text-sm font-medium text-gray-900 mb-3">
          Trạng thái phòng
        </h4>
        {getStatusContent()}
      </div>
    </div>
  );
};
