# Queen Karaoke Booking System - CI/CD Pipeline

This document outlines the Continuous Integration and Continuous Deployment (CI/CD) pipeline for the Queen Karaoke Booking System. The pipeline will be implemented using GitHub Actions (or GitLab CI as an alternative).

## Goals

- **Automation:** Automate testing, building, and deployment processes.
- **Consistency:** Ensure consistent environments and build processes.
- **Early Feedback:** Provide quick feedback to developers on code changes.
- **Reliability:** Reduce manual errors in deployment.
- **Speed:** Accelerate the delivery of new features and fixes.

## Pipeline Stages & Triggers

**Triggers:**

- **Push to `main`/`master` branch:** Triggers the full CI/CD pipeline for production deployment (or staging first, then manual promotion to production).
- **Push to `develop` branch:** Triggers the CI pipeline and deployment to a staging/development environment.
- **Pull Request to `main`/`develop`:** Triggers the CI pipeline (lint, type check, tests) to validate changes before merging.

**Pipeline Stages:**

1.  **Lint & Format Check:** (Triggered on PRs, pushes to develop/main)

    - **Action:** Run ESLint and <PERSON><PERSON><PERSON> to check for code style and formatting issues across all workspaces (`apps/api`, `apps/web`, `packages/shared-types`). Potentially include checks for i18n key completeness or consistency if tools allow.
    - **Tools:** ESLint, Prettier, (optional: i18n linting tools).
    - **Command (root):** `npm run lint`, `npm run format` (check mode, e.g., `prettier --check ...`).
    - **Outcome:** Fail the pipeline if linting or formatting errors are found.

2.  **Type Checking:** (Triggered on PRs, pushes to develop/main)

    - **Action:** Run TypeScript compiler (`tsc`) to check for type errors in all workspaces.
    - **Tools:** TypeScript (`tsc`).
    - **Command (root):** `npm run build:shared` (which runs `tsc` for shared-types), then type check for `api` and `web` (e.g., `npm run typecheck --workspace=api`, `npm run typecheck --workspace=web` - these scripts would run `tsc --noEmit`).
    - **Outcome:** Fail the pipeline if type errors are found.

3.  **Unit & Integration Tests:** (Triggered on PRs, pushes to develop/main)

    - **Action:** Execute unit and integration tests for backend (`apps/api`) and frontend (`apps/web`).
    - **Tools:**
      - BE (`apps/api`): Jest (NestJS default), Supertest for HTTP integration tests.
      - FE (`apps/web`): Jest, React Testing Library.
    - **Commands (workspace-specific):**
      - `npm test --workspace=api`
      - `npm test --workspace=web`
    - **Database for Tests:** Use a separate test database, possibly spun up via Docker within the CI environment or using an in-memory alternative if suitable (e.g., SQLite for some BE tests if Prisma supports it easily for non-critical tests).
    - **Outcome:** Fail the pipeline if any tests fail. Generate and store test coverage reports.

4.  **Build Applications:** (Triggered on pushes to develop/main, and after PR merge if deploying PRs directly to preview envs)

    - **Action:** Build the production-ready artifacts for `shared-types`, `apps/api`, and `apps/web`.
    - **Tools:** `tsc` (for shared-types), `nest build` (for api), `next build` (for web).
    - **Command (root):** `npm run build-all` (which is `npm run build:shared && npm run build:api && npm run build:web`).
    - **Outcome:** Store build artifacts (e.g., `dist` folders, `.next` folder) for the next stage.

5.  **Create Docker Images:** (Triggered on pushes to develop/main)

    - **Action:** Build Docker images for the `api` and `web` applications using their respective `Dockerfiles`.
    - **Tools:** Docker.
    - **Context:** Use the monorepo root as Docker build context to allow `COPY` from `packages/shared-types` if needed, or ensure build artifacts are correctly placed for Docker context within each app.
    - **Commands:**
      - `docker build -t queen-karaoke-api:$GIT_SHA -f apps/api/Dockerfile .`
      - `docker build -t queen-karaoke-web:$GIT_SHA -f apps/web/Dockerfile .`
      - (Tag images with Git commit SHA or version number).
    - **Outcome:** Docker images are built and ready for pushing to a registry.

6.  **Push Docker Images to Registry:** (Triggered on pushes to develop/main)

    - **Action:** Push the newly built Docker images to a container registry (e.g., Docker Hub, GitHub Container Registry, AWS ECR, GitLab Container Registry).
    - **Tools:** Docker CLI.
    - **Authentication:** Securely log in to the Docker registry using secrets.
    - **Commands:**
      - `docker login -u $DOCKER_USER -p $DOCKER_PASSWORD registry.example.com`
      - `docker push queen-karaoke-api:$GIT_SHA`
      - `docker push queen-karaoke-web:$GIT_SHA`
      - Optionally, also tag with `latest` or branch name (e.g., `latest-develop`).
    - **Outcome:** Images are available in the registry for deployment.

7.  **Deploy to Staging Environment:** (Triggered on pushes to `develop` branch)

    - **Action:** Deploy the new Docker images to the staging VPS.
    - **Method:** SSH into the staging server, pull the new images, and restart services using `docker-compose`.
    - **Tools:** SSH, Docker Compose.
    - **Steps:**
      1.  `ssh user@staging-server "cd /path/to/app && docker-compose pull && docker-compose up -d --force-recreate api web"` (Ensure `docker-compose.yml` on server uses the correct image tags, possibly passed as environment variables or updated via a config management step).
      2.  Run database migrations (if any): `ssh user@staging-server "cd /path/to/app && npm run prisma:migrate:deploy --workspace=api"` (This assumes `prisma migrate deploy` is suitable and an appropriate script exists).
      3.  Perform health checks.
    - **Outcome:** Staging environment is updated with the latest changes from the `develop` branch.

8.  **Deploy to Production Environment:** (Triggered on pushes/merges to `main` branch, potentially after manual approval from staging)
    - **Action:** Deploy the new Docker images to the production VPS.
    - **Method:** Similar to staging deployment, but with more caution (e.g., blue/green deployment strategy if feasible, though simpler recreate is common for smaller setups).
    - **Steps:**
      1.  (Optional) Backup database.
      2.  `ssh user@production-server "cd /path/to/app && docker-compose pull && docker-compose up -d --force-recreate api web"`
      3.  Run database migrations: `ssh user@production-server "cd /path/to/app && npm run prisma:migrate:deploy --workspace=api"`.
      4.  Perform health checks and monitoring.
    - **Outcome:** Production environment is updated.

## GitHub Actions Workflow Example (`.github/workflows/main.yml` - Simplified)

```yaml
name: CI/CD Pipeline

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main
      - develop

jobs:
  lint-type-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18' # Or your project's version
          cache: 'npm' # or 'yarn'

      - name: Install dependencies (root and workspaces)
        run: npm install # or yarn install

      - name: Lint
        run: npm run lint

      # - name: Format Check # Usually done locally or via pre-commit hooks, but can be added
      #   run: npm run format:check

      - name: Type Check shared-types
        run: npm run build --workspace=shared-types # This includes tsc

      - name: Type Check API
        run: npm run typecheck --workspace=api # Assumes: tsc --noEmit -p apps/api/tsconfig.json

      - name: Type Check Web
        run: npm run typecheck --workspace=web # Assumes: tsc --noEmit -p apps/web/tsconfig.json

      # Add steps for setting up test DB if needed
      - name: Run API Tests
        run: npm test --workspace=api

      - name: Run Web Tests
        run: npm test --workspace=web

  build-and-push-images:
    needs: lint-type-test
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm install

      - name: Build all applications
        run: npm run build-all

      - name: Log in to Docker Hub # Replace with your registry
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_TOKEN }}

      - name: Get commit SHA
        id: vars
        run: echo \"::set-output name=sha_short::$(git rev-parse --short HEAD)\"

      - name: Build and push API image
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./apps/api/Dockerfile
          push: true
          tags: yourdockerusername/queen-karaoke-api:${{ steps.vars.outputs.sha_short }},yourdockerusername/queen-karaoke-api:latest-${{ github.ref_name }}

      - name: Build and push Web image
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./apps/web/Dockerfile
          push: true
          tags: yourdockerusername/queen-karaoke-web:${{ steps.vars.outputs.sha_short }},yourdockerusername/queen-karaoke-web:latest-${{ github.ref_name }}

  deploy-staging:
    needs: build-and-push-images
    if: github.event_name == 'push' && github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Staging Server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.STAGING_SSH_HOST }}
          username: ${{ secrets.STAGING_SSH_USER }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          script: |
            cd /home/<USER>/queen-booking-system
            echo "API_IMAGE_TAG=latest-develop" > .env.staging.api
            echo "WEB_IMAGE_TAG=latest-develop" > .env.staging.web
            # Update docker-compose.yml to use these tags from .env files
            docker-compose -f docker-compose.yml -f docker-compose.staging.yml pull # Assuming a staging override file
            docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d --force-recreate api web
            # Potentially source .env for DATABASE_URL for migrations if not in docker-compose context
            docker-compose -f docker-compose.yml -f docker-compose.staging.yml exec -T api npm run prisma:migrate:deploy

  deploy-production:
    needs: build-and-push-images
    if: github.event_name == 'push' && github.ref == 'refs/heads/main' # Potentially add manual approval step
    runs-on: ubuntu-latest
    environment: production # Optional: for manual approvals and secrets
    steps:
      - name: Deploy to Production Server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.PROD_SSH_HOST }}
          username: ${{ secrets.PROD_SSH_USER }}
          key: ${{ secrets.PROD_SSH_KEY }}
          script: |
            cd /home/<USER>/queen-booking-system
            echo "API_IMAGE_TAG=latest-main" > .env.prod.api
            echo "WEB_IMAGE_TAG=latest-main" > .env.prod.web
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml pull
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --force-recreate api web
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml exec -T api npm run prisma:migrate:deploy
```

**Notes on Workflow:**

- Secrets (`DOCKER_HUB_USERNAME`, `DOCKER_HUB_TOKEN`, `STAGING_SSH_HOST`, etc.) must be configured in GitHub repository secrets.
- The Docker image tagging strategy uses both commit SHA (for specific versions) and `latest-<branch>` (for easy reference to the latest build of a branch).
- The deployment steps assume a `docker-compose.yml` file is present on the server and might use override files (e.g., `docker-compose.staging.yml`) for environment-specific configurations.
- Database migrations are run after service deployment.
- Health checks should be integrated post-deployment to ensure services are running correctly.

This CI/CD pipeline provides a robust framework for automating the development lifecycle of the Queen Karaoke Booking System.
