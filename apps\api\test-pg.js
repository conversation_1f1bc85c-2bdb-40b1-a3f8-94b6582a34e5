const { Client } = require('pg');

const client = new Client({
  user: 'queen',
  host: '127.0.0.1',
  database: 'queendb',
  password: 'queenpass',
  port: 5432,
});

async function testConnection() {
  try {
    console.log('Attempting to connect to PostgreSQL via node-postgres...');
    await client.connect();
    console.log('Connected successfully to PostgreSQL via node-postgres!');
    const res = await client.query('SELECT NOW()');
    console.log('Query result:', res.rows[0]);
  } catch (err) {
    console.error('Connection error (node-postgres):', err.message);
    console.error(err.stack);
  } finally {
    if (client._connected) {
      // Check if client was connected before trying to end
      await client.end();
      console.log('Connection closed (node-postgres).');
    } else {
      console.log(
        'Client was not connected, no need to close (node-postgres).',
      );
    }
  }
}

testConnection();
