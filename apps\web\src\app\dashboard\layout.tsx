'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '../../stores/auth-store';
import { UserRole } from 'shared-types';
import AdminSidebar from '../../components/dashboard/AdminSidebar';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const { user, isLoading, isInitialized } = useAuthStore();
  const router = useRouter();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  useEffect(() => {
    // Only check auth after the store has been initialized
    if (isInitialized && !isLoading) {
      if (!user) {
        router.push('/auth/login');
        return;
      }

      // Check if user has admin privileges
      if (
        !user.role ||
        ![UserRole.ADMIN, UserRole.SUPER_ADMIN].includes(user.role)
      ) {
        router.push('/'); // Redirect to home page for non-admin users
        return;
      }
    }
  }, [user, isLoading, isInitialized, router]);

  const handleToggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // Show loading state while initializing or checking authentication
  if (!isInitialized || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gold-50 to-white flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 border-4 border-gold-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="text-gold-700 font-medium">Loading admin panel...</p>
        </div>
      </div>
    );
  }

  // Show nothing while redirecting (after initialization)
  if (!user || ![UserRole.ADMIN, UserRole.SUPER_ADMIN].includes(user.role)) {
    return null;
  }

  return (
    <div className="h-screen bg-gradient-to-br from-gold-50 via-white to-gray-50 overflow-hidden">
      {/* Admin Sidebar */}
      <div className="fixed inset-y-0 left-0 z-50 lg:block hidden">
        <AdminSidebar
          isCollapsed={sidebarCollapsed}
          onToggleCollapse={handleToggleSidebar}
        />
      </div>

      {/* Main Content Area */}
      <div
        className={`h-screen flex flex-col transition-all duration-300 ${sidebarCollapsed ? 'lg:pl-16' : 'lg:pl-64'}`}
      >
        {/* Page Content */}
        <main className="flex-1 py-8 px-4 sm:px-6 lg:px-8 overflow-y-auto">
          <div className="max-w-7xl mx-auto">{children}</div>
        </main>
      </div>

      {/* Mobile sidebar overlay */}
      <div className="lg:hidden">
        {/* Mobile sidebar implementation would go here */}
        {/* For now, we'll keep the desktop-only implementation */}
      </div>
    </div>
  );
}
