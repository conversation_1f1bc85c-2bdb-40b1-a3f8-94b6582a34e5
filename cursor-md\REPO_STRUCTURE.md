# Queen Karaoke Booking System - Repository Structure & Initialization\n\nThis document outlines the monorepo structure, initialization commands, and TypeScript configuration for the project.\n\n## 1. Monorepo Layout\n\nThe project will use an npm/yarn workspace-based monorepo structure.\n\n`\nqueen-booking-system/\n├── apps/\n│   ├── api/            # NestJS Backend Application\n│   │   ├── src/\n│   │   ├── test/\n│   │   ├── Dockerfile\n│   │   ├── nest-cli.json\n│   │   ├── package.json\n│   │   └── tsconfig.json\n│   └── web/            # Next.js Frontend Application\n│       ├── app/          # Next.js App Router\n│       ├── components/\n│       ├── public/\n│       │   └── locales/  # Translation files (e.g., en.json, vi.json)\n│       ├── services/     # Or lib/ or utils/ for helper functions, API calls\n│       ├── store/        # State management (e.g., Zustand)\n│       ├── styles/       # Global styles, Tailwind base\n│       ├── types/        # Frontend specific types\n│       ├── Dockerfile\n│       ├── next.config.js\n│       ├── package.json\n│       └── tsconfig.json\n├── packages/\n│   └── shared-types/   # Shared TypeScript types/interfaces/enums\n│       ├── src/\n│       │   ├── index.ts\n│       │   └── common.types.ts // e.g., for LocalizedString type { vi: string; en: string; }\n│       ├── package.json\n│       └── tsconfig.json\n├── cursor-md/          # Project documentation (Markdown files)\n│   ├── PROJECT_OVERVIEW.md\n│   ├── USER_STORIES.md\n│   ├── API_SPEC.md\n│   ├── DB_SCHEMA.md\n│   ├── DEV_ROADMAP.md\n│   ├── REPO_STRUCTURE.md\n│   ├── CI_CD_PIPELINE.md\n│   ├── SECURITY_GUIDE.md\n│   └── DEPLOYMENT.md\n├── .dockerignore\n├── .editorconfig\n├── .eslintignore\n├── .eslintrc.js        # Root ESLint config (can be extended by apps)\n├── .gitignore\n├── .prettierignore\n├── .prettierrc.js      # Root Prettier config\n├── docker-compose.yml  # Docker Compose for local development and production\n├── package.json        # Root package.json for monorepo workspace config\n├── README.md\n└── tsconfig.base.json  # Base tsconfig for path aliases and shared settings\n`\n\n## 2. Initialization Scripts & Commands\n\n**Assumptions:**\n* Node.js, npm/yarn, Docker, Docker Compose are installed.\n* NestJS CLI and Create Next App are available (can be run via `npx`).\n\n**Steps:**\n\n1. **Initialize Root Monorepo (`queen-booking-system/package.json`):**\n\n Using **npm**: \n `bash\n    mkdir queen-booking-system\n    cd queen-booking-system\n    npm init -y\n    # Edit package.json to include workspaces:\n    `\n `json\n    // queen-booking-system/package.json\n    {\n      \"name\": \"queen-booking-system-monorepo\",\n      \"version\": \"1.0.0\",\n      \"private\": true,\n      \"workspaces\": [\n        \"apps/*\",\n        \"packages/*\"\n      ],\n      \"scripts\": {\n        \"dev:api\": \"npm run dev --workspace=api\",\n        \"dev:web\": \"npm run dev --workspace=web\",\n        \"build:api\": \"npm run build --workspace=api\",\n        \"build:web\": \"npm run build --workspace=web\",\n        \"build:shared\": \"npm run build --workspace=shared-types\",\n        \"build-all\": \"npm run build:shared && npm run build:api && npm run build:web\",\n        \"start:api\": \"npm run start:prod --workspace=api\",\n        \"start:web\": \"npm run start --workspace=web\", \n        \"start-all:docker\": \"docker-compose up -d\", // For production-like start\n        \"stop-all:docker\": \"docker-compose down\",\n        \"lint\": \"eslint . --ext .ts,.tsx,.js\",\n        \"lint:fix\": \"eslint . --ext .ts,.tsx,.js --fix\",\n        \"format\": \"prettier --write \\\"**/*.{ts,tsx,js,json,md}\\\"\",\n        \"prisma:generate\": \"npm run prisma:generate --workspace=api\",\n        \"prisma:migrate:dev\": \"npm run prisma:migrate:dev --workspace=api\",\n        \"prisma:studio\": \"npm run prisma:studio --workspace=api\"\n      }\n      // Add devDependencies like eslint, prettier, typescript at the root\n    }\n    `\n\n Using **Yarn**: (Recommended for workspaces)\n `bash\n    mkdir queen-booking-system\n    cd queen-booking-system\n    yarn init -y\n    # Edit package.json to include workspaces:\n    `\n `json\n    // queen-booking-system/package.json\n    {\n      \"name\": \"queen-booking-system-monorepo\",\n      \"version\": \"1.0.0\",\n      \"private\": true,\n      \"workspaces\": [\n        \"apps/*\",\n        \"packages/*\"\n      ],\n      \"scripts\": {\n        \"dev:api\": \"yarn workspace api dev\",\n        \"dev:web\": \"yarn workspace web dev\",\n        \"build:api\": \"yarn workspace api build\",\n        \"build:web\": \"yarn workspace web build\",\n        \"build:shared\": \"yarn workspace shared-types build\",\n        \"build-all\": \"yarn build:shared && yarn build:api && yarn build:web\",\n        \"start:api\": \"yarn workspace api start:prod\",\n        \"start:web\": \"yarn workspace web start\",\n        \"start-all:docker\": \"docker-compose up -d\",\n        \"stop-all:docker\": \"docker-compose down\",\n        \"lint\": \"eslint . --ext .ts,.tsx,.js\",\n        \"lint:fix\": \"eslint . --ext .ts,.tsx,.js --fix\",\n        \"format\": \"prettier --write \\\"**/*.{ts,tsx,js,json,md}\\\"\",\n        \"prisma:generate\": \"yarn workspace api prisma:generate\",\n        \"prisma:migrate:dev\": \"yarn workspace api prisma:migrate:dev\",\n        \"prisma:studio\": \"yarn workspace api prisma:studio\"\n      }\n      // Add devDependencies like eslint, prettier, typescript at the root\n    }\n    `\n\n2. **Create `apps/api` (NestJS + Prisma):**\n\n `bash\n    # From root directory (queen-booking-system)\n    npx @nestjs/cli new apps/api --skip-git --package-manager npm # or yarn\n    cd apps/api\n\n    # Add Prisma (example using npm, adapt for yarn if chosen)\n    npm install prisma --save-dev\n    npm install @prisma/client\n    npx prisma init --datasource-provider postgresql\n    # Configure .env with DATABASE_URL (e.g., postgresql://user:password@localhost:5432/queendb?schema=public)\n    # Edit prisma/schema.prisma with initial models\n    npx prisma generate\n    npx prisma migrate dev --name init\n\n    # Add scripts to apps/api/package.json for Prisma:\n    # \"prisma:generate\": \"prisma generate\",\n    # \"prisma:migrate:dev\": \"prisma migrate dev\",\n    # \"prisma:studio\": \"prisma studio\"\n    # \"build\": \"nest build\", (Ensure this uses tsconfig-paths for monorepo paths)\n    cd ../.. # Back to root\n    `\n\n3. **Create `apps/web` (Next.js + Tailwind CSS):**\n\n ``bash\n    # From root directory (queen-booking-system)\n    npx create-next-app@latest apps/web --typescript --eslint --tailwind --src-dir --app --use-npm # or --use-yarn\n    # Follow prompts, e.g., `Would you like to use \`src/\` directory with this project? Yes`\n    # `Would you like to use App Router? Yes`\n    # `Would you like to customize the default import alias? (@/*) Yes`\n    cd apps/web\n    # Additional setup for Tailwind as per project guidelines (e.g. shadcn/ui if used)\n    cd ../.. # Back to root\n    ``\n\n4. **Create `packages/shared-types`:**\n\n `bash\n    # From root directory (queen-booking-system)\n    mkdir -p packages/shared-types/src\n    cd packages/shared-types\n    npm init -y # or yarn init -y\n    # Edit package.json\n    `\n `json\n    // packages/shared-types/package.json\n    {\n      \"name\": \"shared-types\",\n      \"version\": \"1.0.0\",\n      \"main\": \"dist/index.js\", // Output from tsc\n      \"types\": \"dist/index.d.ts\", // Type definitions\n      \"scripts\": {\n        \"build\": \"tsc -p .\",\n        \"dev\": \"tsc -p . --watch\"\n      },\n      \"devDependencies\": {\n        \"typescript\": \"^5.x.x\" // Match root version\n      }\n    }\n    `\n Create `packages/shared-types/tsconfig.json`:\n `json\n    {\n      \"extends\": \"../../tsconfig.base.json\", // Extend from root base\n      \"compilerOptions\": {\n        \"outDir\": \"./dist\",\n        \"rootDir\": \"./src\",\n        \"composite\": true, // Important for project references\n        \"declaration\": true,\n        \"declarationMap\": true,\n        \"sourceMap\": true\n      },\n      \"include\": [\"src/**/*\"],\n      \"exclude\": [\"node_modules\", \"dist\"]\n    }\n    `\n Create a sample file `packages/shared-types/src/index.ts` (e.g., `export type TestType = string;`)\n cd ../.. # Back to root\n\n5. **Install Root Dependencies:**\n _ From the root directory, run `npm install` or `yarn install` to link workspaces and install root dev dependencies (ESLint, Prettier, TypeScript, husky, etc.).\n\n## 3. `tsconfig.base.json` Setup\n\nCreate `queen-booking-system/tsconfig.base.json` at the root:\n\n```json\n{\n \"compilerOptions\": {\n \"target\": \"ES2020\",\n \"module\": \"commonjs\",\n \"lib\": [\"dom\", \"dom.iterable\", \"esnext\"],\n \"allowJs\": true,\n \"skipLibCheck\": true,\n \"esModuleInterop\": true,\n \"allowSyntheticDefaultImports\": true,\n \"strict\": true,\n \"forceConsistentCasingInFileNames\": true,\n \"noFallthroughCasesInSwitch\": true,\n \"moduleResolution\": \"node\",\n \"resolveJsonModule\": true,\n \"isolatedModules\": true,\n \"noEmit\": true, // Base config should not emit; individual projects will.\n \"jsx\": \"preserve\", // For Next.js, can be overridden\n \"baseUrl\": \".\",\n \"paths\": {\n \"@/_\": [\"apps/web/src/*\"], // Alias for Next.js app\n \"@api/_\": [\"apps/api/src/_\"], // Alias for NestJS app\n \"@shared-types/_\": [\"packages/shared-types/src/_\"],\n \"shared-types\": [\"packages/shared-types/src/index\"],\n \"shared-types/_\": [\"packages/shared-types/src/_\"]\n },\n \"incremental\": true,\n \"plugins\": [\n {\n \"name\": \"next\"\n }\n ]\n },\n \"exclude\": [\"node_modules\", \".next\", \"dist\", \"build\"]\n}\n``\n\n**Individual Project `tsconfig.json` files:**\n\n*   **`apps/api/tsconfig.json`:**\n    Should extend `../../tsconfig.base.json` and set its own `outDir`. It may also need `\"module\": \"commonjs\"` and specific NestJS decorators metadata.\n    ``json\n {\n \"extends\": \"../../tsconfig.base.json\",\n \"compilerOptions\": {\n \"module\": \"commonjs\",\n \"declaration\": true,\n \"removeComments\": true,\n \"emitDecoratorMetadata\": true,\n \"experimentalDecorators\": true,\n \"allowSyntheticDefaultImports\": true,\n \"target\": \"ES2021\",\n \"sourceMap\": true,\n \"outDir\": \"./dist\",\n \"baseUrl\": \"./\", // Important for NestJS paths\n \"incremental\": true,\n \"skipLibCheck\": true,\n \"strictNullChecks\": false,\n \"noImplicitAny\": false,\n \"strictBindCallApply\": false,\n \"forceConsistentCasingInFileNames\": false,\n \"noFallthroughCasesInSwitch\": false,\n \"paths\": { // Override or add paths specific to api if needed\n \"@api/_\": [\"src/_\"],\n \"@shared-types/_\": [\"../../packages/shared-types/src/_\"],\n \"shared-types\": [\"../../packages/shared-types/src/index\"]\n }\n },\n \"include\": [\"src/**/*\"],\n \"exclude\": [\"node_modules\", \"dist\"]\n }\n ``\n    Update `apps/api/nest-cli.json` `\"sourceRoot\": \"src\"` and `\"compilerOptions\": { \"webpack\": true, \"tsConfigPath\": \"./tsconfig.build.json\" }`. Ensure `tsconfig.build.json` also extends base and sets up paths correctly if it exists, or simplify to use one `tsconfig.json`.\n\n*   **`apps/web/tsconfig.json`:**\n    Should extend `../../tsconfig.base.json`. Next.js `create-next-app` usually sets this up well.\n    ``json\n {\n \"extends\": \"../../tsconfig.base.json\",\n \"compilerOptions\": {\n \"lib\": [\"dom\", \"dom.iterable\", \"esnext\"],\n \"allowJs\": true,\n \"skipLibCheck\": true,\n \"strict\": true,\n \"noEmit\": true,\n \"esModuleInterop\": true,\n \"module\": \"esnext\",\n \"moduleResolution\": \"bundler\", // or \"node\"\n \"resolveJsonModule\": true,\n \"isolatedModules\": true,\n \"jsx\": \"preserve\",\n \"incremental\": true,\n \"plugins\": [\n {\n \"name\": \"next\"\n }\n ],\n \"paths\": {\n \"@/_\": [\"./src/_\"],\n \"@shared-types/_\": [\"../../packages/shared-types/src/_\"],\n \"shared-types\": [\"../../packages/shared-types/src/index\"]\n }\n },\n \"include\": [\"next-env.d.ts\", \"**/*.ts\", \"**/*.tsx\", \".next/types/**/*.ts\"],\n \"exclude\": [\"node_modules\"]\n }\n ``\n\n*   **`packages/shared-types/tsconfig.json`:** (Already defined above)\n    Should extend `../../tsconfig.base.json` and set `composite: true`, `declaration: true`, `declarationMap: true`, `outDir`, `rootDir`.\n\n**To make path aliases work across packages:**\n*   In `apps/api/package.json` and `apps/web/package.json`, add `shared-types` as a dependency:\n    ``json\n // In apps/api/package.json and apps/web/package.json\n \"dependencies\": {\n // ... other dependencies\n \"shared-types\": \"workspace:_\" // or \"1.0.0\" if using fixed versions\n },\n ```\n_ Ensure `shared-types` is built before `api` or `web` try to consume it. The root `build-all` script handles this.\n* For NestJS (`apps/api`), you might need `tsconfig-paths` for runtime path resolution if you are not using webpack bundling that resolves these paths, or if you are running scripts directly with `ts-node`.\n * Install `npm install --save-dev tsconfig-paths` in `apps/api`.\n \* Modify `apps/api/package.json` start scripts:\n `\"start:dev\": \"nest start --watch -e \\\"node -r tsconfig-paths/register\\\"\"`\n `\"start:prod\": \"node dist/main\"` (Bundled output should resolve paths)\n\n## 4. Root Scripts (`package.json`)\n\nScripts in the root `package.json` (shown in step 1) will use `npm run <script> --workspace=<name>` or `yarn workspace <name> <script>` to delegate tasks to individual packages.\n\nThis setup provides a solid foundation for a scalable monorepo with shared types and consistent tooling.\n
