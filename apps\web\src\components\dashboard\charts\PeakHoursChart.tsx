'use client';

import React from 'react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ComposedChart,
  Line,
  LineChart,
} from 'recharts';
import { PeakHoursDto } from 'shared-types';
import { analyticsService } from '../../../services/analytics.service';

interface PeakHoursChartProps {
  data: PeakHoursDto[];
  height?: number;
  showRevenue?: boolean;
  chartType?: 'bar' | 'line';
}

const PeakHoursChart: React.FC<PeakHoursChartProps> = ({
  data,
  height = 300,
  showRevenue = false,
  chartType = 'bar',
}) => {
  const chartData = data
    .sort((a, b) => a.hour - b.hour)
    .map(item => ({
      ...item,
      hourLabel: analyticsService.formatHour(item.hour),
    }));

  const formatTooltipValue = (value: number, name: string) => {
    if (name === 'revenue') {
      return [analyticsService.formatCurrency(value), '<PERSON><PERSON>h thu'];
    }
    if (name === 'bookingCount') {
      return [value, 'Số đặt phòng'];
    }
    return [value, name];
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) return null;

    return (
      <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
        <p className="text-sm font-medium text-gray-800 mb-2">Giờ {label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center space-x-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-sm text-gray-600">
              {formatTooltipValue(entry.value, entry.dataKey)[1]}:
            </span>
            <span className="text-sm font-semibold text-gray-800">
              {formatTooltipValue(entry.value, entry.dataKey)[0]}
            </span>
          </div>
        ))}
      </div>
    );
  };

  if (chartType === 'line') {
    return (
      <div style={{ height: `${height}px` }} className="w-full">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={chartData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 20,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis
              dataKey="hourLabel"
              stroke="#6b7280"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            <YAxis
              yAxisId="bookings"
              orientation="left"
              stroke="#6b7280"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            {showRevenue && (
              <YAxis
                yAxisId="revenue"
                orientation="right"
                stroke="#ab8d59"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickFormatter={value => analyticsService.formatNumber(value)}
              />
            )}
            <Tooltip content={<CustomTooltip />} />

            <Line
              yAxisId="bookings"
              type="monotone"
              dataKey="bookingCount"
              stroke="#3b82f6"
              strokeWidth={3}
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
              name="bookingCount"
            />

            {showRevenue && (
              <Line
                yAxisId="revenue"
                type="monotone"
                dataKey="revenue"
                stroke="#ab8d59"
                strokeWidth={3}
                dot={{ fill: '#ab8d59', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: '#ab8d59', strokeWidth: 2 }}
                name="revenue"
              />
            )}
          </LineChart>
        </ResponsiveContainer>
      </div>
    );
  }

  return (
    <div style={{ height: `${height}px` }} className="w-full">
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 20,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="hourLabel"
            stroke="#6b7280"
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          <YAxis
            yAxisId="bookings"
            orientation="left"
            stroke="#6b7280"
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          {showRevenue && (
            <YAxis
              yAxisId="revenue"
              orientation="right"
              stroke="#ab8d59"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={value => analyticsService.formatNumber(value)}
            />
          )}
          <Tooltip content={<CustomTooltip />} />

          <Bar
            yAxisId="bookings"
            dataKey="bookingCount"
            fill="#3b82f6"
            radius={[4, 4, 0, 0]}
            name="bookingCount"
          />

          {showRevenue && (
            <Line
              yAxisId="revenue"
              type="monotone"
              dataKey="revenue"
              stroke="#ab8d59"
              strokeWidth={3}
              dot={{ fill: '#ab8d59', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#ab8d59', strokeWidth: 2 }}
              name="revenue"
            />
          )}
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

export default PeakHoursChart;
