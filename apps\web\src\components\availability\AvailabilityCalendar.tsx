'use client';

import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Clock,
  Plus,
  Minus,
  X,
  AlertCircle,
  Loader2,
  Info,
} from 'lucide-react';
import {
  availabilityService,
  TimeSlot,
  DayAvailability,
} from '../../services/availability.service';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { useDialogs } from '../ui/modal-provider';
import { UnblockModal } from './UnblockModal';
import { BlockedSlotInfoModal } from './BlockedSlotInfoModal';

interface AvailabilityCalendarProps {
  roomId: string;
  locationOperatingHours?: { [key: string]: string };
  onSlotClick?: (slot: TimeSlot, date: string) => void;
  onQuickBlock?: (date: string, startTime: string, endTime: string) => void;
  onUnblock?: (date: string, startTime: string, endTime: string) => void;
}

// Skeleton components for loading states
const SkeletonTimeSlot: React.FC = () => (
  <div className="p-2 border rounded-md bg-gray-100 animate-pulse">
    <div className="h-4 bg-gray-200 rounded w-12 mx-auto"></div>
  </div>
);

const SkeletonStatCard: React.FC = () => (
  <div className="bg-gray-50 p-3 rounded-lg animate-pulse">
    <div className="h-4 bg-gray-200 rounded w-16 mb-1"></div>
    <div className="h-6 bg-gray-300 rounded w-8"></div>
  </div>
);

const LoadingOverlay: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => (
  <div className="relative">
    {children}
    <div className="absolute inset-0 bg-white/70 backdrop-blur-sm flex items-center justify-center rounded-lg transition-opacity duration-300">
      <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-lg shadow-lg">
        <Loader2 className="h-5 w-5 animate-spin text-queens-gold" />
        <span className="text-gray-700 font-medium">Đang cập nhật...</span>
      </div>
    </div>
  </div>
);

export const AvailabilityCalendar: React.FC<AvailabilityCalendarProps> = ({
  roomId,
  locationOperatingHours,
  onSlotClick,
  onQuickBlock,
  onUnblock,
}) => {
  const { alert, confirm } = useDialogs();
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  const [dayAvailability, setDayAvailability] =
    useState<DayAvailability | null>(null);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false); // For block/unblock operations
  const [error, setError] = useState<string | null>(null);

  // Blocked slot info modal state
  const [showBlockedSlotModal, setShowBlockedSlotModal] = useState(false);
  const [selectedBlockedSlot, setSelectedBlockedSlot] = useState<{
    slot: TimeSlot;
    date: string;
  } | null>(null);

  // Unblock modal state (now used only for quick unblock action from hover button)
  const [showUnblockModal, setShowUnblockModal] = useState(false);
  const [selectedUnblockSlot, setSelectedUnblockSlot] = useState<{
    slot: TimeSlot;
    date: string;
  } | null>(null);

  // Load availability for selected date
  useEffect(() => {
    if (roomId && selectedDate) {
      loadDayAvailability();
    }
  }, [roomId, selectedDate]);

  const loadDayAvailability = async (showTransition = true) => {
    if (showTransition) {
      setLoading(true);
    }
    setError(null);

    try {
      // Add minimum loading time for better perceived performance
      const startTime = Date.now();
      const minLoadingTime = showTransition ? 600 : 0;

      const availability =
        await availabilityService.getRoomDayAvailabilityWithOverrides(
          roomId,
          selectedDate,
          locationOperatingHours
        );

      // Ensure minimum loading time for smooth UX
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

      if (remainingTime > 0 && showTransition) {
        setTimeout(() => {
          setDayAvailability(availability);
          setLoading(false);
        }, remainingTime);
      } else {
        setDayAvailability(availability);
        if (showTransition) {
          setLoading(false);
        }
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Lỗi khi tải lịch trống';
      setError(errorMessage);
      console.error('Error loading day availability:', err);
      if (showTransition) {
        setLoading(false);
      }
    }
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedDate(e.target.value);
  };

  const formatDate = (dateStr: string): string => {
    const date = new Date(dateStr);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Hôm nay';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Ngày mai';
    } else {
      return date.toLocaleDateString('vi-VN', {
        weekday: 'long',
        day: 'numeric',
        month: 'numeric',
      });
    }
  };

  const getSlotStatusColor = (slot: TimeSlot): string => {
    if (!slot.isAvailable) {
      if (slot.reason === 'booked') {
        return 'bg-red-100 border-red-300 text-red-700 hover:bg-red-200';
      } else if (slot.reason === 'override_unavailable') {
        return 'bg-orange-100 border-orange-300 text-orange-700 hover:bg-orange-200';
      } else {
        return 'bg-gray-100 border-gray-300 text-gray-500';
      }
    }
    return 'bg-green-100 border-green-300 text-green-700 hover:bg-green-200';
  };

  const getSlotActions = (slot: TimeSlot) => {
    if (!slot.isAvailable) {
      if (slot.reason === 'override_unavailable') {
        return (
          <div className="flex gap-1">
            <button
              onClick={e => {
                e.stopPropagation();
                setSelectedBlockedSlot({ slot, date: selectedDate });
                setShowBlockedSlotModal(true);
              }}
              className="p-1 text-orange-600 hover:bg-orange-200 rounded transition-colors duration-200"
              title="Xem thông tin"
            >
              <Info className="w-3 h-3" />
            </button>
            <button
              onClick={e => {
                e.stopPropagation();
                setSelectedUnblockSlot({ slot, date: selectedDate });
                setShowUnblockModal(true);
              }}
              className="p-1 text-orange-600 hover:bg-orange-200 rounded transition-colors duration-200"
              title="Bỏ chặn"
            >
              <Minus className="w-3 h-3" />
            </button>
          </div>
        );
      } else if (slot.reason === 'booked') {
        return (
          <button
            onClick={e => {
              e.stopPropagation();
              alert(
                'Thời gian này đã được đặt bởi khách hàng',
                'Thông tin',
                'info'
              );
            }}
            className="p-1 text-red-600 hover:bg-red-200 rounded transition-colors duration-200"
            title="Xem thông tin đặt phòng"
          >
            <Info className="w-3 h-3" />
          </button>
        );
      }
    } else {
      return (
        <button
          onClick={e => {
            e.stopPropagation();
            handleQuickBlock(slot);
          }}
          className="p-1 text-green-600 hover:bg-green-200 rounded transition-colors duration-200"
          title="Chặn nhanh"
        >
          <Plus className="w-3 h-3" />
        </button>
      );
    }
    return null;
  };

  const handleQuickBlock = async (slot: TimeSlot) => {
    if (onQuickBlock) {
      setUpdating(true);
      try {
        await onQuickBlock(selectedDate, slot.startTime, slot.endTime);
        // Refresh with smooth transition
        await loadDayAvailability(false);
      } finally {
        setUpdating(false);
      }
    }
  };

  const handleUnblockSuccess = async () => {
    setUpdating(true);
    try {
      // Refresh with smooth transition
      await loadDayAvailability(false);
    } finally {
      setUpdating(false);
      setShowUnblockModal(false);
      setSelectedUnblockSlot(null);
    }
  };

  const renderTimeSlots = () => {
    if (loading) {
      // Render skeleton slots
      return (
        <div className="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-12 gap-2">
          {Array.from({ length: 48 }, (_, index) => (
            <SkeletonTimeSlot key={index} />
          ))}
        </div>
      );
    }

    if (!dayAvailability?.timeSlots?.length) {
      return (
        <div className="text-center py-8 animate-fade-in">
          <Clock className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-500">
            Không có thời gian khả dụng cho ngày này
          </p>
        </div>
      );
    }

    const content = (
      <div className="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-12 gap-2 time-slot-grid animate-fade-in">
        {dayAvailability.timeSlots.map((slot: TimeSlot, index: number) => (
          <div
            key={`${slot.startTime}-${slot.endTime}`}
            className={`
              time-slot relative p-2 border rounded-md cursor-pointer text-xs font-medium text-center
              transition-all duration-200 hover:shadow-sm group
              ${getSlotStatusColor(slot)}
            `}
            style={{ animationDelay: `${index * 0.02}s` }}
            onClick={() => onSlotClick?.(slot, selectedDate)}
            title={`${slot.startTime} - ${slot.endTime} (${slot.isAvailable ? 'Trống' : 'Đã đặt'})`}
          >
            <div className="text-xs">{slot.startTime}</div>

            {/* Action buttons on hover */}
            <div className="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              {getSlotActions(slot)}
            </div>
          </div>
        ))}
      </div>
    );

    return updating ? <LoadingOverlay>{content}</LoadingOverlay> : content;
  };

  const renderStats = () => {
    if (loading) {
      return (
        <div className="grid grid-cols-3 gap-4">
          <SkeletonStatCard />
          <SkeletonStatCard />
          <SkeletonStatCard />
        </div>
      );
    }

    if (!dayAvailability) return null;

    // Calculate statistics from timeSlots
    const stats = dayAvailability.timeSlots.reduce(
      (acc, slot) => {
        acc.totalSlots++;
        if (slot.isAvailable) {
          acc.availableSlots++;
        } else if (slot.reason === 'booked') {
          acc.bookedSlots++;
        } else if (slot.reason === 'override_unavailable') {
          acc.blockedSlots++;
        }
        return acc;
      },
      { totalSlots: 0, availableSlots: 0, bookedSlots: 0, blockedSlots: 0 }
    );

    return (
      <div className="grid grid-cols-3 gap-4 animate-fade-in">
        <div className="bg-green-50 p-3 rounded-lg border border-green-200">
          <div className="text-xs text-green-600 font-medium">Trống</div>
          <div className="text-lg font-bold text-green-700">
            {stats.availableSlots}
          </div>
        </div>
        <div className="bg-red-50 p-3 rounded-lg border border-red-200">
          <div className="text-xs text-red-600 font-medium">Đã đặt</div>
          <div className="text-lg font-bold text-red-700">
            {stats.bookedSlots}
          </div>
        </div>
        <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
          <div className="text-xs text-orange-600 font-medium">Đã chặn</div>
          <div className="text-lg font-bold text-orange-700">
            {stats.blockedSlots}
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card className="transition-all duration-300 ease-in-out">
      <CardHeader className="space-y-4">
        <CardTitle className="flex items-center gap-2">
          <Calendar className="w-5 h-5 text-queens-gold" />
          Lịch trống phòng
        </CardTitle>

        {/* Date Selection */}
        <div className="flex items-center gap-4">
          <div className="flex-1">
            <input
              type="date"
              value={selectedDate}
              onChange={handleDateChange}
              min={new Date().toISOString().split('T')[0]}
              max={
                new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
                  .toISOString()
                  .split('T')[0]
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-queens-gold focus:border-transparent text-black"
            />
          </div>
          <div className="text-sm text-gray-600 font-medium">
            {formatDate(selectedDate)}
          </div>
        </div>

        {/* Statistics */}
        {renderStats()}
      </CardHeader>

      <CardContent className="space-y-4">
        {error && (
          <div className="flex items-center gap-2 p-4 bg-red-50 border border-red-200 rounded-md animate-slide-in-down">
            <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
            <span className="text-red-700 flex-1">{error}</span>
            <Button
              onClick={() => loadDayAvailability()}
              variant="outline"
              size="sm"
              className="border-red-300 text-red-700 hover:bg-red-50"
            >
              Thử lại
            </Button>
          </div>
        )}

        {/* Time Slots Grid */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900">
              Khung giờ (30 phút/khung)
            </h4>
            {!loading && dayAvailability && (
              <Button
                onClick={() => loadDayAvailability()}
                variant="outline"
                size="sm"
                className="transition-colors duration-200"
              >
                <Clock className="w-4 h-4 mr-2" />
                Làm mới
              </Button>
            )}
          </div>

          {renderTimeSlots()}

          {/* Legend */}
          {!loading && dayAvailability && (
            <div className="flex flex-wrap gap-4 text-xs animate-fade-in">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
                <span className="text-gray-600">Trống</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-100 border border-red-300 rounded"></div>
                <span className="text-gray-600">Đã đặt</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-orange-100 border border-orange-300 rounded"></div>
                <span className="text-gray-600">Bị chặn</span>
              </div>
            </div>
          )}
        </div>
      </CardContent>

      {/* Modals */}
      {showBlockedSlotModal && selectedBlockedSlot && (
        <BlockedSlotInfoModal
          isOpen={showBlockedSlotModal}
          onClose={() => {
            setShowBlockedSlotModal(false);
            setSelectedBlockedSlot(null);
          }}
          roomId={roomId}
          slot={selectedBlockedSlot.slot}
          date={selectedBlockedSlot.date}
        />
      )}

      {showUnblockModal &&
        selectedUnblockSlot &&
        selectedUnblockSlot.slot.overrideId && (
          <UnblockModal
            isOpen={showUnblockModal}
            onClose={() => {
              setShowUnblockModal(false);
              setSelectedUnblockSlot(null);
            }}
            roomId={roomId}
            date={selectedUnblockSlot.date}
            startTime={selectedUnblockSlot.slot.startTime}
            endTime={selectedUnblockSlot.slot.endTime}
            overrideId={selectedUnblockSlot.slot.overrideId}
            overrideReason={selectedUnblockSlot.slot.overrideReason}
            onSuccess={handleUnblockSuccess}
          />
        )}
    </Card>
  );
};
