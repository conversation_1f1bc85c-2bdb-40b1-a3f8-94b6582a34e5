'use client';

import React, { useState } from 'react';
import { Clock, ShieldOff, Calendar, AlertTriangle, Info } from 'lucide-react';
import {
  availabilityService,
  TimeSlot,
} from '../../services/availability.service';
import { Button } from '../ui/button';
import { <PERSON><PERSON>, ModalHeader, ModalBody, ModalFooter } from '../ui/modal';
import { UnblockModal } from './UnblockModal';

interface BlockedSlotInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
  roomId: string;
  date: string;
  slot: TimeSlot;
  onUnblockSuccess?: () => void;
}

export const BlockedSlotInfoModal: React.FC<BlockedSlotInfoModalProps> = ({
  isOpen,
  onClose,
  roomId,
  date,
  slot,
  onUnblockSuccess,
}) => {
  const [showUnblockModal, setShowUnblockModal] = useState(false);

  const formatDate = (dateStr: string): string => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('vi-VN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTimeRange = (): string => {
    return availabilityService.formatTimeSlot(slot.startTime, slot.endTime);
  };

  const getBlockedReason = (): string => {
    if (slot.overrideReason) {
      return slot.overrideReason;
    }

    // Handle different slot reasons with appropriate Vietnamese descriptions
    switch (slot.reason) {
      case 'override_unavailable':
        return 'Chặn thủ công bởi quản trị viên';
      case 'outside_operating_hours':
        return 'Ngoài giờ hoạt động của cơ sở';
      case 'unknown':
        return 'Lỗi hệ thống hoặc không xác định';
      default:
        return 'Không có lý do cụ thể';
    }
  };

  const getBlockedDuration = (): string => {
    const start = new Date(`2024-01-01T${slot.startTime}:00`);
    const end = new Date(`2024-01-01T${slot.endTime}:00`);
    const durationMs = end.getTime() - start.getTime();
    const durationMinutes = durationMs / (1000 * 60);
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;

    if (hours > 0 && minutes > 0) {
      return `${hours} giờ ${minutes} phút`;
    } else if (hours > 0) {
      return `${hours} giờ`;
    } else {
      return `${minutes} phút`;
    }
  };

  const handleUnblockClick = () => {
    setShowUnblockModal(true);
  };

  const handleUnblockModalClose = () => {
    setShowUnblockModal(false);
  };

  const handleUnblockSuccess = () => {
    setShowUnblockModal(false);
    if (onUnblockSuccess) {
      onUnblockSuccess();
    }
    onClose();
  };

  const canUnblock = slot.reason === 'override_unavailable' && slot.overrideId;

  const getBlockType = (): string => {
    switch (slot.reason) {
      case 'override_unavailable':
        return 'Chặn thủ công bởi nhân viên';
      case 'outside_operating_hours':
        return 'Chặn hệ thống - Ngoài giờ hoạt động';
      case 'unknown':
        return 'Chặn hệ thống - Không xác định';
      default:
        return 'Chặn hệ thống';
    }
  };

  const getDetailedBlockInfo = (): string => {
    switch (slot.reason) {
      case 'override_unavailable':
        return slot.overrideReason || 'Chặn thủ công bởi nhân viên';
      case 'outside_operating_hours':
        return 'Khung giờ này nằm ngoài giờ hoạt động của cơ sở';
      case 'booked':
        return 'Khung giờ này đã được đặt bởi khách hàng';
      case 'unknown':
        return 'Lý do chặn không xác định';
      default:
        return 'Khung giờ này không khả dụng';
    }
  };

  const getModalTitle = (): string => {
    if (slot.reason === 'override_unavailable') {
      return 'Chi tiết về khung giờ bị chặn bởi nhân viên';
    }
    return 'Thông Tin Khung Giờ Không Khả Dụng';
  };

  const getModalDescription = (): string => {
    if (slot.reason === 'override_unavailable') {
      return 'Chi tiết về khung giờ bị chặn bởi nhân viên';
    }
    return 'Chi tiết về khung giờ không khả dụng';
  };

  const getHeaderIcon = () => {
    if (slot.reason === 'override_unavailable') {
      return <AlertTriangle className="h-5 w-5 text-orange-500" />;
    }
    return <Info className="h-5 w-5 text-gray-500" />;
  };

  const getSectionBackgroundColor = (): string => {
    if (slot.reason === 'override_unavailable') {
      return 'bg-orange-50 border-orange-200';
    }
    return 'bg-gray-50 border-gray-200';
  };

  const getSectionTextColor = (): string => {
    if (slot.reason === 'override_unavailable') {
      return 'text-orange-600';
    }
    return 'text-gray-600';
  };

  const getSectionTitleColor = (): string => {
    if (slot.reason === 'override_unavailable') {
      return 'text-orange-900';
    }
    return 'text-gray-900';
  };

  return (
    <>
      <Modal isOpen={isOpen} onClose={onClose} size="md">
        <ModalHeader>
          <div className="flex items-center gap-2">
            {getHeaderIcon()}
            <h2 className="text-h5 font-semibold text-gray-900">
              {getModalTitle()}
            </h2>
          </div>
          <p className="text-sm text-gray-600 mt-2">{getModalDescription()}</p>
        </ModalHeader>

        <ModalBody>
          <div className="space-y-4">
            {/* Time Slot Overview */}
            <div
              className={`p-4 rounded-md border ${getSectionBackgroundColor()}`}
            >
              <div className="flex items-center gap-2 mb-3">
                <Clock className={`h-4 w-4 ${getSectionTextColor()}`} />
                <span className={`font-medium ${getSectionTitleColor()}`}>
                  Chi tiết khung giờ
                </span>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Ngày:</span>
                  <p className="text-gray-600">{formatDate(date)}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Thời gian:</span>
                  <p className="text-gray-600 font-mono">{formatTimeRange()}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Thời lượng:</span>
                  <p className="text-gray-600">{getBlockedDuration()}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Trạng thái:</span>
                  <p className="text-gray-600">
                    {availabilityService.getSlotStatusLabel(slot)}
                  </p>
                </div>
              </div>
            </div>

            {/* Block Details */}
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex items-center gap-2 mb-3">
                <Info className="h-4 w-4 text-gray-600" />
                <span className="font-medium text-gray-900">Chi tiết chặn</span>
              </div>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Lý do:</span>
                  <p className="text-gray-600 mt-1">{getDetailedBlockInfo()}</p>
                </div>
                {slot.overrideId && (
                  <div>
                    <span className="font-medium text-gray-700">ID chặn:</span>
                    <p className="text-gray-600 font-mono text-xs">
                      {slot.overrideId}
                    </p>
                  </div>
                )}
                <div>
                  <span className="font-medium text-gray-700">Loại:</span>
                  <p className="text-gray-600">{getBlockType()}</p>
                </div>
              </div>
            </div>

            {/* Impact Information */}
            <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-900">Tác động</span>
              </div>
              <p className="text-sm text-blue-800">
                Khách hàng không thể đặt phòng trong khung giờ này.
                {canUnblock &&
                  ' Bạn có thể bỏ chặn để mở lại khả năng đặt phòng.'}
                {slot.reason === 'outside_operating_hours' &&
                  ' Khung giờ này nằm ngoài giờ hoạt động của cơ sở.'}
              </p>
            </div>

            {/* Unblock Action */}
            {canUnblock && (
              <div className="bg-green-50 p-4 rounded-md border border-green-200">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <ShieldOff className="h-4 w-4 text-green-600" />
                      <span className="font-medium text-green-900">
                        Bỏ chặn khung giờ
                      </span>
                    </div>
                    <p className="text-sm text-green-800">
                      Khôi phục khả năng đặt phòng cho khung giờ này
                    </p>
                  </div>
                  <Button
                    onClick={handleUnblockClick}
                    className="bg-green-500 hover:bg-green-600 text-white"
                    size="sm"
                  >
                    <ShieldOff className="h-3 w-3 mr-1" />
                    Bỏ chặn
                  </Button>
                </div>
              </div>
            )}

            {/* Non-unblockable notice */}
            {!canUnblock && (
              <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                <div className="flex items-center gap-2 mb-1">
                  <Info className="h-4 w-4 text-gray-600" />
                  <span className="font-medium text-gray-700">Lưu ý</span>
                </div>
                <p className="text-sm text-gray-600">
                  {slot.reason === 'outside_operating_hours'
                    ? 'Khung giờ này nằm ngoài giờ hoạt động của cơ sở và không thể bỏ chặn. Vui lòng kiểm tra giờ hoạt động của địa điểm.'
                    : slot.reason === 'unknown'
                      ? 'Khung giờ này không khả dụng do lỗi hệ thống hoặc cấu hình chưa rõ ràng.'
                      : 'Khung giờ này không thể bỏ chặn vì nó thuộc loại chặn hệ thống hoặc không có quyền chỉnh sửa.'}
                </p>
              </div>
            )}
          </div>
        </ModalBody>

        <ModalFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            Đóng
          </Button>
        </ModalFooter>
      </Modal>

      {/* Unblock Modal */}
      {canUnblock && slot.overrideId && (
        <UnblockModal
          isOpen={showUnblockModal}
          onClose={handleUnblockModalClose}
          roomId={roomId}
          date={date}
          startTime={slot.startTime}
          endTime={slot.endTime}
          overrideId={slot.overrideId}
          overrideReason={slot.overrideReason}
          onSuccess={handleUnblockSuccess}
        />
      )}
    </>
  );
};
