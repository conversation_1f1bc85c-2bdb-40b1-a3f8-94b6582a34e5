import { Modu<PERSON> } from '@nestjs/common';
import { BookingsService } from './bookings.service';
import { BookingsController } from './bookings.controller';
import { BookingReferenceGenerator } from './booking-reference.generator';
import { PrismaModule } from '../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module';
import { AvailabilityModule } from '../availability/availability.module'; // For availability checks
import { RoomsModule } from '../rooms/rooms.module'; // For room details
import { EmailModule } from '../email/email.module';

@Module({
  imports: [
    PrismaModule,
    AuthModule,
    AvailabilityModule,
    RoomsModule,
    EmailModule,
  ],
  controllers: [BookingsController],
  providers: [BookingsService, BookingReferenceGenerator],
  exports: [BookingsService],
})
export class BookingsModule {}
