'use client';

import { motion } from 'framer-motion';

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

const shimmerAnimation = {
  initial: {
    backgroundPosition: '200% 0',
    opacity: 0.4,
  },
  animate: {
    backgroundPosition: ['-200% 0', '200% 0'],
    opacity: [0.4, 0.8, 0.4],
    transition: {
      duration: 4,
      ease: 'linear',
      repeat: Infinity,
      opacity: {
        duration: 2,
        ease: 'easeInOut',
        repeat: Infinity,
      },
    },
  },
};

export function Hero() {
  return (
    <motion.div
      key="hero-section"
      className="text-center max-w-2xl mx-auto"
      initial="hidden"
      animate="visible"
      exit={{
        opacity: 0,
        y: -50,
        transition: {
          duration: 0.3,
          ease: [0.25, 0.46, 0.45, 0.94],
        },
      }}
      variants={{
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { duration: 0.8 } },
      }}
    >
      <motion.div
        className="inline-flex items-center gap-6"
        initial={{ scale: 0.5, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{
          scale: 0.9,
          opacity: 0,
          transition: {
            duration: 0.2,
            ease: [0.25, 0.46, 0.45, 0.94],
          },
        }}
        transition={{
          duration: 0.4,
          ease: [0.34, 1.56, 0.64, 1],
        }}
      >
        <motion.img
          src="/images/brand/logo.png"
          alt="Queen Entertainment Logo"
          className="h-16 md:h-20 lg:h-24 w-auto object-contain"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{
            scale: 0.8,
            opacity: 0,
            transition: {
              duration: 0.2,
              ease: [0.25, 0.46, 0.45, 0.94],
            },
          }}
          transition={{
            duration: 0.3,
            delay: 0.2,
            ease: [0.34, 1.56, 0.64, 1],
          }}
        />
        <div className="relative">
          <motion.h1
            className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold relative z-10 bg-gradient-to-r from-[#9A6C2E] via-[#E6B325] to-[#9A6C2E] bg-clip-text text-transparent drop-shadow-[0_1px_1px_rgba(0,0,0,0.2)] whitespace-nowrap"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{
              y: -20,
              opacity: 0,
              transition: {
                duration: 0.2,
                ease: [0.25, 0.46, 0.45, 0.94],
              },
            }}
            transition={{
              duration: 0.4,
              ease: [0.25, 0.46, 0.45, 0.94],
            }}
          >
            Queen Karaoke
          </motion.h1>
          <motion.div
            className="absolute -inset-x-6 -inset-y-4 bg-gradient-to-r from-[#9A6C2E]/0 via-[#E6B325]/30 to-[#9A6C2E]/0 rounded-full blur-2xl -z-10"
            variants={shimmerAnimation}
            initial="initial"
            animate="animate"
            exit={{
              opacity: 0,
              scale: 0.9,
              transition: {
                duration: 0.2,
              },
            }}
          />
        </div>
      </motion.div>
      <motion.p
        className="text-base md:text-lg lg:text-xl xl:text-2xl text-gold-700/90 tracking-wide"
        variants={fadeInUp}
        exit={{
          opacity: 0,
          y: -20,
          transition: {
            duration: 0.2,
            ease: [0.25, 0.46, 0.45, 0.94],
          },
        }}
      >
        Trải nghiệm đẳng cấp - Không gian sang trọng
      </motion.p>
    </motion.div>
  );
}
