'use client';

import React from 'react';
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
} from 'recharts';
import { RoomTypeStatsDto } from 'shared-types';
import { analyticsService } from '../../../services/analytics.service';

interface RoomTypeChartProps {
  data: RoomTypeStatsDto[];
  chartType?: 'pie' | 'bar';
  height?: number;
  metric?: 'count' | 'revenue' | 'bookings' | 'occupancyRate';
}

const RoomTypeChart: React.FC<RoomTypeChartProps> = ({
  data,
  chartType = 'pie',
  height = 300,
  metric = 'count',
}) => {
  // Room type colors mapping
  const getRoomTypeColor = (roomType: string): string => {
    switch (roomType) {
      case 'QUEENS_EYES':
        return '#065F46'; // Deep emerald
      case 'RUBY':
        return '#8B2635'; // Deep ruby red
      case 'SAPPHIRE':
        return '#1E3A8A'; // Deep sapphire blue
      case 'OPAL':
        return '#7C3AED'; // Purple
      case 'PEARL':
        return '#374151'; // Dark gray
      case 'HALL':
        return '#1F2937'; // Formal dark
      default: // STANDARD
        return '#987947'; // Gold-600
    }
  };

  const chartData = data.map(item => ({
    ...item,
    name: analyticsService.getRoomTypeDisplayName(item.roomType),
    value: item[metric],
    color: getRoomTypeColor(item.roomType),
  }));

  const formatValue = (value: number, metricType: string) => {
    switch (metricType) {
      case 'revenue':
        return analyticsService.formatCurrency(value);
      case 'occupancyRate':
        return `${value.toFixed(1)}%`;
      default:
        return value.toString();
    }
  };

  const getMetricLabel = (metricType: string) => {
    switch (metricType) {
      case 'count':
        return 'Số phòng';
      case 'revenue':
        return 'Doanh thu';
      case 'bookings':
        return 'Số đặt phòng';
      case 'occupancyRate':
        return 'Tỷ lệ lấp đầy';
      default:
        return 'Giá trị';
    }
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (!active || !payload || !payload.length) return null;

    const data = payload[0].payload;

    return (
      <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
        <p className="text-sm font-medium text-gray-800 mb-2">{data.name}</p>
        <div className="space-y-1">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Số phòng:</span>
            <span className="text-sm font-semibold text-gray-800">
              {data.count}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Đặt phòng:</span>
            <span className="text-sm font-semibold text-gray-800">
              {data.bookings}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Doanh thu:</span>
            <span className="text-sm font-semibold text-gray-800">
              {analyticsService.formatCurrency(data.revenue)}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Tỷ lệ lấp đầy:</span>
            <span className="text-sm font-semibold text-gray-800">
              {data.occupancyRate.toFixed(1)}%
            </span>
          </div>
        </div>
      </div>
    );
  };

  if (chartType === 'pie') {
    return (
      <div style={{ height: `${height}px` }} className="w-full">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              outerRadius={height * 0.3}
              innerRadius={height * 0.15}
              dataKey="value"
              label={({ name, percent }) =>
                `${name} ${(percent * 100).toFixed(0)}%`
              }
              labelLine={false}
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </div>
    );
  }

  return (
    <div style={{ height: `${height}px` }} className="w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 20,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="name"
            stroke="#6b7280"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            angle={-45}
            textAnchor="end"
            height={60}
          />
          <YAxis
            stroke="#6b7280"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            tickFormatter={value =>
              metric === 'revenue'
                ? analyticsService.formatNumber(value)
                : value
            }
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="value" radius={[4, 4, 0, 0]}>
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default RoomTypeChart;
