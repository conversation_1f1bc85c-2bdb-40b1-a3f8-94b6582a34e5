'use client';

import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, MapPin } from 'lucide-react';
import { locationsService } from '../../../../services/locations.service';
import { CreateLocationDto, OperatingHours } from 'shared-types';
import { Button } from '../../../../components/ui/button';
import { Input } from '../../../../components/ui/input';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../../components/ui/card';
import { Label } from '../../../../components/ui/label';
import { useDialogs } from '../../../../components/ui/modal-provider';
import { OperatingHoursEditor } from '../../../../components/dashboard/OperatingHoursEditor';
import { ImageUpload } from '../../../../components/ui/index';

interface FormData {
  nameVi: string;
  nameEn: string;
  descriptionVi: string;
  descriptionEn: string;
  address: string;
  phoneNumber: string;
  email: string;
  operatingHours: OperatingHours;
  image?: File;
}

interface FormErrors {
  [key: string]: string;
}

export default function NewLocationPage() {
  const router = useRouter();
  const { alert } = useDialogs();
  const [formData, setFormData] = useState<FormData>({
    nameVi: '',
    nameEn: '',
    descriptionVi: '',
    descriptionEn: '',
    address: '',
    phoneNumber: '',
    email: '',
    operatingHours: {
      monday: '09:00-00:00',
      tuesday: '09:00-00:00',
      wednesday: '09:00-00:00',
      thursday: '09:00-00:00',
      friday: '09:00-00:00',
      saturday: '09:00-00:00',
      sunday: '09:00-00:00',
    },
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Memoize the operating hours change handler to prevent infinite loops
  const handleOperatingHoursChange = useCallback((hours: OperatingHours) => {
    setFormData(prev => ({ ...prev, operatingHours: hours }));
  }, []);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.nameVi.trim()) {
      newErrors.nameVi = 'Tên tiếng Việt là bắt buộc';
    }

    if (!formData.nameEn.trim()) {
      newErrors.nameEn = 'Tên tiếng Anh là bắt buộc';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Địa chỉ là bắt buộc';
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = 'Số điện thoại là bắt buộc';
    } else if (!/^\+?[0-9\s-()]+$/.test(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Số điện thoại không hợp lệ';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email là bắt buộc';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email không hợp lệ';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const locationData: CreateLocationDto = {
        name: {
          vi: formData.nameVi,
          en: formData.nameEn,
        },
        description: {
          vi: formData.descriptionVi,
          en: formData.descriptionEn,
        },
        address: formData.address,
        phoneNumber: formData.phoneNumber,
        email: formData.email,
        operatingHours: formData.operatingHours as Record<string, string>,
      };

      // Create location first
      const createdLocation =
        await locationsService.createLocation(locationData);

      // Upload image if selected
      if (formData.image) {
        await locationsService.uploadLocationImage(
          createdLocation.id,
          formData.image
        );
      }

      // Success - redirect to locations list
      router.push('/dashboard/locations');
    } catch (error) {
      await alert(
        'Có lỗi xảy ra khi tạo chi nhánh. Vui lòng thử lại.',
        'Lỗi',
        'error'
      );
      console.error('Error creating location:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add image change handler
  const handleImageChange = (images: File[]) => {
    if (images.length > 0) {
      setFormData(prev => ({ ...prev, image: images[0] }));
    } else {
      setFormData(prev => ({ ...prev, image: undefined }));
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={() => router.back()} className="p-2">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-h1 font-bold text-gray-900">
            Thêm Chi Nhánh Mới
          </h1>
          <p className="text-lg text-gray-600">
            Tạo một chi nhánh Queen Karaoke mới
          </p>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-queens-gold" />
              Thông Tin Cơ Bản
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Image Upload */}
            <div className="space-y-2">
              <Label>Hình ảnh chi nhánh</Label>
              <ImageUpload
                images={formData.image ? [formData.image] : []}
                onImagesChange={handleImageChange}
                maxImages={1}
                maxSize={5}
                disabled={isSubmitting}
              />
            </div>

            {/* Vietnamese Name */}
            <div className="space-y-2">
              <Label htmlFor="nameVi">Tên tiếng Việt *</Label>
              <Input
                id="nameVi"
                value={formData.nameVi}
                onChange={e => handleInputChange('nameVi', e.target.value)}
                placeholder="VD: Queen Karaoke Quận 1"
                className={errors.nameVi ? 'border-red-500' : ''}
              />
              {errors.nameVi && (
                <p className="text-sm text-red-600">{errors.nameVi}</p>
              )}
            </div>

            {/* English Name */}
            <div className="space-y-2">
              <Label htmlFor="nameEn">Tên tiếng Anh *</Label>
              <Input
                id="nameEn"
                value={formData.nameEn}
                onChange={e => handleInputChange('nameEn', e.target.value)}
                placeholder="EG: Queen Karaoke District 1"
                className={errors.nameEn ? 'border-red-500' : ''}
              />
              {errors.nameEn && (
                <p className="text-sm text-red-600">{errors.nameEn}</p>
              )}
            </div>

            {/* Address */}
            <div className="space-y-2">
              <Label htmlFor="address">Địa chỉ *</Label>
              <Input
                id="address"
                value={formData.address}
                onChange={e => handleInputChange('address', e.target.value)}
                placeholder="123 Nguyễn Huệ, Quận 1, TP.HCM"
                className={errors.address ? 'border-red-500' : ''}
              />
              {errors.address && (
                <p className="text-sm text-red-600">{errors.address}</p>
              )}
            </div>

            {/* Phone Number */}
            <div className="space-y-2">
              <Label htmlFor="phoneNumber">Số điện thoại *</Label>
              <Input
                id="phoneNumber"
                value={formData.phoneNumber}
                onChange={e => handleInputChange('phoneNumber', e.target.value)}
                placeholder="+84-28-1234-5678"
                className={errors.phoneNumber ? 'border-red-500' : ''}
              />
              {errors.phoneNumber && (
                <p className="text-sm text-red-600">{errors.phoneNumber}</p>
              )}
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={e => handleInputChange('email', e.target.value)}
                placeholder="<EMAIL>"
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && (
                <p className="text-sm text-red-600">{errors.email}</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Mô Tả</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Vietnamese Description */}
            <div className="space-y-2">
              <Label htmlFor="descriptionVi">Mô tả tiếng Việt</Label>
              <textarea
                id="descriptionVi"
                value={formData.descriptionVi}
                onChange={e =>
                  handleInputChange('descriptionVi', e.target.value)
                }
                placeholder="Mô tả chi nhánh bằng tiếng Việt..."
                className="w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-queens-gold focus:border-transparent resize-vertical text-black"
                rows={4}
              />
            </div>

            {/* English Description */}
            <div className="space-y-2">
              <Label htmlFor="descriptionEn">Mô tả tiếng Anh</Label>
              <textarea
                id="descriptionEn"
                value={formData.descriptionEn}
                onChange={e =>
                  handleInputChange('descriptionEn', e.target.value)
                }
                placeholder="Description in English..."
                className="w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-queens-gold focus:border-transparent resize-vertical text-black"
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Giờ Hoạt Động</CardTitle>
          </CardHeader>
          <CardContent>
            <OperatingHoursEditor
              value={formData.operatingHours}
              onChange={handleOperatingHoursChange}
              errors={errors.operatingHours}
            />
          </CardContent>
        </Card>

        {/* Submit Buttons */}
        <div className="flex gap-3 justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            Hủy
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
            className="gap-2 bg-queens-gold hover:bg-queens-gold/90 text-black font-medium"
          >
            {isSubmitting ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            {isSubmitting ? 'Đang tạo...' : 'Tạo Chi Nhánh'}
          </Button>
        </div>
      </form>
    </div>
  );
}
