import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { AvailabilityOverride, BookingStatus } from '../../generated/prisma';
import {
  CreateAvailabilityOverrideDto,
  UpdateAvailabilityOverrideDto,
  AvailabilityOverrideQueryDto,
  CheckRoomAvailabilityQueryDto,
  RoomAvailabilityResponseDto,
  FindAlternativeTimeSlotsQueryDto,
  AlternativeTimeSlotsResponseDto,
  AlternativeTimeSlotDto,
} from '@shared-types/dtos.types';
import {
  formatISO,
  getDay,
  isEqual,
  isBefore,
  isAfter,
  startOfDay,
  endOfDay,
  addMinutes,
  addHours,
  subHours,
  differenceInMinutes,
} from 'date-fns';

// Type alias for operating hours structure for clarity
// Corresponds to: e.g., { "monday": "10:00-23:00", ... }
export type OperatingHoursMap = Record<string, string>;

@Injectable()
export class AvailabilityService {
  constructor(private prisma: PrismaService) {}

  // CRUD for AvailabilityOverride
  async createOverride(
    dto: CreateAvailabilityOverrideDto,
  ): Promise<AvailabilityOverride> {
    const roomExists = await this.prisma.room.findUnique({
      where: { id: dto.roomId },
    });
    if (!roomExists) {
      throw new NotFoundException(`Room with ID ${dto.roomId} not found.`);
    }

    const startTime = new Date(dto.startTime);
    const endTime = new Date(dto.endTime);
    if (!isBefore(startTime, endTime)) {
      throw new BadRequestException('Start time must be before end time.');
    }

    return this.prisma.availabilityOverride.create({
      data: {
        roomId: dto.roomId,
        date: startOfDay(new Date(dto.date)),
        startTime: startTime,
        endTime: endTime,
        isAvailable: dto.isAvailable,
        reason: dto.reason,
      },
    });
  }

  async findAllOverrides(
    query: AvailabilityOverrideQueryDto,
  ): Promise<AvailabilityOverride[]> {
    const {
      roomId,
      dateRange,
      isAvailable,
      page = 1,
      limit = 10,
      sortBy,
      sortOrder,
    } = query;
    const skip = (Number(page) - 1) * Number(limit);
    const take = Number(limit);

    const where: any = {};

    if (roomId) {
      where.roomId = roomId;
    }
    if (isAvailable !== undefined) {
      where.isAvailable = isAvailable;
    }
    if (dateRange) {
      where.date = {
        gte: dateRange.startDate
          ? startOfDay(new Date(dateRange.startDate))
          : undefined,
        lte: dateRange.endDate
          ? endOfDay(new Date(dateRange.endDate))
          : undefined,
      };
    }

    return this.prisma.availabilityOverride.findMany({
      where,
      skip,
      take,
      orderBy: sortBy
        ? { [sortBy]: sortOrder || 'asc' }
        : { createdAt: 'desc' },
    });
  }

  async findOneOverride(id: string): Promise<AvailabilityOverride | null> {
    const override = await this.prisma.availabilityOverride.findUnique({
      where: { id },
    });
    if (!override) {
      throw new NotFoundException(
        `AvailabilityOverride with ID ${id} not found.`,
      );
    }
    return override;
  }

  async updateOverride(
    id: string,
    dto: UpdateAvailabilityOverrideDto,
  ): Promise<AvailabilityOverride> {
    const existingOverride = await this.prisma.availabilityOverride.findUnique({
      where: { id },
    });
    if (!existingOverride) {
      throw new NotFoundException(
        `AvailabilityOverride with ID ${id} not found.`,
      );
    }

    const dataToUpdate: Partial<AvailabilityOverride> = {};

    if (dto.startTime) {
      dataToUpdate.startTime = new Date(dto.startTime);
    }
    if (dto.endTime) {
      dataToUpdate.endTime = new Date(dto.endTime);
    }
    if (dto.isAvailable !== undefined) {
      dataToUpdate.isAvailable = dto.isAvailable;
    }
    if (dto.reason !== undefined) {
      dataToUpdate.reason = dto.reason;
    }

    const finalStartTime = dataToUpdate.startTime || existingOverride.startTime;
    const finalEndTime = dataToUpdate.endTime || existingOverride.endTime;

    if (!isBefore(finalStartTime, finalEndTime)) {
      throw new BadRequestException('Start time must be before end time.');
    }

    return this.prisma.availabilityOverride.update({
      where: { id },
      data: dataToUpdate,
    });
  }

  async removeOverride(id: string): Promise<AvailabilityOverride> {
    const existingOverride = await this.prisma.availabilityOverride.findUnique({
      where: { id },
    });
    if (!existingOverride) {
      throw new NotFoundException(
        `AvailabilityOverride with ID ${id} not found.`,
      );
    }
    return this.prisma.availabilityOverride.delete({ where: { id } });
  }

  private parseOperatingHoursRange(
    hoursRange: string,
    referenceDateForTime: Date,
  ): { open: Date; close: Date } | null {
    if (!hoursRange || !hoursRange.includes('-')) return null;
    const [openStr, closeStr] = hoursRange.split('-');

    const [openHours, openMinutes] = openStr.split(':').map(Number);
    const openTime = new Date(referenceDateForTime);
    openTime.setHours(openHours, openMinutes, 0, 0);

    const [closeHours, closeMinutes] = closeStr.split(':').map(Number);
    const closeTime = new Date(referenceDateForTime);
    closeTime.setHours(closeHours, closeMinutes, 0, 0);

    if (isBefore(closeTime, openTime) || isEqual(closeTime, openTime)) {
      closeTime.setDate(closeTime.getDate() + 1);
    }

    return { open: openTime, close: closeTime };
  }

  async checkRoomAvailability(
    roomId: string,
    query: CheckRoomAvailabilityQueryDto,
  ): Promise<RoomAvailabilityResponseDto> {
    const {
      startTime: requestedStartTimeStr,
      endTime: requestedEndTimeStr,
      excludeBookingId,
    } = query;

    const requestedStartTime = new Date(requestedStartTimeStr);
    let requestedEndTime = new Date(requestedEndTimeStr);

    // Validate date parsing
    if (
      isNaN(requestedStartTime.getTime()) ||
      isNaN(requestedEndTime.getTime())
    ) {
      throw new BadRequestException('Invalid start or end time format.');
    }

    // Handle midnight crossover: if end time is earlier than start time,
    // assume end time is on the next day (common for karaoke venues)
    if (
      isBefore(requestedEndTime, requestedStartTime) ||
      isEqual(requestedEndTime, requestedStartTime)
    ) {
      requestedEndTime = new Date(
        requestedEndTime.getTime() + 24 * 60 * 60 * 1000,
      ); // Add 24 hours
    }

    // After handling midnight crossover, validate that end is actually after start
    if (!isBefore(requestedStartTime, requestedEndTime)) {
      throw new BadRequestException(
        'Requested start time must be before end time.',
      );
    }

    const room = await this.prisma.room.findUnique({
      where: { id: roomId, isActive: true },
      include: { location: true },
    });

    if (!room) {
      throw new NotFoundException(`Active room with ID ${roomId} not found.`);
    }
    if (!room.location.isActive) {
      return {
        roomId,
        requestedStartTime: formatISO(requestedStartTime),
        requestedEndTime: formatISO(requestedEndTime),
        isGenerallyAvailable: false,
        reason: 'Location is currently inactive.',
      };
    }

    const locationOperatingHours = room.location
      .operatingHours as OperatingHoursMap | null;
    const dayOfWeek = getDay(requestedStartTime);
    const dayNames = [
      'sunday',
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
    ];
    const currentDayName = dayNames[dayOfWeek];

    let dayOpenTime: Date | null = null;
    let dayCloseTime: Date | null = null;

    if (
      locationOperatingHours &&
      typeof locationOperatingHours === 'object' &&
      locationOperatingHours[currentDayName]
    ) {
      const parsedTimes = this.parseOperatingHoursRange(
        locationOperatingHours[currentDayName],
        requestedStartTime,
      );
      if (parsedTimes) {
        dayOpenTime = parsedTimes.open;
        dayCloseTime = parsedTimes.close;

        if (
          !(
            (isEqual(requestedStartTime, dayOpenTime) ||
              isAfter(requestedStartTime, dayOpenTime)) &&
            (isEqual(requestedEndTime, dayCloseTime) ||
              isBefore(requestedEndTime, dayCloseTime)) &&
            isAfter(requestedEndTime, dayOpenTime)
          )
        ) {
          return {
            roomId,
            requestedStartTime: formatISO(requestedStartTime),
            requestedEndTime: formatISO(requestedEndTime),
            isGenerallyAvailable: false,
            reason: `Requested time (${formatISO(
              requestedStartTime,
            )} - ${formatISO(
              requestedEndTime,
            )}) is outside operating hours for ${currentDayName} (${
              locationOperatingHours[currentDayName]
            }). Open: ${formatISO(dayOpenTime)}, Close: ${formatISO(
              dayCloseTime,
            )}.`,
          };
        }
      } else {
        return {
          roomId,
          requestedStartTime: formatISO(requestedStartTime),
          requestedEndTime: formatISO(requestedEndTime),
          isGenerallyAvailable: false,
          reason: `Operating hours for ${currentDayName} are not configured or invalid format.`,
        };
      }
    } else {
      return {
        roomId,
        requestedStartTime: formatISO(requestedStartTime),
        requestedEndTime: formatISO(requestedEndTime),
        isGenerallyAvailable: false,
        reason: `Location is not open on ${currentDayName} or operating hours not set.`,
      };
    }

    const blockingOverrides = await this.prisma.availabilityOverride.findMany({
      where: {
        roomId,
        isAvailable: false,
        AND: [
          { startTime: { lt: requestedEndTime } },
          { endTime: { gt: requestedStartTime } },
        ],
      },
    });

    if (blockingOverrides.length > 0) {
      return {
        roomId,
        requestedStartTime: formatISO(requestedStartTime),
        requestedEndTime: formatISO(requestedEndTime),
        isGenerallyAvailable: false,
        reason: `Room is unavailable due to an override: ${blockingOverrides[0].reason || 'Maintenance'}`,
      };
    }

    // Build the where clause for conflicting bookings
    const conflictingBookingsWhere: any = {
      roomId,
      status: {
        notIn: [
          BookingStatus.CANCELLED_BY_ADMIN,
          BookingStatus.CANCELLED_BY_USER,
          BookingStatus.NO_SHOW,
        ],
      },
      AND: [
        { startTime: { lt: requestedEndTime } },
        { endTime: { gt: requestedStartTime } },
      ],
    };

    // Exclude the current booking if excludeBookingId is provided (for update scenarios)
    if (excludeBookingId) {
      conflictingBookingsWhere.id = { not: excludeBookingId };
    }

    const conflictingBookings = await this.prisma.booking.findMany({
      where: conflictingBookingsWhere,
    });

    if (conflictingBookings.length > 0) {
      return {
        roomId,
        requestedStartTime: formatISO(requestedStartTime),
        requestedEndTime: formatISO(requestedEndTime),
        isGenerallyAvailable: false,
        reason: 'Room is already booked during the requested time.',
      };
    }

    return {
      roomId,
      requestedStartTime: formatISO(requestedStartTime),
      requestedEndTime: formatISO(requestedEndTime),
      isGenerallyAvailable: true,
    };
  }

  async findAlternativeTimeSlots(
    roomId: string,
    query: FindAlternativeTimeSlotsQueryDto,
  ): Promise<AlternativeTimeSlotsResponseDto> {
    const {
      startTime: requestedStartTimeStr,
      endTime: requestedEndTimeStr,
      searchRangeHours = 4,
      maxSuggestions = 5,
      preferredDuration = true,
    } = query;

    const requestedStartTime = new Date(requestedStartTimeStr);
    let requestedEndTime = new Date(requestedEndTimeStr);

    // Validate date parsing
    if (
      isNaN(requestedStartTime.getTime()) ||
      isNaN(requestedEndTime.getTime())
    ) {
      throw new BadRequestException('Invalid start or end time format.');
    }

    // Handle midnight crossover
    if (
      isBefore(requestedEndTime, requestedStartTime) ||
      isEqual(requestedEndTime, requestedStartTime)
    ) {
      requestedEndTime = new Date(
        requestedEndTime.getTime() + 24 * 60 * 60 * 1000,
      );
    }

    const requestedDurationMinutes = differenceInMinutes(
      requestedEndTime,
      requestedStartTime,
    );

    // Get room and location details
    const room = await this.prisma.room.findUnique({
      where: { id: roomId, isActive: true },
      include: { location: true },
    });

    if (!room) {
      throw new NotFoundException(`Active room with ID ${roomId} not found.`);
    }

    if (!room.location.isActive) {
      return {
        roomId,
        requestedStartTime: formatISO(requestedStartTime),
        requestedEndTime: formatISO(requestedEndTime),
        requestedDurationMinutes,
        alternatives: [],
        message: 'Location is currently inactive.',
      };
    }

    // Get operating hours for the day
    const locationOperatingHours = room.location
      .operatingHours as OperatingHoursMap | null;
    const dayOfWeek = getDay(requestedStartTime);
    const dayNames = [
      'sunday',
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
    ];
    const currentDayName = dayNames[dayOfWeek];

    let dayOpenTime: Date | null = null;
    let dayCloseTime: Date | null = null;

    if (
      locationOperatingHours &&
      typeof locationOperatingHours === 'object' &&
      locationOperatingHours[currentDayName]
    ) {
      const parsedTimes = this.parseOperatingHoursRange(
        locationOperatingHours[currentDayName],
        requestedStartTime,
      );
      if (parsedTimes) {
        dayOpenTime = parsedTimes.open;
        dayCloseTime = parsedTimes.close;
      }
    }

    if (!dayOpenTime || !dayCloseTime) {
      return {
        roomId,
        requestedStartTime: formatISO(requestedStartTime),
        requestedEndTime: formatISO(requestedEndTime),
        requestedDurationMinutes,
        alternatives: [],
        message: `Location is not open on ${currentDayName}.`,
      };
    }

    // Generate potential time slots within search range
    const searchStartTime = subHours(requestedStartTime, searchRangeHours);
    const searchEndTime = addHours(requestedStartTime, searchRangeHours);

    // Ensure search range is within operating hours
    const effectiveSearchStart = isAfter(searchStartTime, dayOpenTime)
      ? searchStartTime
      : dayOpenTime;
    const effectiveSearchEnd = isBefore(searchEndTime, dayCloseTime)
      ? searchEndTime
      : dayCloseTime;

    const alternatives: AlternativeTimeSlotDto[] = [];
    const timeSlotInterval = 30; // 30-minute intervals

    // Generate time slots in 30-minute intervals
    let currentSlotStart = effectiveSearchStart;

    while (
      isBefore(currentSlotStart, effectiveSearchEnd) &&
      alternatives.length < maxSuggestions
    ) {
      const currentSlotEnd = addMinutes(
        currentSlotStart,
        requestedDurationMinutes,
      );

      // Skip if this would go beyond the search range or operating hours
      if (
        isAfter(currentSlotEnd, effectiveSearchEnd) ||
        isAfter(currentSlotEnd, dayCloseTime)
      ) {
        currentSlotStart = addMinutes(currentSlotStart, timeSlotInterval);
        continue;
      }

      // Skip the originally requested time slot
      if (
        isEqual(currentSlotStart, requestedStartTime) &&
        isEqual(currentSlotEnd, requestedEndTime)
      ) {
        currentSlotStart = addMinutes(currentSlotStart, timeSlotInterval);
        continue;
      }

      // Check if this slot is available
      try {
        const availabilityCheck = await this.checkRoomAvailability(roomId, {
          startTime: formatISO(currentSlotStart),
          endTime: formatISO(currentSlotEnd),
        });

        if (availabilityCheck.isGenerallyAvailable) {
          const pricePerHour =
            typeof room.pricePerHour === 'number'
              ? room.pricePerHour
              : parseFloat(room.pricePerHour.toString());

          const slotPrice = (pricePerHour * requestedDurationMinutes) / 60;

          let reason = 'available_alternative';
          if (isBefore(currentSlotStart, requestedStartTime)) {
            reason = 'earlier_time';
          } else if (isAfter(currentSlotStart, requestedStartTime)) {
            reason = 'later_time';
          }

          alternatives.push({
            startTime: formatISO(currentSlotStart),
            endTime: formatISO(currentSlotEnd),
            durationMinutes: requestedDurationMinutes,
            price: slotPrice,
            reason,
          });
        }
      } catch (error) {
        // Skip this slot if there's an error checking availability
        console.warn(
          'Error checking availability for alternative slot:',
          error,
        );
      }

      currentSlotStart = addMinutes(currentSlotStart, timeSlotInterval);
    }

    // Sort alternatives by proximity to requested time if preferredDuration is true
    if (preferredDuration && alternatives.length > 0) {
      alternatives.sort((a, b) => {
        const aStart = new Date(a.startTime);
        const bStart = new Date(b.startTime);
        const aDistance = Math.abs(
          differenceInMinutes(aStart, requestedStartTime),
        );
        const bDistance = Math.abs(
          differenceInMinutes(bStart, requestedStartTime),
        );
        return aDistance - bDistance;
      });
    }

    let message = '';
    if (alternatives.length === 0) {
      message = `Không có thời gian khả dụng trong vòng ${searchRangeHours} giờ từ thời gian yêu cầu.`;
    } else {
      message = `Tìm thấy ${alternatives.length} thời gian thay thế khả dụng.`;
    }

    return {
      roomId,
      requestedStartTime: formatISO(requestedStartTime),
      requestedEndTime: formatISO(requestedEndTime),
      requestedDurationMinutes,
      alternatives,
      message,
    };
  }
}
