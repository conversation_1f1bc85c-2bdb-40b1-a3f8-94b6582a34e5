# Queen Karaoke Booking System - Public Frontend Specification

## Overview

This document outlines the detailed specifications for the public-facing frontend of the Queen Karaoke Booking System. The implementation must strictly follow the design language guidelines while providing an intuitive and elegant booking experience.

## Page Specifications

### 1. Homepage

#### Hero Section

- Full-width gradient background using Queens Gold palette
- Large, centered heading with luxury animation entrance
- Location search or quick-select component
- Background pattern using gold accents

```tsx
// Hero section styling
<section className="relative min-h-[600px] bg-gradient-to-br from-gold-50 via-white to-gold-100">
  <div className="absolute inset-0 bg-[url('/patterns/luxury.svg')] opacity-5" />
  <div className="container mx-auto px-4 py-16 text-center">
    <h1 className="text-display font-bold text-gold-900 mb-8">Queen Karaoke</h1>
    {/* Location selection component */}
  </div>
</section>
```

#### Location Grid

- Responsive grid layout (1 column mobile, 2 tablet, 3 desktop)
- Location cards with hover animation
- Image with overlay gradient
- Location name and quick details
- "View Rooms" CTA button

```tsx
// Location card component
<div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
  {locations.map(location => (
    <LocationCard key={location.id} className="luxury-hover" {...location} />
  ))}
</div>
```

### 2. Location/Room Listing Page

#### Location Header

- Location name and details
- Operating hours
- Contact information
- Map integration (if available)

#### Room Type Filters

- Horizontal scrollable on mobile
- Pills or tabs design
- Quick filters by capacity and price range

#### Room Grid

- Room cards with type-specific styling
- Price display with luxury font
- Capacity and amenities icons
- Quick booking CTA
- Hover state with room preview

```tsx
// Room type-specific styling
const roomTypeStyles = {
  QUEENS_EYES:
    'bg-gradient-to-br from-queens-bg via-white to-gold-50 border-queens-secondary',
  RUBY: 'bg-gradient-to-br from-ruby-bg to-white border-ruby-secondary',
  SAPPHIRE:
    'bg-gradient-to-br from-sapphire-bg to-white border-sapphire-secondary',
  // ... other room types
};
```

### 3. Room Details & Booking Page

#### Room Gallery

- Large feature image
- Thumbnail grid
- Lightbox functionality
- Smooth image transitions

#### Room Information

- Room name and type badge
- Detailed description
- Complete amenities list
- Capacity and restrictions
- Price breakdown

#### Booking Calendar

- Month view with availability indicators
- Time slot selection interface
- Price calculation display
- Mobile-optimized date picker

```tsx
// Calendar day cell styling
<div
  className={cn(
    'h-14 rounded-lg border transition-all duration-200',
    isAvailable && 'hover:border-queens-gold cursor-pointer',
    isSelected && 'bg-queens-gold text-white',
    isDisabled && 'bg-gray-100 cursor-not-allowed'
  )}
>
  {/* Day content */}
</div>
```

### 4. Booking Form

#### Guest Information

- Clean form layout
- Required field indicators
- Real-time validation
- Error states with proper feedback

#### Booking Summary

- Selected room and time
- Price breakdown
- Terms and conditions
- Payment method selection

#### Payment Integration

- PayOS.vn integration
- Loading states
- Success/failure handling
- Confirmation display

## Animation Specifications

### Micro-interactions

- Button hover: 150ms duration
- Form focus: 200ms duration
- Card hover: 300ms duration
- Modal entrance: 400ms duration

### Page Transitions

- Page entrance: 400ms fade + slide
- Content stagger: 80ms between items
- Smooth scroll behavior
- Loading state transitions

## Responsive Breakpoints

- Mobile: 375px - 639px
- Tablet: 640px - 1023px
- Desktop: 1024px+

## Accessibility Requirements

### Keyboard Navigation

- Logical tab order
- Focus indicators
- Skip links
- ARIA labels

### Screen Readers

- Proper heading hierarchy
- Alt text for images
- ARIA roles and states
- Live regions for updates

## Performance Targets

- First Contentful Paint: < 1.5s
- Time to Interactive: < 3.5s
- Largest Contentful Paint: < 2.5s
- Cumulative Layout Shift: < 0.1

## Error Handling

### Network Errors

- Retry mechanisms
- Friendly error messages
- Offline indicators
- Data recovery options

### Validation Errors

- Inline validation
- Form-level validation
- Clear error messages
- Recovery suggestions

## Testing Requirements

### Unit Tests

- Component rendering
- User interactions
- State management
- Utility functions

### Integration Tests

- Booking flow
- Payment process
- Error scenarios
- API integration

### E2E Tests

- Critical user paths
- Cross-browser testing
- Mobile testing
- Performance testing

## Deployment Checklist

- [ ] All pages responsive and tested
- [ ] Accessibility audit passed
- [ ] Performance metrics met
- [ ] Error handling verified
- [ ] Analytics integration complete
- [ ] SEO optimization done
- [ ] Security measures implemented
- [ ] Documentation updated
