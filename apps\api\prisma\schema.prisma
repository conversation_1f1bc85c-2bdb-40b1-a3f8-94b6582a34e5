// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// =============================================================================
// ENUMS
// =============================================================================

enum UserRole {
  CUSTOMER
  ADMIN
  SUPER_ADMIN
}

enum RoomTypeEnum {
  STANDARD
  RUBY
  SAPPHIRE
  QUEENS_EYES
  OPAL
  PEARL
  HALL
}

enum BookingStatus {
  PENDING_PAYMENT
  CONFIRMED
  CANCELLED_BY_USER
  CANCELLED_BY_ADMIN
  COMPLETED
  NO_SHOW
}

enum PaymentStatus {
  PENDING
  SUCCESSFUL
  FAILED
  REFUNDED
}

enum DecorStyle {
  MODERN
  CLASSIC
}

// =============================================================================
// MODELS
// =============================================================================

model User {
  id            String     @id @default(uuid())
  email         String     @unique
  passwordHash  String     // Bcrypt hashed password
  name          String?
  phoneNumber   String?    @unique
  role          UserRole   @default(CUSTOMER)
  isActive      Boolean    @default(true) // For disabling accounts
  emailVerified Boolean    @default(false)
  emailVerificationToken String?    @unique // Token sent to user for email verification
  emailVerificationTokenExpiresAt DateTime? // Expiry time for the token
  lastLogin     DateTime?  // Tracks last login time
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  bookings    Booking[]  // Relation to Bookings made by this user
  auditLogs   AuditLog[] // Admin actions performed by this user

  @@index([email])
  @@index([role])
}

model Location {
  id          String  @id @default(uuid())
  name        Json    // Localized: e.g., {"vi": "Quảng Phú", "en": "Quang Phu"}
  address     String
  description Json?   // Localized: e.g., {"vi": "Mô tả tiếng Việt", "en": "English Description"}
  imageUrl    String?
  phoneNumber String?
  email       String?
  operatingHours Json?  // e.g., { "monday": "10:00-23:00", ... }
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  rooms     Room[]     // Relation to Rooms available at this location
  bookings  Booking[]  // Relation to Bookings made for this location
  auditLogs AuditLog[] // Audit logs related to this location

  @@index([name])
}

model Room {
  id           String   @id @default(uuid())
  name         Json     // Localized: e.g., {"vi": "Giấc Mơ Ngân Hà", "en": "Galaxy Dream"}
  theme        Json?    // Localized
  description  Json?    // Localized
  capacity     Int      // Maximum number of people
  pricePerHour Decimal  // Price in VND
  images       String[] // Array of image URLs
  amenities    Json[]   // Localized: e.g., [{"vi": "TV Màn Hình Lớn", "en": "Large Screen TV"}]
  roomType     RoomTypeEnum? @default(STANDARD)
  isActive     Boolean  @default(true) // For temporarily disabling a room
  decorStyle   DecorStyle @default(MODERN)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  locationId String   // Foreign key to Location
  location   Location @relation(fields: [locationId], references: [id], onDelete: Cascade)

  bookings             Booking[]             // Relation to Bookings for this room
  availabilityOverrides AvailabilityOverride[] // Specific overrides for room availability
  auditLogs            AuditLog[]            // Audit logs related to this room

  @@index([locationId])
  @@index([name, locationId], name: "UniqueRoomNamePerLocation") // Ensure room names are unique within a location
}

model AvailabilityOverride {
  id          String   @id @default(uuid())
  roomId      String
  room        Room     @relation(fields: [roomId], references: [id], onDelete: Cascade)
  date        DateTime // Date of the override (YYYY-MM-DD)
  startTime   DateTime // Full DateTime for start (UTC)
  endTime     DateTime // Full DateTime for end (UTC)
  isAvailable Boolean  // True if making available, False if blocking
  reason      String?  // e.g., "Maintenance", "Special Event"
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  auditLogs AuditLog[] // Audit logs related to this override

  @@index([roomId, date])
}

model Booking {
  id             String        @id @default(uuid())
  bookingReference String      @unique // User-friendly, shorter booking ID, e.g., QB-240115-001

  userId         String?       // Foreign key to User (nullable for guest bookings)
  user           User?         @relation(fields: [userId], references: [id], onDelete: SetNull)

  locationId     String
  location       Location      @relation(fields: [locationId], references: [id], onDelete: Restrict) // Prevent location deletion if bookings exist

  roomId         String
  room           Room          @relation(fields: [roomId], references: [id], onDelete: Restrict)    // Prevent room deletion if bookings exist

  bookingDate    DateTime      // Date of the booking (YYYY-MM-DD)
  startTime      DateTime      // Full DateTime for booking start (UTC)
  endTime        DateTime      // Full DateTime for booking end (UTC)
  durationMinutes Int          // Calculated: endTime - startTime

  numberOfGuests Int
  totalPrice     Decimal       // Final price paid
  currency       String        @default("VND")

  status         BookingStatus @default(PENDING_PAYMENT)

  // Guest details (if userId is null)
  guestName      String?
  guestEmail     String?
  guestPhone     String?

  notes          String?       // Special requests or notes from customer or admin
  adminNotes     String?       // Internal notes by admin

  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  payment        Payment?      // Relation to Payment (one-to-one)
  auditLogs      AuditLog[]    // Audit logs related to this booking

  isReminderSent Boolean @default(false) // Added for email reminders

  @@index([userId])
  @@index([locationId])
  @@index([roomId])
  @@index([bookingDate])
  @@index([status])
  @@index([guestEmail])
  @@index([startTime, endTime, roomId], name: "RoomTimeSlotUniqueness") // Ensure no overlapping bookings for the same room
}

model Payment {
  id                String        @id @default(uuid())
  bookingId         String        @unique // Foreign key to Booking (one-to-one)
  booking           Booking       @relation(fields: [bookingId], references: [id], onDelete: Cascade)

  amount            Decimal
  currency          String        @default("VND")
  paymentGateway    String        @default("PayOS.vn")
  transactionId     String        @unique // ID from PayOS.vn
  paymentIntentId   String?       @unique // Broader intent ID from PayOS if applicable
  status            PaymentStatus @default(PENDING)
  paymentMethod     String?       // e.g., "Credit Card", "Bank Transfer" (from PayOS)
  paidAt            DateTime?     // Timestamp when payment was successful
  rawResponse       Json?         // Store the raw webhook response from PayOS for reconciliation

  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  auditLogs         AuditLog[]    // Audit logs related to this payment

  @@index([transactionId])
  @@index([status])
}

model SystemSetting {
  id          String @id @default(uuid())
  key         String @unique // e.g., "PAYOS_API_KEY_PUBLIC", "CANCELLATION_WINDOW_HOURS"
  value       String // Value of the setting (might be JSON string for complex objects, potentially localized if user-facing)
  description String? // Potentially localized if user-facing
  isSensitive Boolean @default(false) // Indicates if value should be masked or handled carefully
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  auditLogs AuditLog[] // Audit logs related to this setting

  @@index([key])
}

model AuditLog {
  id        String   @id @default(uuid())
  timestamp DateTime @default(now())
  userId    String?  // Admin user who performed the action (FK to User)
  user      User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  action    String   // e.g., "CREATE_ROOM", "UPDATE_BOOKING_STATUS", "ADMIN_LOGIN_SUCCESS"
  entity    String?  // Name of the entity affected, e.g., "Room", "Booking"
  entityId  String?  // ID of the affected entity
  details   Json?    // Additional details, e.g., old value vs new value for an update
  ipAddress String?

  // Optional direct relations if you want to query logs by entity easily
  // These are denormalized and need careful management if used.
  bookingId  String?  @map("booking_id")
  booking    Booking? @relation(fields: [bookingId], references: [id], onDelete: SetNull)

  locationId String?  @map("location_id")
  location   Location? @relation(fields: [locationId], references: [id], onDelete: SetNull)

  roomId     String?  @map("room_id")
  room       Room?    @relation(fields: [roomId], references: [id], onDelete: SetNull)

  paymentId  String?  @map("payment_id")
  payment    Payment? @relation(fields: [paymentId], references: [id], onDelete: SetNull)

  settingId  String?  @map("system_setting_id")
  setting    SystemSetting? @relation(fields: [settingId], references: [id], onDelete: SetNull)

  availabilityOverrideId String? @map("availability_override_id")
  availabilityOverride AvailabilityOverride? @relation(fields: [availabilityOverrideId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([action])
  @@index([entity, entityId])
  @@index([timestamp])
}
