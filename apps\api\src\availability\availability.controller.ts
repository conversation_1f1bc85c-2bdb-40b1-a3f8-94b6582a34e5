import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { AvailabilityService } from './availability.service';
import {
  CreateAvailabilityOverrideDto,
  UpdateAvailabilityOverrideDto,
  AvailabilityOverrideQueryDto,
  CheckRoomAvailabilityQueryDto,
  RoomAvailabilityResponseDto,
  FindAlternativeTimeSlotsQueryDto,
  AlternativeTimeSlotsResponseDto,
} from '@shared-types/dtos.types';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Public } from '../auth/decorators/public.decorator';
import { UserRole } from '@shared-types/common.types';
import { AvailabilityOverride } from '../../generated/prisma';

@Controller('availability')
export class AvailabilityController {
  constructor(private readonly availabilityService: AvailabilityService) {}

  // --- Availability Overrides CRUD ---
  @Post('overrides')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @HttpCode(HttpStatus.CREATED)
  createOverride(
    @Body() createDto: CreateAvailabilityOverrideDto,
  ): Promise<AvailabilityOverride> {
    return this.availabilityService.createOverride(createDto);
  }

  @Get('overrides')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  findAllOverrides(
    @Query() query: AvailabilityOverrideQueryDto,
  ): Promise<AvailabilityOverride[]> {
    return this.availabilityService.findAllOverrides(query);
  }

  @Get('overrides/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  findOneOverride(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<AvailabilityOverride | null> {
    return this.availabilityService.findOneOverride(id);
  }

  @Patch('overrides/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  updateOverride(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateAvailabilityOverrideDto,
  ): Promise<AvailabilityOverride> {
    return this.availabilityService.updateOverride(id, updateDto);
  }

  @Delete('overrides/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.SUPER_ADMIN)
  @HttpCode(HttpStatus.OK)
  removeOverride(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<AvailabilityOverride> {
    return this.availabilityService.removeOverride(id);
  }

  // --- Room Availability Check ---
  // This endpoint is nested under rooms for a more RESTful approach
  // e.g., /rooms/:roomId/availability/check
  // So this specific controller might only handle general availability or override management.
  // Let's adjust the path for the check to be more specific to a room.
  // We can create this endpoint directly in RoomsController or keep it here but adjust path.
  // For now, placing it here but making it clear it acts on a specific room.

  @Public() // Typically, checking availability should be public or user-role accessible
  @Get('rooms/:roomId/check')
  checkRoomAvailability(
    @Param('roomId', ParseUUIDPipe) roomId: string,
    @Query() query: CheckRoomAvailabilityQueryDto,
  ): Promise<RoomAvailabilityResponseDto> {
    return this.availabilityService.checkRoomAvailability(roomId, query);
  }

  @Public() // Public endpoint for finding alternative time slots
  @Get('rooms/:roomId/alternatives')
  findAlternativeTimeSlots(
    @Param('roomId', ParseUUIDPipe) roomId: string,
    @Query() query: FindAlternativeTimeSlotsQueryDto,
  ): Promise<AlternativeTimeSlotsResponseDto> {
    return this.availabilityService.findAlternativeTimeSlots(roomId, query);
  }
}
