# Queen Karaoke Booking System - Security Guide

This document outlines key security considerations and practices for the Queen Karaoke Booking System. Security is a continuous process, and this guide serves as a baseline.

## 1. Authentication & Authorization

### 1.1. Role-Based Access Control (RBAC)

- **Framework:** Passport.js with JWT (JSON Web Tokens) strategy for the NestJS backend.
- **Roles:**
  - `CUSTOMER`: Authenticated public users.
  - `ADMIN`: Authenticated administrative users.
  - `SUPER_ADMIN` (Optional): For managing other admin accounts and critical system settings.
- **Implementation:**

  - **JWTs:** Upon successful login, the API issues a JWT containing the user ID and role.
  - **Token Expiry:** JWTs will have a reasonable expiration time (e.g., 1-4 hours for access tokens, longer for refresh tokens if used).
  - **NestJS Guards:** Custom NestJS Guards (e.g., `RolesGuard`) will protect routes based on user roles extracted from the JWT.

    ```typescript
    // Example RolesGuard in NestJS
    import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
    import { Reflector } from '@nestjs/core';
    import { ROLES_KEY } from './roles.decorator'; // Custom decorator like @Roles(UserRole.ADMIN)
    import { UserRole } from '@prisma/client'; // Assuming UserRole enum from Prisma

    @Injectable()
    export class RolesGuard implements CanActivate {
      constructor(private reflector: Reflector) {}

      canActivate(context: ExecutionContext): boolean {
        const requiredRoles = this.reflector.getAllAndOverride<UserRole[]>(
          ROLES_KEY,
          [context.getHandler(), context.getClass()]
        );
        if (!requiredRoles) {
          return true; // No roles required, access granted
        }
        const { user } = context.switchToHttp().getRequest();
        return requiredRoles.some(role => user?.roles?.includes(role)); // Check if user.roles exists
      }
    }
    ```

  - **Password Hashing:** User passwords will be hashed using a strong algorithm like bcrypt (e.g., `bcryptjs` library) with a sufficient salt factor.
  - **Admin Authentication:** Admin login should be via a separate endpoint (e.g., `/auth/admin/login`) and may have stricter lockout policies.

### 1.2. Session Security (Primarily for API state if not purely token-based)

- **Redis for Sessions (If Applicable):** While JWTs are stateless, if any server-side session state is maintained (e.g., for specific flows or before full JWT adoption), Redis will be used for session storage. This is more scalable and robust than in-memory stores.
- **Secure Cookies (If Refresh Tokens are used via Cookies):** If refresh tokens are stored in cookies, they must be `HttpOnly`, `Secure` (requires HTTPS), and ideally `SameSite=Strict` or `Lax`.

## 2. Data Protection & Transport Security

### 2.1. HTTPS/TLS

- **Requirement:** HTTPS must be enforced for all communication between clients (web browser, mobile app) and the server (API, web frontend).
- **Implementation:** Nginx will be used as a reverse proxy to terminate SSL/TLS. SSL certificates will be obtained from a trusted Certificate Authority (CA) like Let's Encrypt (can be automated with Certbot).
- **Nginx Configuration:** Redirect all HTTP traffic to HTTPS. Use strong TLS protocols and ciphers (e.g., TLS 1.2, TLS 1.3).

### 2.2. Sensitive Data Handling

- **Payment Information:** Payment processing is outsourced to PayOS.vn. The system will NOT store raw credit card numbers or full payment credentials. Only transaction IDs and payment statuses from PayOS.vn will be stored.
- **Personally Identifiable Information (PII):** PII (names, emails, phone numbers) will be stored securely in the PostgreSQL database. Access to PII will be restricted based on roles. Consider encryption at rest for highly sensitive PII fields if required by regulations, though PostgreSQL's built-in security features and proper access controls are often sufficient.
- **API Keys & Secrets:** All API keys (PayOS.vn, email services, etc.) and database credentials will be stored as environment variables or in a secure secrets management system (e.g., HashiCorp Vault, Docker secrets, or environment variables injected by the CI/CD system), NOT hardcoded in the application.

## 3. Input Validation & Output Encoding

### 3.1. Input Validation (BE & FE)

- **Backend (NestJS):**

  - Use `class-validator` and `class-transformer` libraries for validating incoming DTOs (Data Transfer Objects) in API requests.
  - Validate data types, formats (email, phone), lengths, ranges, and check for required fields.
  - Sanitize inputs to prevent injection attacks (e.g., SQL injection, NoSQL injection - though Prisma helps prevent SQLi significantly).
  - Example DTO with validation:

    ```typescript
    import { IsEmail, IsNotEmpty, IsString, MinLength } from 'class-validator';

    export class CreateUserDto {
      @IsEmail()
      email: string;

      @IsString()
      @MinLength(8)
      password: string;

      @IsString()
      @IsNotEmpty()
      name: string;
    }
    ```

- **Frontend (Next.js):**
  - Implement client-side validation for better UX, but always rely on backend validation as the source of truth.
  - Use libraries like `zod` or form handling libraries with built-in validation (e.g., `react-hook-form` with `zod` resolver).

### 3.2. Output Encoding (FE)

- **Preventing XSS (Cross-Site Scripting):**
  - React (used by Next.js) automatically escapes dynamic content rendered in JSX, which helps prevent XSS.
  - Be cautious when using `dangerouslySetInnerHTML` – only use it with sanitized HTML.
  - Ensure any data rendered from the API into HTML attributes is properly encoded/sanitized if it's not handled by React's default mechanisms.
- **Content Security Policy (CSP):** Implement a strong CSP header via Nginx or in Next.js middleware to restrict sources of scripts, styles, images, etc., to mitigate XSS and other injection attacks.

## 4. Rate Limiting & Brute Force Protection

### 4.1. API Rate Limiting (BE)

- **Implementation:** Use a NestJS module like `@nestjs/throttler` to limit the number of requests an IP address or user can make to API endpoints within a certain time window.
- **Configuration:** Apply stricter limits to sensitive endpoints like login, registration, and password reset.
- **Storage:** Redis can be used as a distributed store for rate limiter counts in a multi-instance deployment.

### 4.2. Brute Force Protection (BE)

- **Login Endpoints:** Implement account lockout policies after a certain number of failed login attempts (e.g., lock account for 15 minutes after 5 failed attempts).
- **CAPTCHA:** Consider using CAPTCHA (e.g., hCaptcha, reCAPTCHA) on login and registration forms to prevent automated bot attacks, especially if high traffic is expected.

## 5. Logging & Monitoring

### 5.1. Audit Logging (BE)

- **Requirement:** Log critical security-related events and administrative actions.
- **Implementation:** The `AuditLog` table (see `DB_SCHEMA.md`) will store these events.
- **Events to Log:**
  - Admin logins (successful and failed).
  - CRUD operations on core entities (Locations, Rooms, Bookings by admin).
  - Changes to system settings.
  - Security policy violations (e.g., repeated rate limit hits from an IP).
  - User account status changes (activate/deactivate by admin).
- **Log Details:** Timestamp, performing user (admin), action, affected entity/ID, IP address, outcome.

### 5.2. Application & System Logging (BE & FE)

- **Backend:** Use a structured logging library (e.g., Pino or NestJS default logger configured for JSON output) to log application errors, warnings, and important informational messages.
- **Frontend:** Log critical frontend errors to a centralized logging service if necessary (e.g., Sentry, LogRocket).
- **Log Storage:** Store logs in a way that they can be aggregated and analyzed (e.g., Docker logging drivers sending to ELK stack, Datadog, or cloud provider logging services).
- **Health Checks:** Implement a health check endpoint (e.g., `/health`) in the API that can be used by Docker and Nginx to verify service status.

## 6. Error Handling

- **Generic Error Messages:** Avoid exposing detailed error messages or stack traces to end-users, especially for security-sensitive operations. Return generic error messages (e.g., "Invalid credentials," "An unexpected error occurred").
- **Specific Logs:** While user-facing errors are generic, detailed error information should be logged securely on the backend for debugging.

## 7. Dependency Management

- **Regular Updates:** Regularly update all dependencies (npm packages, OS packages, Docker base images) to patch known vulnerabilities.
- **Vulnerability Scanning:** Use tools like `npm audit` or GitHub Dependabot (or Snyk) to scan for known vulnerabilities in project dependencies.

## 8. Docker Security

- **Least Privilege:** Run Docker containers with non-root users where possible.
- **Lean Base Images:** Use minimal, official Docker base images.
- **Image Scanning:** Integrate Docker image vulnerability scanning into the CI/CD pipeline (e.g., Trivy, Snyk, or registry-provided scanning).
- **Resource Limits:** Configure resource limits (CPU, memory) for containers in `docker-compose.yml`.

## 9. Regular Security Audits & Penetration Testing

- Periodically conduct security code reviews.
- Consider engaging third-party security professionals for penetration testing, especially before major launches or if handling highly sensitive data.

By implementing these security measures, the Queen Karaoke Booking System can establish a strong security posture.
