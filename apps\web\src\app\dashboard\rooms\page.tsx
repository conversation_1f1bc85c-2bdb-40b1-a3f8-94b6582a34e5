'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Search,
  Plus,
  Building,
  Users,
  DollarSign,
  Eye,
  Edit,
  Trash2,
  Crown,
  Palette,
  CheckCircle,
  AlertCircle,
  Image,
  ActivitySquare,
} from 'lucide-react';
import { roomsService } from '../../../services/rooms.service';
import { locationsService } from '../../../services/locations.service';
import { Room, RoomQueryDto, Location, RoomTypeEnum, DecorStyle } from 'shared-types';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';
import { useDialogs } from '../../../components/ui/modal-provider';

interface RoomWithLocationInfo extends Room {
  locationInfo?: {
    id: string;
    name: any;
  };
}

interface RoomsPageState {
  rooms: RoomWithLocationInfo[];
  locations: Location[];
  loading: boolean;
  error: string | null;
  search: string;
  selectedLocation: string;
  selectedRoomType: string;
  selectedDecorStyle: string;
  selectedStatus: string;
}

export default function RoomsPage() {
  const router = useRouter();
  const { alert, confirm } = useDialogs();
  const [state, setState] = useState<RoomsPageState>({
    rooms: [],
    locations: [],
    loading: true,
    error: null,
    search: '',
    selectedLocation: '',
    selectedRoomType: '',
    selectedDecorStyle: '',
    selectedStatus: '',
  });

  const fetchLocations = async () => {
    try {
      const response = await locationsService.getLocations({
        page: 1,
        limit: 100,
      });
      console.log('[RoomsPage] Fetched locations:', response.items);
      setState(prev => ({ ...prev, locations: response.items }));
    } catch (error) {
      console.error('Error fetching locations:', error);
    }
  };

  const fetchRoomsForAllLocations = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    try {
      const queryParams: RoomQueryDto = {
        page: 1,
        limit: 100,
      };

      if (state.selectedLocation) {
        queryParams.locationId = state.selectedLocation;
      }

      if (state.selectedRoomType) {
        queryParams.roomType = state.selectedRoomType as RoomTypeEnum;
      }

      if (state.selectedDecorStyle) {
        queryParams.decorStyle = state.selectedDecorStyle as DecorStyle;
      }

      if (state.selectedStatus) {
        queryParams.isActive = state.selectedStatus === 'active';
      }

      const allRooms: RoomWithLocationInfo[] = [];

      // If a specific location is selected, only fetch rooms for that location
      if (state.selectedLocation) {
        const rooms = await roomsService.getRooms(state.selectedLocation, queryParams);
        const locationInfo = state.locations.find(
          loc => loc.id === state.selectedLocation
        );
        allRooms.push(
          ...rooms.map(room => ({
            ...room,
            locationInfo: locationInfo
              ? { id: locationInfo.id, name: locationInfo.name }
              : undefined,
          }))
        );
      } else {
        // Otherwise, fetch rooms for all locations
        for (const location of state.locations) {
          try {
            const rooms = await roomsService.getRooms(location.id, queryParams);
            allRooms.push(
              ...rooms.map(room => ({
                ...room,
                locationInfo: { id: location.id, name: location.name },
              }))
            );
          } catch (error) {
            console.error(
              `Error fetching rooms for location ${location.id}:`,
              error
            );
          }
        }
      }

      // Apply search filter if any
      const filteredRooms = state.search
        ? allRooms.filter(room =>
            roomsService
              .getRoomDisplayName(room)
              .toLowerCase()
              .includes(state.search.toLowerCase())
          )
        : allRooms;

      // Apply room type filter if any
      const roomTypeFilteredRooms = state.selectedRoomType
        ? filteredRooms.filter(
            room => room.roomType === state.selectedRoomType
          )
        : filteredRooms;

      // Apply decor style filter if any
      const decorStyleFilteredRooms = state.selectedDecorStyle
        ? roomTypeFilteredRooms.filter(
            room => room.decorStyle === state.selectedDecorStyle
          )
        : roomTypeFilteredRooms;

      // Apply status filter if any
      const statusFilteredRooms = state.selectedStatus
        ? decorStyleFilteredRooms.filter(
            room =>
              room.isActive === (state.selectedStatus === 'active')
          )
        : decorStyleFilteredRooms;

      setState(prev => ({ ...prev, rooms: statusFilteredRooms, loading: false }));
    } catch (error) {
      console.error('Error fetching rooms:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: 'Có lỗi xảy ra khi tải danh sách phòng.',
      }));
    }
  };

  useEffect(() => {
    fetchLocations();
  }, []);

  useEffect(() => {
    console.log(
      '[RoomsPage] Locations effect triggered. Locations count:',
      state.locations.length
    );
    if (state.locations.length > 0) {
      console.log(
        '[RoomsPage] Calling fetchRoomsForAllLocations because locations exist.'
      );
      fetchRoomsForAllLocations();
    } else {
      console.log(
        '[RoomsPage] Not calling fetchRoomsForAllLocations, locations count is 0.'
      );
      if (state.rooms.length > 0) {
        setState(prev => ({ ...prev, rooms: [], loading: false }));
      } else {
        setState(prev => ({ ...prev, loading: false }));
      }
    }
  }, [
    state.locations,
    state.search,
    state.selectedLocation,
    state.selectedRoomType,
    state.selectedDecorStyle,
    state.selectedStatus,
  ]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setState(prev => ({ ...prev, search: event.target.value }));
  };

  const handleDeleteRoom = async (room: RoomWithLocationInfo) => {
    if (!room.locationInfo?.id) return;

    const confirmed = await confirm(
      `Bạn có chắc chắn muốn xóa phòng "${roomsService.getRoomDisplayName(room)}"?`
    );
    if (!confirmed) return;

    try {
      await roomsService.deleteRoom(room.locationInfo.id, room.id);
      fetchRoomsForAllLocations(); // Refresh the list
    } catch (error) {
      await alert('Có lỗi xảy ra khi xóa phòng', 'Lỗi', 'error');
    }
  };

  const getLocationDisplayName = (location: any): string => {
    return location?.name?.vi || location?.name?.en || 'Không có tên';
  };

  const getRoomTypeClasses = (roomType?: RoomTypeEnum): string => {
    const baseClasses = 'px-3 py-1.5 rounded-full text-sm font-medium inline-flex items-center gap-1.5';

    switch (roomType) {
      case RoomTypeEnum.QUEENS_EYES:
        return `${baseClasses} bg-queens-50 text-queens-primary border border-queens-200`;
      case RoomTypeEnum.RUBY:
        return `${baseClasses} bg-ruby-50 text-ruby-primary border border-ruby-200`;
      case RoomTypeEnum.SAPPHIRE:
        return `${baseClasses} bg-sapphire-50 text-sapphire-primary border border-sapphire-200`;
      case RoomTypeEnum.OPAL:
        return `${baseClasses} bg-opal-50 text-opal-primary border border-opal-200`;
      case RoomTypeEnum.PEARL:
        return `${baseClasses} bg-pearl-50 text-pearl-primary border border-pearl-200`;
      case RoomTypeEnum.HALL:
        return `${baseClasses} bg-hall-50 text-hall-primary border border-hall-200`;
      case RoomTypeEnum.STANDARD:
      default:
        return `${baseClasses} bg-standard-50 text-standard-primary border border-standard-200`;
    }
  };

  const getDecorStyleClasses = (decorStyle: DecorStyle): string => {
    const baseClasses = 'px-3 py-1.5 rounded-full text-sm font-medium inline-flex items-center gap-1.5';
    
    switch (decorStyle) {
      case DecorStyle.MODERN:
        return `${baseClasses} bg-gradient-to-r from-gold-50 to-gold-100 text-gold-800 border border-gold-200`;
      case DecorStyle.CLASSIC:
        return `${baseClasses} bg-gradient-to-r from-gray-50 to-gray-100 text-gray-800 border border-gray-200`;
      default:
        return `${baseClasses} bg-gray-50 text-gray-800 border border-gray-200`;
    }
  };

  // Helper function to get current location display name
  const getCurrentLocationLabel = (): string => {
    if (!state.selectedLocation) return 'Tất cả chi nhánh';
    const location = state.locations.find(
      loc => loc.id === state.selectedLocation
    );
    return location ? getLocationDisplayName(location) : 'Tất cả chi nhánh';
  };

  // Helper function to get current room type display name
  const getCurrentRoomTypeLabel = (): string => {
    if (!state.selectedRoomType) return 'Tất cả loại phòng';
    return roomsService.getRoomTypeDisplayName(
      state.selectedRoomType as RoomTypeEnum
    );
  };

  // Helper function to get current status display name
  const getCurrentStatusLabel = (): string => {
    if (!state.selectedStatus) return 'Tất cả trạng thái';
    return state.selectedStatus === 'active'
      ? 'Đang hoạt động'
      : 'Ngừng hoạt động';
  };

  // Helper function to get current decor style display name
  const getCurrentDecorStyleLabel = (): string => {
    if (!state.selectedDecorStyle) return 'Tất cả phong cách';
    return roomsService.getDecorStyleDisplayName(
      state.selectedDecorStyle as DecorStyle
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-h1 font-bold text-gray-900">Quản Lý Phòng Hát</h1>
          <p className="text-lg text-gray-600">
            Quản lý tất cả phòng karaoke trên toàn hệ thống
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => router.push('/dashboard/locations')}
            variant="outline"
            className="gap-2 bg-queens-gold hover:bg-queens-gold/90 text-white font-medium shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
          >
            <Building className="h-4 w-4" />
            Quản Lý Chi Nhánh
          </Button>
          <Button
            onClick={() => {
              if (state.locations.length === 0) {
                router.push('/dashboard/locations/new');
              } else {
                router.push('/dashboard/rooms/new');
              }
            }}
            className="gap-2 bg-queens-gold hover:bg-queens-gold/90 text-white font-medium shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
          >
            <Plus className="h-4 w-4" />
            Thêm Phòng Mới
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6 space-y-4">
          {/* Search Row */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
            <Input
              placeholder="Tìm kiếm theo tên phòng..."
              value={state.search}
              onChange={handleSearchChange}
              className="pl-10 text-gray-900 w-full"
            />
          </div>

          {/* Filters Row */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Location Filter */}
            <Select
              value={state.selectedLocation}
              onValueChange={(value: string) =>
                setState(prev => ({ ...prev, selectedLocation: value }))
              }
            >
              <SelectTrigger className="text-black">
                <Building className="w-4 h-4 mr-2 text-gray-500" />
                <span className="text-black">{getCurrentLocationLabel()}</span>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  <span className="text-black">Tất cả chi nhánh</span>
                </SelectItem>
                {state.locations.map(location => (
                  <SelectItem key={location.id} value={location.id}>
                    <span className="text-black">
                      {getLocationDisplayName(location)}
                    </span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Room Type Filter */}
            <Select
              value={state.selectedRoomType}
              onValueChange={(value: string) =>
                setState(prev => ({ ...prev, selectedRoomType: value }))
              }
            >
              <SelectTrigger className="text-black">
                <Crown className="w-4 h-4 mr-2 text-gray-500" />
                <span className="text-black">{getCurrentRoomTypeLabel()}</span>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  <span className="text-black">Tất cả loại phòng</span>
                </SelectItem>
                {Object.values(RoomTypeEnum).map(type => (
                  <SelectItem key={type} value={type}>
                    <span className="text-black">
                      {roomsService.getRoomTypeDisplayName(type)}
                    </span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Decor Style Filter */}
            <Select
              value={state.selectedDecorStyle}
              onValueChange={(value: string) =>
                setState(prev => ({ ...prev, selectedDecorStyle: value }))
              }
            >
              <SelectTrigger className="text-black">
                <Palette className="w-4 h-4 mr-2 text-gray-500" />
                <span className="text-black">{getCurrentDecorStyleLabel()}</span>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  <span className="text-black">Tất cả phong cách</span>
                </SelectItem>
                {Object.values(DecorStyle).map(style => (
                  <SelectItem key={style} value={style}>
                    <span className="text-black">
                      {roomsService.getDecorStyleDisplayName(style)}
                    </span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Status Filter */}
            <Select
              value={state.selectedStatus}
              onValueChange={(value: string) =>
                setState(prev => ({ ...prev, selectedStatus: value }))
              }
            >
              <SelectTrigger className="text-black">
                <ActivitySquare className="w-4 h-4 mr-2 text-gray-500" />
                <span className="text-black">{getCurrentStatusLabel()}</span>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  <span className="text-black">Tất cả trạng thái</span>
                </SelectItem>
                <SelectItem value="active">
                  <span className="text-black">Đang hoạt động</span>
                </SelectItem>
                <SelectItem value="inactive">
                  <span className="text-black">Ngừng hoạt động</span>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Rooms Grid */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Danh Sách Phòng Hát ({state.rooms.length})</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {state.loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-queens-gold"></div>
            </div>
          ) : state.error ? (
            <div className="text-center py-12">
              <p className="text-red-600">{state.error}</p>
              <Button
                onClick={fetchRoomsForAllLocations}
                className="mt-4"
                variant="outline"
              >
                Thử lại
              </Button>
            </div>
          ) : state.locations.length === 0 ? (
            <div className="text-center py-12">
              <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-4">
                Bạn cần tạo chi nhánh trước khi thêm phòng
              </p>
              <Button
                onClick={() => router.push('/dashboard/locations/new')}
                className="bg-queens-gold hover:bg-queens-gold/90 text-white font-medium shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
              >
                Tạo chi nhánh đầu tiên
              </Button>
            </div>
          ) : state.rooms.length === 0 ? (
            <div className="text-center py-12">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-4">Không tìm thấy phòng nào</p>
              <Button
                onClick={() => router.push('/dashboard/rooms/new')}
                className="bg-queens-gold hover:bg-queens-gold/90 text-white font-medium shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
              >
                Tạo phòng đầu tiên
              </Button>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {state.rooms.map(room => {
                console.log(
                  'Room object on list page:',
                  JSON.stringify(room, null, 2)
                );
                console.log(
                  'Room ID:',
                  room.id,
                  'Location Info ID:',
                  room.locationInfo?.id
                );
                return (
                  <Card
                    key={room.id}
                    className="luxury-hover border border-gray-200 overflow-hidden bg-gradient-to-br from-white to-gray-50/30"
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg font-semibold text-gray-900 mb-1">
                            {roomsService.getRoomDisplayName(room)}
                          </CardTitle>
                          <p className="text-sm text-gray-600 mb-3">
                            {room.locationInfo && getLocationDisplayName(room.locationInfo)}
                          </p>
                          <div className="flex flex-wrap gap-2">
                            {room.roomType && (
                              <div className={getRoomTypeClasses(room.roomType)}>
                                <Crown className="w-4 h-4" />
                                {roomsService.getRoomTypeDisplayName(room.roomType)}
                              </div>
                            )}
                            <div className={getDecorStyleClasses(room.decorStyle)}>
                              <Palette className="w-4 h-4" />
                              {roomsService.getDecorStyleDisplayName(room.decorStyle)}
                            </div>
                          </div>
                        </div>
                        <div
                          className={`px-2.5 py-1 rounded-full text-xs font-medium flex items-center gap-1.5 ${
                            room.isActive
                              ? 'bg-green-50 text-green-700 border border-green-200'
                              : 'bg-gray-50 text-gray-700 border border-gray-200'
                          }`}
                        >
                          {room.isActive ? (
                            <>
                              <CheckCircle className="w-3.5 h-3.5" />
                              Hoạt động
                            </>
                          ) : (
                            <>
                              <AlertCircle className="w-3.5 h-3.5" />
                              Ngừng hoạt động
                            </>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-3 mb-4">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-700 font-medium flex items-center gap-1.5">
                            <Users className="h-4 w-4 text-gray-500" />
                            Sức chứa
                          </span>
                          <span className="font-semibold text-gray-900">
                            {room.capacity} người
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-700 font-medium flex items-center gap-1.5">
                            <DollarSign className="h-4 w-4 text-gray-500" />
                            Giá/giờ
                          </span>
                          <span className="font-semibold text-queens-gold text-base">
                            {roomsService.formatPrice(room.pricePerHour)}
                          </span>
                        </div>
                        {room.images && room.images.length > 0 && (
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-700 font-medium flex items-center gap-1.5">
                              <Image className="h-4 w-4 text-gray-500" />
                              Hình ảnh
                            </span>
                            <span className="font-semibold text-gray-900">
                              {room.images.length} ảnh
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/dashboard/rooms/${room.id}`)}
                          className="flex-1 bg-queens-gold hover:bg-queens-gold/90 text-white font-medium shadow-sm hover:shadow-md transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-150"
                        >
                          <Eye className="h-4 w-4 mr-1.5" />
                          Xem
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/dashboard/rooms/${room.id}/edit`)}
                          className="flex-1 bg-queens-gold hover:bg-queens-gold/90 text-white font-medium shadow-sm hover:shadow-md transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-150"
                        >
                          <Edit className="h-4 w-4 mr-1.5" />
                          Sửa
                        </Button>
                        <Button
                          variant="delete"
                          size="sm"
                          onClick={() => handleDeleteRoom(room)}
                          className="flex-1 hover:bg-red-600/90 shadow-sm hover:shadow-md transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-150"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
