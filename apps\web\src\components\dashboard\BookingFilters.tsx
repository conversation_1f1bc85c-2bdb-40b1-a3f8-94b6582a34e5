'use client';

import { useState, useEffect } from 'react';
import { BookingStatus, BookingQueryDto, Location, Room } from 'shared-types';
import {
  Search,
  Filter,
  X,
  Calendar,
  MapPin,
  Users,
  Mail,
  ActivitySquare,
  DoorOpen,
  ArrowUpDown,
} from 'lucide-react';
import { locationsService } from '../../services/locations.service';
import { roomsService } from '../../services/rooms.service';
import { Select, SelectContent, SelectItem, SelectTrigger } from '../ui/select';

interface BookingFiltersProps {
  onFiltersChange: (filters: BookingQueryDto) => void;
  initialFilters?: BookingQueryDto;
}

const BookingFilters = ({
  onFiltersChange,
  initialFilters = {},
}: BookingFiltersProps) => {
  const [filters, setFilters] = useState<BookingQueryDto>(initialFilters);
  const [locations, setLocations] = useState<Location[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);

  // Fetch locations on component mount
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const response = await locationsService.getLocations({ limit: 100 });
        setLocations(response.items || []);
      } catch (error) {
        console.error('Error fetching locations:', error);
      }
    };

    fetchLocations();
  }, []);

  // Fetch rooms when location is selected
  useEffect(() => {
    const fetchRooms = async () => {
      if (filters.locationId) {
        try {
          const response = await locationsService.getLocationRooms(
            filters.locationId
          );
          setRooms(response || []);
        } catch (error) {
          console.error('Error fetching rooms:', error);
          setRooms([]);
        }
      } else {
        setRooms([]);
      }
    };

    fetchRooms();
  }, [filters.locationId]);

  // Handle filter changes
  const handleFilterChange = (key: keyof BookingQueryDto, value: any) => {
    const newFilters = { ...filters, [key]: value };

    // Reset room filter when location changes
    if (key === 'locationId') {
      newFilters.roomId = undefined;
    }

    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  // Clear all filters except email if it's provided
  const clearFilters = () => {
    const clearedFilters: BookingQueryDto = {
      page: 1,
      limit: filters.limit || 12,
      ...(filters.guestEmail && { guestEmail: filters.guestEmail }), // Preserve email filter
    };
    setFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  // Get active filters count (excluding email, page, and limit)
  const getActiveFiltersCount = () => {
    const excludeKeys = ['page', 'limit', 'guestEmail'];
    return Object.entries(filters).filter(
      ([key, value]) =>
        !excludeKeys.includes(key) && value !== undefined && value !== ''
    ).length;
  };

  // Get status label
  const getStatusLabel = (status: BookingStatus): string => {
    const statusLabels: Record<BookingStatus, string> = {
      [BookingStatus.PENDING_PAYMENT]: 'Chờ thanh toán',
      [BookingStatus.CONFIRMED]: 'Đã xác nhận',
      [BookingStatus.COMPLETED]: 'Hoàn thành',
      [BookingStatus.CANCELLED_BY_USER]: 'Khách hủy',
      [BookingStatus.CANCELLED_BY_ADMIN]: 'Admin hủy',
      [BookingStatus.NO_SHOW]: 'Không đến',
    };
    return statusLabels[status] || status;
  };

  // Helper functions for current selections
  const getCurrentStatusLabel = (): string => {
    if (!filters.status) return 'Tất cả trạng thái';
    return getStatusLabel(filters.status as BookingStatus);
  };

  const getCurrentLocationLabel = (): string => {
    if (!filters.locationId) return 'Tất cả cơ sở';
    const location = locations.find(l => l.id === filters.locationId);
    return location?.name?.vi || location?.name?.en || 'Không xác định';
  };

  const getCurrentRoomLabel = (): string => {
    if (!filters.roomId)
      return filters.locationId ? 'Tất cả phòng' : 'Chọn cơ sở trước';
    const room = rooms.find(r => r.id === filters.roomId);
    return room?.name?.vi || room?.name?.en || 'Không xác định';
  };

  const getCurrentSortLabel = (): string => {
    const sortLabels: Record<string, string> = {
      createdAt: 'Ngày tạo',
      startTime: 'Ngày đặt',
      totalPrice: 'Giá tiền',
    };
    return sortLabels[filters.sortBy || 'createdAt'] || 'Ngày tạo';
  };

  return (
    <div className="space-y-4">
      {/* Search Row */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
        <input
          type="text"
          placeholder="Tìm kiếm theo mã đặt phòng, tên khách hàng..."
          value={filters.search || ''}
          onChange={e => handleFilterChange('search', e.target.value)}
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-queens-gold focus:border-transparent text-gray-900"
        />
      </div>

      {/* Filters Row */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Status Filter */}
        <Select
          value={filters.status || ''}
          onValueChange={value =>
            handleFilterChange('status', value || undefined)
          }
        >
          <SelectTrigger className="text-black">
            <ActivitySquare className="w-4 h-4 mr-2 text-gray-500" />
            <span className="text-black">{getCurrentStatusLabel()}</span>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">
              <span className="text-black">Tất cả trạng thái</span>
            </SelectItem>
            {Object.values(BookingStatus).map(status => (
              <SelectItem key={status} value={status}>
                <span className="text-black">{getStatusLabel(status)}</span>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Location Filter */}
        <Select
          value={filters.locationId || ''}
          onValueChange={value =>
            handleFilterChange('locationId', value || undefined)
          }
        >
          <SelectTrigger className="text-black">
            <MapPin className="w-4 h-4 mr-2 text-gray-500" />
            <span className="text-black">{getCurrentLocationLabel()}</span>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">
              <span className="text-black">Tất cả cơ sở</span>
            </SelectItem>
            {locations.map(location => (
              <SelectItem key={location.id} value={location.id}>
                <span className="text-black">
                  {location.name?.vi || location.name?.en || 'Không có tên'}
                </span>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Room Filter */}
        <Select
          value={filters.roomId || ''}
          onValueChange={value =>
            handleFilterChange('roomId', value || undefined)
          }
        >
          <SelectTrigger className="text-black">
            <DoorOpen className="w-4 h-4 mr-2 text-gray-500" />
            <span className="text-black">{getCurrentRoomLabel()}</span>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">
              <span className="text-black">Tất cả phòng</span>
            </SelectItem>
            {rooms.map(room => (
              <SelectItem key={room.id} value={room.id}>
                <span className="text-black">
                  {room.name?.vi || room.name?.en || 'Không có tên'}
                </span>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Sort Filter */}
        <Select
          value={filters.sortBy || 'createdAt'}
          onValueChange={value => handleFilterChange('sortBy', value)}
        >
          <SelectTrigger className="text-black">
            <ArrowUpDown className="w-4 h-4 mr-2 text-gray-500" />
            <span className="text-black">{getCurrentSortLabel()}</span>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="createdAt">
              <span className="text-black">Ngày tạo</span>
            </SelectItem>
            <SelectItem value="startTime">
              <span className="text-black">Ngày đặt</span>
            </SelectItem>
            <SelectItem value="totalPrice">
              <span className="text-black">Giá tiền</span>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Active Filters */}
      {getActiveFiltersCount() > 0 && (
        <div className="flex items-center justify-end gap-2">
          <span className="text-sm text-gray-600">
            {getActiveFiltersCount()} bộ lọc đang áp dụng
          </span>
          <button
            onClick={clearFilters}
            className="flex items-center gap-1.5 text-sm text-gray-700 hover:text-gray-900"
          >
            <X className="w-4 h-4" />
            Xóa bộ lọc
          </button>
        </div>
      )}
    </div>
  );
};

export default BookingFilters;
