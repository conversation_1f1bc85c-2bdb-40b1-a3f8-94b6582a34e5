import type { Metada<PERSON> } from 'next';
import { Roboto_Condensed } from 'next/font/google';
import './globals.css';
import { AuthInitializer } from '../components/AuthInitializer';
import { ModalProvider } from '../components/ui/modal-provider';

const robotoCondensed = Roboto_Condensed({
  variable: '--font-roboto-condensed',
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'Queen Karaoke Booking System',
  description: 'Luxury karaoke room booking system',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${robotoCondensed.variable} antialiased`}>
        <ModalProvider>
          <AuthInitializer />
          {children}
        </ModalProvider>
      </body>
    </html>
  );
}
