import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  CreateRoomDto,
  UpdateRoomDto,
  RoomQueryDto,
} from '@shared-types/dtos.types';
import { Prisma, Room, RoomTypeEnum } from '../../generated/prisma';
import { CloudinaryService } from '../cloudinary/cloudinary.service';
// import { LocalizedString } from '@shared-types/common.types'; // Commented out as it might be unused due to type inference

@Injectable()
export class RoomsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly cloudinaryService: CloudinaryService,
  ) {}

  private async validateLocation(locationId: string): Promise<void> {
    const location = await this.prisma.location.findUnique({
      where: { id: locationId, isActive: true },
    });
    if (!location) {
      throw new NotFoundException(
        `Active location with ID "${locationId}" not found.`,
      );
    }
  }

  async create(
    locationId: string,
    createRoomDto: CreateRoomDto,
  ): Promise<Room> {
    await this.validateLocation(locationId);

    const {
      name,
      theme,
      description,
      capacity,
      pricePerHour,
      images,
      amenities,
      roomType,
    } = createRoomDto;

    // name from createRoomDto is LocalizedString
    const existingRoom = await this.prisma.room.findFirst({
      where: {
        locationId,
        name: { path: ['en'], equals: name.en }, // Accessing .en directly implies 'name' is LocalizedString
      },
    });

    if (existingRoom) {
      throw new BadRequestException(
        `A room with the name "${name.en}" already exists in this location.`,
      );
    }

    try {
      const room = await this.prisma.room.create({
        data: {
          location: { connect: { id: locationId } },
          name: name as unknown as Prisma.InputJsonValue, // 'name' is LocalizedString here
          theme: theme as unknown as Prisma.InputJsonValue, // 'theme' is LocalizedString | undefined
          description: description as unknown as Prisma.InputJsonValue, // 'description' is LocalizedString | undefined
          capacity,
          pricePerHour,
          images: images || [],
          amenities: amenities
            ? amenities.map((a) => a as unknown as Prisma.InputJsonValue) // 'a' here is LocalizedString
            : [],
          roomType: roomType || RoomTypeEnum.STANDARD,
          isActive: true,
        },
      });
      return room;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new BadRequestException(
            'A room with similar details already exists due to a unique constraint violation.',
          );
        }
      }
      const message =
        error instanceof Error ? error.message : 'Could not create room.';
      throw new BadRequestException('Could not create room.', message);
    }
  }

  async uploadRoomImages(
    locationId: string,
    roomId: string,
    files: Express.Multer.File[],
  ): Promise<Room> {
    await this.validateLocation(locationId);
    const room = await this.prisma.room.findUnique({
      where: { id: roomId, locationId },
    });

    if (!room) {
      throw new NotFoundException(
        `Room with ID "${roomId}" not found in location "${locationId}".`,
      );
    }

    if (!files || files.length === 0) {
      throw new BadRequestException('No image files provided for upload.');
    }

    const uploadedImageUrls: string[] = [];
    const existingImagePublicIds = room.images
      .map((url) => {
        // Attempt to extract public_id from Cloudinary URL
        // Example URL: http://res.cloudinary.com/cloud_name/image/upload/v12345/folder/public_id.jpg
        // A more robust regex might be needed depending on exact URL structure and transformations
        const match = url.match(/upload\/(?:v\d+\/)?(?:[^/]+\/)*([^.]+)/);
        return match ? match[1] : null;
      })
      .filter((id) => id !== null);

    try {
      for (const file of files) {
        const result = await this.cloudinaryService.uploadImage(
          file,
          `rooms/${roomId}`, // Optional: specific folder in Cloudinary for room images
        );
        if (result && result.secure_url) {
          uploadedImageUrls.push(result.secure_url);
        } else {
          console.warn('Cloudinary upload result missing secure_url:', result);
        }
      }

      for (const publicId of existingImagePublicIds) {
        if (publicId) {
          try {
            // Construct the full public_id including the folder path
            const fullPublicId = `rooms/${roomId}/${publicId}`;
            await this.cloudinaryService.deleteImage(fullPublicId);
          } catch (deleteError) {
            console.error(
              `Failed to delete old image ${publicId} from Cloudinary:`,
              deleteError,
            );
          }
        }
      }

      return this.prisma.room.update({
        where: { id: roomId },
        data: { images: uploadedImageUrls },
      });
    } catch (error) {
      for (const url of uploadedImageUrls) {
        const match = url.match(/upload\/(?:v\d+\/)?(?:[^/]+\/)*([^.]+)/);
        // Extract the public_id including the folder, relative to the Cloudinary root
        // Example: if url is http://res.cloudinary.com/demo/image/upload/rooms/room123/sample.jpg
        // publicIdWithFolder should be rooms/room123/sample (without extension)
        const publicIdWithFolder =
          match && match[0]
            ? match[0]
                .substring(match[0].indexOf('upload/') + 'upload/'.length)
                .split('.')[0]
            : null;

        if (publicIdWithFolder) {
          try {
            await this.cloudinaryService.deleteImage(publicIdWithFolder);
          } catch (cleanupError) {
            console.error(
              'Failed to cleanup uploaded image during error handling:',
              cleanupError,
            );
          }
        }
      }
      const message =
        error instanceof Error ? error.message : 'Could not upload images.';
      throw new InternalServerErrorException(
        'Failed to upload images.',
        message,
      );
    }
  }

  async findAll(locationId: string, query: RoomQueryDto): Promise<Room[]> {
    await this.validateLocation(locationId);
    const {
      search,
      sortBy,
      sortOrder,
      page,
      limit,
      isActive,
      minCapacity,
      maxCapacity,
      minPrice,
      maxPrice,
      roomType,
    } = query;
    const pageNum = page ? parseInt(String(page), 10) : 1;
    const limitNum = limit ? parseInt(String(limit), 10) : 10;
    const skip = (pageNum - 1) * limitNum;
    const take = limitNum;

    const where: Prisma.RoomWhereInput = {
      locationId,
      isActive:
        isActive === undefined
          ? undefined
          : String(isActive).toLowerCase() === 'true',
    };

    if (roomType) {
      where.roomType = roomType;
    }

    if (search) {
      where.OR = [
        {
          name: { path: ['vi'], string_contains: search, mode: 'insensitive' },
        },
        {
          name: { path: ['en'], string_contains: search, mode: 'insensitive' },
        },
        {
          description: {
            path: ['vi'],
            string_contains: search,
            mode: 'insensitive',
          },
        },
        {
          description: {
            path: ['en'],
            string_contains: search,
            mode: 'insensitive',
          },
        },
        {
          theme: { path: ['vi'], string_contains: search, mode: 'insensitive' },
        },
        {
          theme: { path: ['en'], string_contains: search, mode: 'insensitive' },
        },
      ];
    }

    if (minCapacity !== undefined) {
      where.capacity = {
        ...(where.capacity as Prisma.IntFilter),
        gte: Number(minCapacity),
      };
    }
    if (maxCapacity !== undefined) {
      where.capacity = {
        ...(where.capacity as Prisma.IntFilter),
        lte: Number(maxCapacity),
      };
    }
    if (minPrice !== undefined) {
      where.pricePerHour = {
        ...(where.pricePerHour as Prisma.DecimalFilter),
        gte: Number(minPrice),
      };
    }
    if (maxPrice !== undefined) {
      where.pricePerHour = {
        ...(where.pricePerHour as Prisma.DecimalFilter),
        lte: Number(maxPrice),
      };
    }

    const orderBy: Prisma.RoomOrderByWithRelationInput = {};
    if (sortBy) {
      if (sortBy === 'name.en' || sortBy === 'name.vi' || sortBy === 'name') {
        const lang = sortBy.split('.')[1] || 'en';
        orderBy.name = { [lang]: sortOrder || 'asc' } as any;
      } else if (Object.keys(this.prisma.room.fields).includes(sortBy)) {
        orderBy[sortBy] = sortOrder || 'asc';
      }
    } else {
      orderBy.createdAt = 'desc';
    }

    return this.prisma.room.findMany({
      where,
      skip,
      take,
      orderBy,
    });
  }

  async findOne(locationId: string, id: string): Promise<Room | null> {
    await this.validateLocation(locationId);
    const room = await this.prisma.room.findUnique({
      where: { id, locationId, isActive: true },
    });
    if (!room) {
      throw new NotFoundException(
        `Active room with ID "${id}" in location "${locationId}" not found.`,
      );
    }
    return room;
  }

  async findOneIncludingInactive(
    locationId: string,
    id: string,
  ): Promise<Room | null> {
    await this.validateLocation(locationId);
    const room = await this.prisma.room.findUnique({
      where: { id, locationId },
    });
    if (!room) {
      throw new NotFoundException(
        `Room with ID "${id}" in location "${locationId}" not found.`,
      );
    }
    return room;
  }

  async update(
    locationId: string,
    id: string,
    updateRoomDto: UpdateRoomDto,
  ): Promise<Room> {
    await this.validateLocation(locationId);
    const roomToUpdate = await this.prisma.room.findUnique({
      where: { id, locationId },
    });
    if (!roomToUpdate) {
      throw new NotFoundException(
        `Room with ID "${id}" not found in location "${locationId}".`,
      );
    }

    const {
      name,
      theme,
      description,
      capacity,
      pricePerHour,
      images,
      amenities,
      isActive,
      roomType,
      decorStyle,
    } = updateRoomDto;

    // name from updateRoomDto is LocalizedString | undefined
    if (name) {
      const existingRoomWithSameName = await this.prisma.room.findFirst({
        where: {
          locationId,
          name: { path: ['en'], equals: name.en },
          id: { not: id },
        },
      });
      if (existingRoomWithSameName) {
        throw new BadRequestException(
          `Another room with the name "${name.en}" already exists in this location.`,
        );
      }
    }

    try {
      return this.prisma.room.update({
        where: { id, locationId },
        data: {
          name: name ? (name as unknown as Prisma.InputJsonValue) : undefined,
          theme: theme
            ? (theme as unknown as Prisma.InputJsonValue)
            : undefined,
          description: description
            ? (description as unknown as Prisma.InputJsonValue)
            : undefined,
          capacity,
          pricePerHour,
          images,
          amenities: amenities
            ? amenities.map((a) => a as unknown as Prisma.InputJsonValue)
            : undefined,
          roomType,
          decorStyle,
          isActive,
        },
      });
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2002'
      ) {
        throw new BadRequestException(
          'Update violates a unique constraint. Another room with similar identifying details might exist.',
        );
      }
      const message =
        error instanceof Error ? error.message : 'Could not update room.';
      throw new BadRequestException('Could not update room.', message);
    }
  }

  async remove(locationId: string, id: string): Promise<Room> {
    await this.validateLocation(locationId);
    const roomToRemove = await this.prisma.room.findUnique({
      where: { id, locationId },
    });

    if (!roomToRemove) {
      throw new NotFoundException(
        `Room with ID "${id}" not found in location "${locationId}".`,
      );
    }

    if (!roomToRemove.isActive) {
      throw new BadRequestException(
        `Room with ID "${id}" is already inactive.`,
      );
    }

    return this.prisma.room.update({
      where: { id, locationId },
      data: { isActive: false },
    });
  }
}
