import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gold-600 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 button-hover',
  {
    variants: {
      variant: {
        default:
          'bg-gold-600 text-white shadow-md hover:bg-gold-700 hover:shadow-lg active:bg-gold-800 active:shadow-sm',
        premium:
          'bg-gradient-to-r from-gold-600 to-gold-700 text-white font-semibold shadow-lg hover:from-gold-700 hover:to-gold-800 hover:shadow-xl hover:-translate-y-0.5',
        secondary:
          'bg-white border-2 border-gold-600 text-gold-700 shadow-sm hover:bg-gold-50 hover:border-gold-700 hover:text-gold-800 hover:shadow-md',
        destructive:
          'bg-red-600 text-white shadow-md hover:bg-red-700 hover:shadow-lg',
        delete:
          'bg-red-600 text-white font-medium shadow-md hover:bg-red-700 hover:shadow-sm transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-150',
        outline:
          'border border-gold-200 bg-white hover:bg-gold-50 hover:text-gold-800 text-gold-700',
        ghost: 'text-gray-700 hover:bg-gold-50 hover:text-gold-800',
        link: 'text-gold-600 underline-offset-4 hover:underline hover:text-gold-700',
        action:
          'bg-gold-500 text-white font-medium shadow-md hover:bg-gold-600 hover:shadow-lg active:bg-gold-800 transform transition-all duration-200 hover:scale-[1.02]',
        growth:
          'bg-queens-primary text-white font-medium shadow-md hover:bg-queens-secondary hover:shadow-lg active:bg-queens-600 transform transition-all duration-200 hover:scale-[1.02]',
        sapphire:
          'bg-sapphire-primary text-white font-medium shadow-md hover:bg-sapphire-secondary hover:shadow-lg active:bg-sapphire-600 transform transition-all duration-200 hover:scale-[1.02]',
        ruby: 'bg-ruby-primary text-white font-medium shadow-md hover:bg-ruby-secondary hover:shadow-lg active:bg-ruby-600 transform transition-all duration-200 hover:scale-[1.02]',
        opal: 'bg-opal-primary text-white font-medium shadow-md hover:bg-opal-secondary hover:shadow-lg active:bg-opal-600 transform transition-all duration-200 hover:scale-[1.02]',
      },
      size: {
        default: 'h-10 px-6 py-3',
        sm: 'h-8 px-4 py-2 text-xs',
        lg: 'h-12 px-8 py-4',
        xl: 'h-14 px-10 py-5 text-lg',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
