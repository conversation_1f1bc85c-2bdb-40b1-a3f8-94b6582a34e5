# Queen Karaoke Booking System - API Specification

This document outlines the REST API endpoints for the Queen Karaoke Booking System. All request and response bodies are in JSON format.

**Authentication:**

- Endpoints marked with `Auth: Public` are accessible without authentication.
- Endpoints marked with `Auth: Customer` require a valid customer JWT token in the `Authorization` header (`Bearer <token>`).
- Endpoints marked with `Auth: Admin` require a valid admin JWT token in the `Authorization` header (`Bearer <token>`).

**Internationalization (i18n):**

- The API supports bilingual content: Vietnamese (`vi`) as the primary language and English (`en`) as the secondary.
- Clients should specify their preferred language using the `Accept-Language` HTTP header (e.g., `Accept-Language: vi-VN`, `Accept-Language: en-US`, `Accept-Language: en`).
- If the header is omitted or an unsupported language is requested, the API will default to Vietnamese (`vi`).
- Endpoints returning translatable content (e.g., names, descriptions, themes) will respect this header.

## Domain: `/auth`

Handles user authentication and registration.

### 1. Customer Registration

- **Endpoint:** `POST /auth/register`
- **Auth:** `Public`
- **Request Body:**
  ```json
  {
    "email": "<EMAIL>",
    "password": "Str0ngP@sswOrd!",
    "name": "John Doe",
    "phoneNumber": "0123456789"
  }
  ```
- **Response Body (Success 201):**
  ```json
  {
    "userId": "uuid-string",
    "email": "<EMAIL>",
    "name": "John Doe",
    "message": "Registration successful. Please check your email for verification if enabled."
  }
  ```
- \*\*Response Body (Error 400 - Validation, Error 409 - Email Exists):
  ```json
  {
    "statusCode": 400,
    "message": ["email must be an email"],
    "error": "Bad Request"
  }
  ```

### 2. Customer Login

- **Endpoint:** `POST /auth/login`
- **Auth:** `Public`
- **Request Body:**
  ```json
  {
    "email": "<EMAIL>",
    "password": "Str0ngP@sswOrd!"
  }
  ```
- **Response Body (Success 200):**
  ```json
  {
    "accessToken": "jwt.token.string",
    "user": {
      "userId": "uuid-string",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "customer"
    }
  }
  ```
- \*\*Response Body (Error 401 - Invalid Credentials):
  ```json
  {
    "statusCode": 401,
    "message": "Invalid credentials",
    "error": "Unauthorized"
  }
  ```

### 3. Admin Login

- **Endpoint:** `POST /auth/admin/login`
- **Auth:** `Public`
- **Request Body:**
  ```json
  {
    "username": "admin_user", // Or email, depending on admin setup
    "password": "S3cureAdminP@ss!"
  }
  ```
- **Response Body (Success 200):**
  ```json
  {
    "accessToken": "jwt.admin.token.string",
    "user": {
      "adminId": "uuid-string",
      "username": "admin_user",
      "role": "admin"
    }
  }
  ```
- **Response Body (Error 401 - Invalid Credentials):** (Same as customer login error)

### 4. Get Current User Profile

- **Endpoint:** `GET /auth/me`
- **Auth:** `Customer` or `Admin` (depending on token)
- **Request Body:** None
- **Response Body (Success 200 - Customer):**
  ```json
  {
    "userId": "uuid-string",
    "email": "<EMAIL>",
    "name": "John Doe",
    "phoneNumber": "0123456789",
    "role": "customer"
  }
  ```
- **Response Body (Success 200 - Admin):**
  ```json
  {
    "adminId": "uuid-string",
    "username": "admin_user",
    "role": "admin"
    // other admin-specific fields
  }
  ```
- **Response Body (Error 401 - Unauthorized):** (If token is invalid or missing)

### 5. Update Customer Profile

- **Endpoint:** `PUT /auth/me`
- **Auth:** `Customer`
- **Request Body:**
  ```json
  {
    "name": "Johnathan Doe",
    "phoneNumber": "0987654321" // Only fields to be updated
  }
  ```
- **Response Body (Success 200):** Updated customer profile (similar to `GET /auth/me` response for customer)
- \*\*Response Body (Error 400 - Validation, Error 401 - Unauthorized):

### 6. Change Password

- **Endpoint:** `PUT /auth/change-password`
- **Auth:** `Customer` or `Admin`
- **Request Body:**
  ```json
  {
    "currentPassword": "OldP@sswOrd!",
    "newPassword": "NewStr0ngP@sswOrd!"
  }
  ```
- **Response Body (Success 200):**
  ```json
  {
    "message": "Password updated successfully"
  }
  ```
- \*\*Response Body (Error 400 - Validation, Error 401 - Incorrect current password):

## Domain: `/locations`

Handles karaoke locations.

### 1. Get All Locations

- **Endpoint:** `GET /locations`
- **Auth:** `Public`
- **Request Body:** None
- **Response Body (Success 200):**
  ```json
  [
    {
      "locationId": "uuid-location-1",
      "name": "Quảng Phú",
      "address": "123 Main St, Quảng Phú",
      "description": "Vibrant karaoke spot in the heart of Quảng Phú.",
      "imageUrl": "/images/quangphu.jpg"
    },
    {
      "locationId": "uuid-location-2",
      "name": "Buôn Ma Thuột",
      "address": "456 Center Rd, Buôn Ma Thuột",
      "description": "Spacious rooms with modern themes.",
      "imageUrl": "/images/bmt.jpg"
    }
    // ... more locations
  ]
  ```

### 2. Get Location by ID

- **Endpoint:** `GET /locations/{locationId}`
- **Auth:** `Public`
- **Headers:** `Accept-Language` (optional, defaults to `vi`)
- **Request Body:** None
- **Response Body (Success 200):** Single location object (structure as above, with fields like `name`, `description` localized based on `Accept-Language` header).
- \*\*Response Body (Error 404 - Not Found):

## Domain: `/rooms`

Handles karaoke rooms.

### 1. Get Rooms by Location

- **Endpoint:** `GET /rooms?locationId={locationId}`
- **Auth:** `Public`
- **Headers:** `Accept-Language` (optional, defaults to `vi`)
- **Query Parameters:**
  - `locationId` (string, required): ID of the location.
  - `date` (string, optional, YYYY-MM-DD): To check availability for a specific date.
  - `startTime` (string, optional, HH:MM): To check availability for a specific time.
  - `partySize` (number, optional): To filter rooms by capacity.
- **Request Body:** None
- **Response Body (Success 200):**
  ```json
  [
    {
      "roomId": "uuid-room-1",
      "locationId": "uuid-location-1",
      "name": "Galaxy Dream",
      "theme": "Space Adventure",
      "description": "Explore the cosmos in this starry room.",
      "capacity": 10,
      "pricePerHour": 200000, // VND
      "images": ["/images/room1_1.jpg", "/images/room1_2.jpg"],
      "amenities": ["Large Screen TV", "Wireless Mics", "Song Book"],
      "isAvailable": true // If date/time provided in query
    }
    // ... more rooms (name, theme, description will be localized)
  ]
  ```

### 2. Get Room by ID

- **Endpoint:** `GET /rooms/{roomId}`
- **Auth:** `Public`
- **Headers:** `Accept-Language` (optional, defaults to `vi`)
- **Request Body:** None
- **Response Body (Success 200):** Single room object (structure as above, with fields like `name`, `theme`, `description` localized).
- \*\*Response Body (Error 404 - Not Found):

### 3. Get Room Availability (Time Slots)

- **Endpoint:** `GET /rooms/{roomId}/availability?date={YYYY-MM-DD}`
- **Auth:** `Public`
- **Headers:** `Accept-Language` (optional, defaults to `vi`)
- **Query Parameters:**
  - `date` (string, required, YYYY-MM-DD): The date to check availability for.
- **Request Body:** None
- **Response Body (Success 200):**
  ```json
  {
    "roomId": "uuid-room-1",
    "date": "2024-07-28",
    "timeSlots": [
      {
        "startTime": "10:00",
        "endTime": "11:00",
        "isAvailable": true,
        "price": 200000
      },
      {
        "startTime": "11:00",
        "endTime": "12:00",
        "isAvailable": false,
        "price": 200000
      },
      {
        "startTime": "12:00",
        "endTime": "13:00",
        "isAvailable": true,
        "price": 200000
      }
      // ... more slots for the day
    ]
  }
  ```
- \*\*Response Body (Error 400 - Invalid Date, Error 404 - Room Not Found):

## Domain: `/bookings`

Handles karaoke bookings.

### 1. Create Booking

- **Endpoint:** `POST /bookings`
- **Auth:** `Customer` or `Public` (if public booking is allowed directly and user details are collected in this step)
- **Request Body:**
  ```json
  {
    "roomId": "uuid-room-1",
    "locationId": "uuid-location-1",
    "bookingDate": "2024-07-28", // YYYY-MM-DD
    "startTime": "14:00", // HH:MM
    "endTime": "16:00", // HH:MM (duration can be derived or endTime provided)
    "numberOfGuests": 5,
    // User details if not authenticated or if admin is booking for someone
    "customerName": "Guest User", // Required if booking as guest
    "customerEmail": "<EMAIL>", // Required if booking as guest
    "customerPhone": "0123456780", // Required if booking as guest
    "totalPrice": 400000 // Calculated on frontend, validated on backend
  }
  ```
- **Response Body (Success 201):**
  ```json
  {
    "bookingId": "uuid-booking-1",
    "userId": "uuid-user-1", // Null if guest booking
    "roomId": "uuid-room-1",
    "locationName": "Quảng Phú",
    "roomName": "Galaxy Dream",
    "bookingDate": "2024-07-28",
    "startTime": "14:00",
    "endTime": "16:00",
    "numberOfGuests": 5,
    "totalPrice": 400000,
    "status": "PENDING_PAYMENT", // or "CONFIRMED" if payment is not a separate step via redirect
    "paymentIntentId": "payos_intent_id_string", // If PayOS intent created
    "paymentUrl": "https://payos.vn/checkout/...." // If PayOS redirect needed
  }
  ```
- \*\*Response Body (Error 400 - Validation, Error 409 - Slot Unavailable, Error 404 - Room/Location Not Found):

### 2. Get Customer's Bookings

- **Endpoint:** `GET /bookings/my-bookings`
- **Auth:** `Customer`
- **Query Parameters:**
  - `status` (string, optional): Filter by status (e.g., `upcoming`, `past`, `cancelled`)
- **Request Body:** None
- **Response Body (Success 200):**
  ```json
  [
    {
      "bookingId": "uuid-booking-1",
      "locationName": "Quảng Phú",
      "roomName": "Galaxy Dream",
      "bookingDate": "2024-07-28",
      "startTime": "14:00",
      "endTime": "16:00",
      "totalPrice": 400000,
      "status": "CONFIRMED"
    }
    // ... more bookings
  ]
  ```

### 3. Get Booking Details by ID (Customer)

- **Endpoint:** `GET /bookings/{bookingId}`
- **Auth:** `Customer` (can only access their own bookings)
- **Request Body:** None
- **Response Body (Success 200):** Single booking object (detailed structure like create response, including payment status)
- \*\*Response Body (Error 403 - Forbidden, Error 404 - Not Found):

### 4. Cancel Booking (Customer)

- **Endpoint:** `PATCH /bookings/{bookingId}/cancel`
- **Auth:** `Customer`
- **Request Body:** None (or optional reason `{"reason": "Change of plans"}`)
- **Response Body (Success 200):**
  ```json
  {
    "message": "Booking cancelled successfully",
    "bookingId": "uuid-booking-1",
    "newStatus": "CANCELLED"
  }
  ```
- \*\*Response Body (Error 400 - Cancellation policy violation, Error 403 - Forbidden, Error 404 - Not Found):

### 5. Payment Confirmation Webhook (from PayOS.vn)

- **Endpoint:** `POST /bookings/payment/webhook`
- **Auth:** `Public` (Secured by PayOS signature verification)
- **Request Body:** (Structure defined by PayOS.vn)
  ```json
  // Example structure, actual will depend on PayOS
  {
    "orderCode": "payos_order_id_related_to_bookingId",
    "amount": 400000,
    "status": "PAID",
    "signature": "payos_signature_string"
    // ... other payment details
  }
  ```
- **Response Body (Success 200):**
  ```json
  {
    "message": "Webhook received and processed successfully"
  }
  ```
- **Action:** Verify signature, update booking status to `CONFIRMED`, send confirmation email.

## Domain: `/admin` (All endpoints require `Auth: Admin`)

### Admin: Locations

- **`POST /admin/locations`**: Create Location
  - Request: `{ name, address, description, imageUrl }`
  - Response (201): Created Location object.
- **`PUT /admin/locations/{locationId}`**: Update Location
  - Request: `{ name?, address?, description?, imageUrl? }`
  - Response (200): Updated Location object.
- **`DELETE /admin/locations/{locationId}`**: Delete Location
  - Response (204): No content.
  - Note: Consider soft delete or checks for associated rooms/bookings.

### Admin: Rooms

- **`GET /admin/rooms?locationId={id}`**: Get all rooms (optionally filtered by location)
  - Response (200): Array of Room objects (detailed admin view, including `isActive` flag).
- **`POST /admin/rooms`**: Create Room
  - Request: `{ locationId, name, theme, description, capacity, pricePerHour, images: [], amenities: [], isActive: true }`
  - Response (201): Created Room object.
- **`GET /admin/rooms/{roomId}`**: Get Room by ID (Admin)
  - Response (200): Single Room object (detailed admin view).
- **`PUT /admin/rooms/{roomId}`**: Update Room
  - Request: (Partial Room object - any field from create)
  - Response (200): Updated Room object.
- **`DELETE /admin/rooms/{roomId}`**: Delete Room
  - Response (204): No content.
  - Note: Consider soft delete or checks for active bookings.

### Admin: Room Availability / Time Slots

- **`GET /admin/rooms/{roomId}/availability-override`**: Get availability overrides for a room.
  - Response (200): `[{ date, startTime, endTime, isAvailable, reason? }]`
- **`POST /admin/rooms/{roomId}/availability-override`**: Create/Update an availability override (block/unblock specific slots).
  - Request: `{ date, startTime, endTime, isAvailable, reason? }`
  - Response (201 or 200): Updated/created override confirmation.
- **`DELETE /admin/rooms/{roomId}/availability-override/{overrideId}`**: Delete an override.
  - Response (204): No content.

### Admin: Bookings

- **`GET /admin/bookings`**: Get All Bookings (with filters)
  - Query Params: `locationId`, `roomId`, `dateFrom`, `dateTo`, `status`, `customerEmail`, `page`, `limit`
  - Response (200): Paginated list of all bookings with full details.
- **`POST /admin/bookings`**: Manually Create Booking (Admin)
  - Request: Similar to customer booking creation, but admin can specify user or book for guest, and potentially override price/status.
  - Response (201): Created booking object.
- **`GET /admin/bookings/{bookingId}`**: Get Booking by ID (Admin)
  - Response (200): Full booking details.
- **`PUT /admin/bookings/{bookingId}`**: Update Booking (Admin)
  - Request: `{ roomId?, bookingDate?, startTime?, endTime?, numberOfGuests?, status?, totalPrice?, notes? }`
  - Response (200): Updated booking object. (Log changes)
- **`PATCH /admin/bookings/{bookingId}/status`**: Update Booking Status (Admin)
  - Request: `{ "status": "CONFIRMED" | "CANCELLED" | "COMPLETED" | "NO_SHOW", "adminNotes": "..." }`
  - Response (200): Updated booking object.

### Admin: Customers

- **`GET /admin/customers`**: Get All Customers
  - Query Params: `searchEmail`, `searchName`, `page`, `limit`
  - Response (200): Paginated list of customer objects (`{ userId, email, name, phoneNumber, registrationDate, isActive }`).
- **`GET /admin/customers/{userId}`**: Get Customer by ID
  - Response (200): Customer object + their booking history.
- **`PUT /admin/customers/{userId}`**: Update Customer (e.g., name, phone)
  - Request: `{ name?, phoneNumber? }`
  - Response (200): Updated customer object.
- **`PATCH /admin/customers/{userId}/status`**: Activate/Deactivate Customer Account
  - Request: `{ "isActive": true | false }`
  - Response (200): `{ message: "Customer account status updated" }`

### Admin: Analytics

- **`GET /admin/analytics/overview`**: Get key analytics metrics.
  - Query Params: `dateFrom?`, `dateTo?`, `locationId?`
  - Response (200): `{ totalBookings, totalRevenue, averageOccupancy, newCustomers }`
- **`GET /admin/analytics/revenue-report`**: Revenue report by room/location.
  - Query Params: `dateFrom?`, `dateTo?`, `groupBy: 'room' | 'location'`
  - Response (200): `[{ dimension, revenue, bookingCount }]`
- **`GET /admin/analytics/occupancy-report`**: Occupancy report.
  - Query Params: `dateFrom?`, `dateTo?`, `locationId?`, `roomId?`
  - Response (200): `[{ date/timePeriod, occupancyRate, bookedHours, totalAvailableHours }]`

### Admin: System Settings

- **`GET /admin/settings`**: Get current system settings.
  - Response (200): `{ payOsApiKeyPublic, bookingCancellationHours, emailTemplates: { confirmation: "...", reminder: "..." } }` (Sensitive keys like PayOS secret should not be exposed directly here if possible, or handled carefully)
- **`PUT /admin/settings`**: Update system settings.
  - Request: (Partial settings object)
  - Response (200): Updated settings object.

### Admin: Users (for managing other admins - Super Admin only)

- **`GET /admin/users`**: List admin users (Super Admin only).
- **`POST /admin/users`**: Create new admin user (Super Admin only).
- **`PUT /admin/users/{adminUserId}`**: Update admin user (Super Admin only).
- **`DELETE /admin/users/{adminUserId}`**: Delete admin user (Super Admin only).
