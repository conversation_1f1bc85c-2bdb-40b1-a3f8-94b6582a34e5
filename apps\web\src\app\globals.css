@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  /* Design Language Colors */
  --color-gold-50: #f9f6f0;
  --color-gold-100: #f3ede1;
  --color-gold-200: #e7dbc3;
  --color-gold-300: #dbc9a5;
  --color-gold-400: #bda476;
  --color-gold-500: #ab8d59;
  --color-gold-600: #987947;
  --color-gold-700: #806439;
  --color-gold-800: #674e2d;
  --color-gold-900: #4e3921;

  /* Queens Brand Gold */
  --color-queens-gold: #ab8d59;

  /* Room Type Colors */
  --color-ruby-primary: #8B2635;
  --color-ruby-secondary: #C4394B;
  --color-ruby-600: #731e29;
  --color-ruby-accent: #987947;
  --color-ruby-bg: #FDF2F4;

  --color-sapphire-primary: #1E3A8A;
  --color-sapphire-secondary: #3B82F6;
  --color-sapphire-600: #1d4ed8;
  --color-sapphire-accent: #987947;
  --color-sapphire-bg: #F0F4FF;

  --color-queens-primary: #065F46;
  --color-queens-secondary: #059669;
  --color-queens-600: #047857;
  --color-queens-accent: #674e2d;
  --color-queens-bg: #ECFDF5;

  --color-opal-primary: #7C3AED;
  --color-opal-secondary: #A855F7;
  --color-opal-600: #9333ea;
  --color-opal-accent: #987947;
  --color-opal-bg: #FAF5FF;

  --color-pearl-primary: #374151;
  --color-pearl-secondary: #6B7280;
  --color-pearl-accent: #987947;
  --color-pearl-bg: #FEFEFE;

  --color-hall-primary: #1F2937;
  --color-hall-secondary: #374151;
  --color-hall-accent: #ab8d59;
  --color-hall-bg: #F9FAFB;

  --color-standard-primary: #987947;
  --color-standard-secondary: #ab8d59;
  --color-standard-accent: #806439;
  --color-standard-bg: #f9f6f0;

  /* Shadcn UI Colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--color-gold-600);
  --color-primary-foreground: #ffffff;
  --color-secondary: var(--color-gold-100);
  --color-secondary-foreground: var(--color-gold-800);
  --color-muted: var(--color-gold-50);
  --color-muted-foreground: var(--color-gold-600);
  --color-accent: var(--color-gold-100);
  --color-accent-foreground: var(--color-gold-800);
  --color-destructive: #DC2626;
  --color-destructive-foreground: #ffffff;
  --color-border: var(--color-gold-200);
  --color-input: var(--color-gold-200);
  --color-ring: var(--color-gold-600);

  /* Typography */
  --font-sans: var(--font-roboto-condensed), system-ui, sans-serif;
  --font-roboto: var(--font-roboto-condensed), system-ui, sans-serif;

  /* Animation Durations */
  --duration-micro: 150ms;
  --duration-small: 200ms;
  --duration-medium: 300ms;
  --duration-large: 400ms;
  --duration-xl: 600ms;

  /* Border Radius */
  --radius: 0.5rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-roboto-condensed), system-ui, sans-serif;
}

/* Typography Scale */
.text-display {
  font-size: 3.75rem;
  line-height: 1.1;
  font-weight: 700;
}

.text-h1 {
  font-size: 3rem;
  line-height: 1.2;
  font-weight: 600;
}

.text-h2 {
  font-size: 2.25rem;
  line-height: 1.3;
  font-weight: 600;
}

.text-h3 {
  font-size: 1.875rem;
  line-height: 1.4;
  font-weight: 600;
}

.text-h4 {
  font-size: 1.5rem;
  line-height: 1.4;
  font-weight: 500;
}

.text-h5 {
  font-size: 1.25rem;
  line-height: 1.5;
  font-weight: 500;
}

.text-h6 {
  font-size: 1.125rem;
  line-height: 1.5;
  font-weight: 500;
}

.text-price {
  font-size: 1.5rem;
  line-height: 1.3;
  font-weight: 700;
}

.text-luxury {
  font-size: 2.5rem;
  line-height: 1.2;
  font-weight: 700;
  letter-spacing: -0.02em;
}

/* Room Type Treatments */
.queens-treatment {
  background: linear-gradient(135deg, var(--color-queens-bg) 0%, #ffffff 50%, var(--color-gold-50) 100%);
  border: 2px solid color-mix(in srgb, var(--color-queens-secondary) 30%, transparent);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.ruby-treatment {
  background: linear-gradient(135deg, var(--color-ruby-bg) 0%, #ffffff 100%);
  border: 1px solid color-mix(in srgb, var(--color-ruby-secondary) 20%, transparent);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.sapphire-treatment {
  background: linear-gradient(135deg, var(--color-sapphire-bg) 0%, #ffffff 100%);
  border: 1px solid color-mix(in srgb, var(--color-sapphire-secondary) 20%, transparent);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.opal-treatment {
  background: linear-gradient(135deg, var(--color-opal-bg) 0%, #ffffff 100%);
  border: 2px solid transparent;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  position: relative;
}

.opal-treatment::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 0.5rem;
  padding: 2px;
  background: linear-gradient(135deg, #7C3AED 0%, #EC4899 50%, #F59E0B 100%);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  z-index: -1;
}

.pearl-treatment {
  background: linear-gradient(135deg, #ffffff 0%, var(--color-pearl-bg) 100%);
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.pearl-treatment:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.hall-treatment {
  background: linear-gradient(135deg, var(--color-hall-bg) 0%, #f9fafb 100%);
  border: 1px solid #e5e7eb;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.standard-treatment {
  background: linear-gradient(135deg, var(--color-standard-bg) 0%, #ffffff 100%);
  border: 1px solid var(--color-gold-200);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Queens Brand Gold Utility Classes */
.bg-queens-gold {
  background-color: var(--color-queens-gold);
}

.bg-queens-gold:hover {
  background-color: color-mix(in srgb, var(--color-queens-gold) 90%, transparent);
}

.text-queens-gold {
  color: var(--color-queens-gold);
}

.border-queens-gold {
  border-color: var(--color-queens-gold);
}

/* Button Contrast Guidelines - Queens Gold Background */
.bg-queens-gold {
  color: #ffffff; /* WCAG AA compliant - 4.52:1 contrast ratio */
}

.bg-queens-gold .icon {
  color: #ffffff; /* Ensure icons also use white */
}

/* Custom Animation Classes */
.fade-in {
  opacity: 0;
  animation: fadeIn 0.6s ease-out forwards;
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: scale(0.95); 
  }
  to { 
    opacity: 1; 
    transform: scale(1); 
  }
}

/* Modal Animation Classes */
.animate-in {
  animation-fill-mode: both;
}

.fade-in-0 {
  animation-name: fade-in;
}

.zoom-in-95 {
  animation-name: zoom-in;
}

.duration-300 {
  animation-duration: 300ms;
}

.ease-luxury {
  animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes zoom-in {
  from { 
    opacity: 0; 
    transform: scale(0.95); 
  }
  to { 
    opacity: 1; 
    transform: scale(1); 
  }
}

.luxury-hover {
  transition: all 300ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.luxury-hover:hover {
  transform: scale(1.02) translateY(-4px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.button-hover {
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

.button-hover:hover {
  transform: scale(1.02);
}

.button-hover:active {
  transform: scale(0.98);
}

/* Custom animations for availability components */
@keyframes fadeIn {
  from { 
    opacity: 0; 
  }
  to { 
    opacity: 1; 
  }
}

@keyframes slideInUp {
  from { 
    opacity: 0; 
    transform: translateY(10px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes slideInDown {
  from { 
    opacity: 0; 
    transform: translateY(-10px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0; 
    transform: scale(0.95); 
  }
  to { 
    opacity: 1; 
    transform: scale(1); 
  }
}

/* Custom animation utilities */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out forwards;
}

.animate-slide-in-up {
  animation: slideInUp 0.3s ease-out forwards;
}

.animate-slide-in-down {
  animation: slideInDown 0.3s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out forwards;
}

/* Loading state utilities */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Smooth transitions for availability status changes */
.availability-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure CLS prevention with min-height containers */
.availability-container {
  min-height: 80px;
  transition: all 0.3s ease-in-out;
}

.time-slot-grid {
  transition: opacity 0.4s ease-in-out;
}

/* Staggered animation delays for time slots */
.time-slot:nth-child(1) { animation-delay: 0.02s; }
.time-slot:nth-child(2) { animation-delay: 0.04s; }
.time-slot:nth-child(3) { animation-delay: 0.06s; }
.time-slot:nth-child(4) { animation-delay: 0.08s; }
.time-slot:nth-child(5) { animation-delay: 0.10s; }
.time-slot:nth-child(6) { animation-delay: 0.12s; }
.time-slot:nth-child(7) { animation-delay: 0.14s; }
.time-slot:nth-child(8) { animation-delay: 0.16s; }
.time-slot:nth-child(9) { animation-delay: 0.18s; }
.time-slot:nth-child(10) { animation-delay: 0.20s; }

/* Enhanced hover effects */
.time-slot:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Loading overlay */
.loading-overlay {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  transition: opacity 0.3s ease-in-out;
}

/* Suggestion cards animation */
.suggestion-card {
  opacity: 0;
  animation: slideInUp 0.3s ease-out forwards;
}

.suggestion-card:nth-child(1) { animation-delay: 0.1s; }
.suggestion-card:nth-child(2) { animation-delay: 0.2s; }
.suggestion-card:nth-child(3) { animation-delay: 0.3s; }
.suggestion-card:nth-child(4) { animation-delay: 0.4s; }
.suggestion-card:nth-child(5) { animation-delay: 0.5s; }
