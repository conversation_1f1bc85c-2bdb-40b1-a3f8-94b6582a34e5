import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { AnalyticsService } from './analytics.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from 'shared-types';

@Controller('analytics')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('dashboard')
  async getDashboardStats() {
    return this.analyticsService.getDashboardStats();
  }

  @Get('room-types')
  async getRoomTypeDistribution() {
    return this.analyticsService.getRoomTypeDistribution();
  }

  @Get('booking-trends')
  async getBookingTrends(@Query('days') days?: string) {
    const daysNumber = days ? parseInt(days, 10) : 30;
    return this.analyticsService.getBookingTrends(daysNumber);
  }

  @Get('popular-rooms')
  async getPopularRooms(@Query('limit') limit?: string) {
    const limitNumber = limit ? parseInt(limit, 10) : 10;
    return this.analyticsService.getPopularRooms(limitNumber);
  }

  @Get('peak-hours')
  async getPeakHours() {
    return this.analyticsService.getPeakHours();
  }
}
