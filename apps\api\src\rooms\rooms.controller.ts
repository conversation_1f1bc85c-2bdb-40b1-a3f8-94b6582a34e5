import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseU<PERSON><PERSON>ipe,
  HttpCode,
  HttpStatus,
  UploadedFiles,
  UseInterceptors,
  BadRequestException,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { Express } from 'express';
import { RoomsService } from './rooms.service';
import {
  CreateRoomDto,
  UpdateRoomDto,
  RoomQueryDto,
} from '@shared-types/dtos.types';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Public } from '../auth/decorators/public.decorator';
import { UserRole } from '@shared-types/common.types';
import { Room } from '../../generated/prisma'; // Assuming Room type might be needed for responses

const MAX_IMAGE_COUNT = 5;
const MAX_IMAGE_SIZE_MB = 5;

@Controller('locations/:locationId/rooms')
@UseGuards(JwtAuthGuard, RolesGuard)
export class RoomsController {
  constructor(private readonly roomsService: RoomsService) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @HttpCode(HttpStatus.CREATED)
  create(
    @Param('locationId', ParseUUIDPipe) locationId: string,
    @Body() createRoomDto: CreateRoomDto,
  ): Promise<Room> {
    // Image URLs in CreateRoomDto are assumed to be pre-existing URLs if any,
    // actual file uploads will happen via the dedicated /images endpoint.
    // The createRoomDto.images field can be used for initial seeding if needed.
    return this.roomsService.create(locationId, createRoomDto);
  }

  @Post(':roomId/images')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @UseInterceptors(
    FilesInterceptor('images', MAX_IMAGE_COUNT, {
      // 'images' is the field name in form-data
      limits: { fileSize: MAX_IMAGE_SIZE_MB * 1024 * 1024 },
      fileFilter: (req, file, cb) => {
        if (!file.mimetype.match(/\/(jpg|jpeg|png|gif|webp)$/)) {
          return cb(
            new BadRequestException(
              'Invalid file type. Only JPG, JPEG, PNG, GIF, WEBP are allowed.',
            ),
            false,
          );
        }
        cb(null, true);
      },
    }),
  )
  async uploadRoomImages(
    @Param('locationId', ParseUUIDPipe) locationId: string,
    @Param('roomId', ParseUUIDPipe) roomId: string,
    @UploadedFiles() files: Array<Express.Multer.File>,
  ): Promise<Room> {
    if (!files || files.length === 0) {
      throw new BadRequestException(
        'No image files uploaded. Please upload at least one image.',
      );
    }
    return this.roomsService.uploadRoomImages(locationId, roomId, files);
  }

  @Public()
  @Get()
  findAll(
    @Param('locationId', ParseUUIDPipe) locationId: string,
    @Query() query: RoomQueryDto,
  ): Promise<Room[]> {
    return this.roomsService.findAll(locationId, query);
  }

  @Public()
  @Get(':id')
  findOne(
    @Param('locationId', ParseUUIDPipe) locationId: string,
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<Room | null> {
    return this.roomsService.findOne(locationId, id);
  }

  @Patch(':id')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  update(
    @Param('locationId', ParseUUIDPipe) locationId: string,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateRoomDto: UpdateRoomDto,
  ): Promise<Room> {
    return this.roomsService.update(locationId, id, updateRoomDto);
  }

  @Delete(':id')
  @Roles(UserRole.SUPER_ADMIN)
  @HttpCode(HttpStatus.OK) // Or HttpStatus.NO_CONTENT if nothing is returned
  remove(
    @Param('locationId', ParseUUIDPipe) locationId: string,
    @Param('id', ParseUUIDPipe) id: string,
    // Service returns the updated (soft-deleted) room
  ): Promise<Room> {
    return this.roomsService.remove(locationId, id);
  }
}
