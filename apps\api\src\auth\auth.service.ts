import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import * as bcrypt from 'bcrypt';
import { LoginDto, RegisterDto, AuthResponseDto } from 'shared-types';
import { UserRole } from 'shared-types';
import { EmailService } from '../email/email.service';
import { v4 as uuidv4 } from 'uuid';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private emailService: EmailService,
    private configService: ConfigService,
  ) {}

  async register(registerDto: RegisterDto): Promise<AuthResponseDto> {
    const { email, password, name, phoneNumber } = registerDto;

    const existingUser = await this.prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      throw new ConflictException('Email đã được sử dụng');
    }

    if (password.length < 8) {
      throw new BadRequestException('Mật khẩu phải có ít nhất 8 ký tự');
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const emailVerificationToken = uuidv4();
    const emailVerificationTokenExpiresAt = new Date(
      Date.now() + 24 * 60 * 60 * 1000, // 24 hours from now
    );

    const user = await this.prisma.user.create({
      data: {
        email,
        passwordHash: hashedPassword,
        name,
        phoneNumber,
        role: UserRole.CUSTOMER,
        isActive: true,
        emailVerified: false,
        emailVerificationToken,
        emailVerificationTokenExpiresAt,
      },
    });

    try {
      const frontendUrl = this.configService.get<string>('FRONTEND_URL');
      if (!frontendUrl) {
        this.logger.error(
          'FRONTEND_URL is not set. Cannot send verification email.',
        );
      } else {
        const verificationLink = `${frontendUrl}/auth/verify-email?token=${emailVerificationToken}`;
        const emailHtml = await this.emailService.renderTemplate(
          'email-verification',
          'vi',
          {
            userName: name || email,
            verificationLink,
          },
        );
        await this.emailService.sendMail({
          to: email,
          subject: 'Xác thực địa chỉ email của bạn',
          html: emailHtml,
        });
        this.logger.log(`Verification email sent to ${email}`);
      }
    } catch (error) {
      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      this.logger.error(
        `Failed to send verification email to ${email}: ${errorMessage}`,
        error instanceof Error ? error.stack : undefined,
      );
    }

    const { accessToken, refreshToken } = this.generateTokens(
      user.id,
      user.email,
      user.role as UserRole,
    );

    return {
      user: {
        id: user.id,
        email: user.email,
        name: user.name || undefined,
        role: user.role as UserRole,
        emailVerified: user.emailVerified, // Now part of AuthResponseDto.user
      },
      accessToken,
      refreshToken,
    };
  }

  async login(loginDto: LoginDto): Promise<AuthResponseDto> {
    const { email, password } = loginDto;

    const user = await this.prisma.user.findUnique({
      where: { email },
    });

    if (!user || !user.isActive) {
      throw new UnauthorizedException('Thông tin đăng nhập không hợp lệ');
    }

    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Thông tin đăng nhập không hợp lệ');
    }

    const { accessToken, refreshToken } = this.generateTokens(
      user.id,
      user.email,
      user.role as UserRole,
    );

    return {
      user: {
        id: user.id,
        email: user.email,
        name: user.name || undefined,
        role: user.role as UserRole,
        emailVerified: user.emailVerified,
      },
      accessToken,
      refreshToken,
    };
  }

  async adminLogin(
    username: string,
    password: string,
  ): Promise<AuthResponseDto> {
    const user = await this.prisma.user.findFirst({
      where: {
        OR: [{ email: username }, { name: username }],
        role: {
          in: [UserRole.ADMIN, UserRole.SUPER_ADMIN],
        },
        isActive: true,
      },
    });

    if (!user) {
      throw new UnauthorizedException(
        'Thông tin đăng nhập quản trị không hợp lệ',
      );
    }

    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      throw new UnauthorizedException(
        'Thông tin đăng nhập quản trị không hợp lệ',
      );
    }

    const { accessToken, refreshToken } = this.generateTokens(
      user.id,
      user.email,
      user.role as UserRole,
    );

    return {
      user: {
        id: user.id,
        email: user.email,
        name: user.name || undefined,
        role: user.role as UserRole,
        emailVerified: user.emailVerified,
      },
      accessToken,
      refreshToken,
    };
  }

  async validateUser(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId, isActive: true },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        emailVerified: true,
        phoneNumber: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return user ? { ...user, role: user.role as UserRole } : null;
  }

  private generateTokens(userId: string, email: string, role: UserRole) {
    const payload = { sub: userId, email, role };
    const accessToken = this.jwtService.sign(payload, { expiresIn: '1h' });
    const refreshToken = this.jwtService.sign(payload, { expiresIn: '30d' });
    return { accessToken, refreshToken };
  }

  async hashPassword(password: string): Promise<string> {
    return await bcrypt.hash(password, 10);
  }

  async comparePasswords(
    password: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return await bcrypt.compare(password, hashedPassword);
  }

  async verifyEmail(token: string): Promise<{ message: string }> {
    this.logger.log(`Attempting to verify email with token: ${token}`);
    const user = await this.prisma.user.findUnique({
      where: { emailVerificationToken: token },
    });

    if (!user) {
      this.logger.warn(`Verification token not found: ${token}`);
      throw new BadRequestException(
        'Liên kết xác thực không hợp lệ hoặc đã hết hạn.',
      );
    }

    if (user.emailVerified) {
      this.logger.log(`Email already verified for user: ${user.email}`);
      return { message: 'Email đã được xác thực trước đó.' };
    }

    if (
      user.emailVerificationTokenExpiresAt &&
      user.emailVerificationTokenExpiresAt < new Date()
    ) {
      this.logger.warn(`Verification token expired for user: ${user.email}`);
      // Optionally, offer to resend or delete the expired token
      // await this.prisma.user.update({
      //   where: { id: user.id },
      //   data: { emailVerificationToken: null, emailVerificationTokenExpiresAt: null },
      // });
      throw new BadRequestException(
        'Liên kết xác thực đã hết hạn. Vui lòng yêu cầu một liên kết mới.',
      );
    }

    await this.prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: true,
        isActive: true, // Ensure user is active
        emailVerificationToken: null, // Clear the token
        emailVerificationTokenExpiresAt: null, // Clear expiry
      },
    });

    this.logger.log(`Email successfully verified for user: ${user.email}`);
    // Optionally send a welcome email or confirmation of verification
    return {
      message: 'Xác thực email thành công! Bạn có thể đăng nhập ngay bây giờ.',
    };
  }

  async resendVerificationEmail(userId: string): Promise<{ message: string }> {
    const user = await this.prisma.user.findUnique({ where: { id: userId } });

    if (!user) {
      this.logger.warn(
        `User not found for resending verification email: ${userId}`,
      );
      throw new BadRequestException('Không tìm thấy người dùng.'); // Should not happen if called by authenticated user
    }

    if (user.emailVerified) {
      this.logger.log(
        `Email already verified, no need to resend for user: ${user.email}`,
      );
      return { message: 'Email của bạn đã được xác thực trước đó.' };
    }

    // Generate new token and expiry
    const newEmailVerificationToken = uuidv4();
    const newEmailVerificationTokenExpiresAt = new Date(
      Date.now() + 24 * 60 * 60 * 1000, // 24 hours
    );

    await this.prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerificationToken: newEmailVerificationToken,
        emailVerificationTokenExpiresAt: newEmailVerificationTokenExpiresAt,
      },
    });

    try {
      const frontendUrl = this.configService.get<string>('FRONTEND_URL');
      if (!frontendUrl) {
        this.logger.error(
          'FRONTEND_URL is not set. Cannot send new verification email.',
        );
        throw new BadRequestException(
          'Không thể gửi email xác thực lúc này. Vui lòng thử lại sau.',
        );
      }
      const verificationLink = `${frontendUrl}/auth/verify-email?token=${newEmailVerificationToken}`;
      const emailHtml = await this.emailService.renderTemplate(
        'email-verification',
        'vi',
        {
          userName: user.name || user.email,
          verificationLink,
        },
      );
      await this.emailService.sendMail({
        to: user.email,
        subject: 'Xác thực địa chỉ email của bạn (Yêu cầu mới)',
        html: emailHtml,
      });
      this.logger.log(`New verification email sent to ${user.email}`);
      return {
        message: 'Một email xác thực mới đã được gửi đến địa chỉ của bạn.',
      };
    } catch (error) {
      let errorMessage = 'Unknown error during resend';
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      this.logger.error(
        `Failed to resend verification email to ${user.email}: ${errorMessage}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new BadRequestException(
        'Không thể gửi email xác thực lúc này. Vui lòng thử lại sau.',
      );
    }
  }
}
