import {
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  IsBoolean,
  Matches,
} from 'class-validator';
import { UserRole } from 'shared-types';

export class AdminCreateUserDto {
  @IsEmail({}, { message: '<PERSON>ail phải có định dạng hợp lệ' })
  email: string;

  @IsOptional()
  @IsString({ message: 'Tên phải là chuỗi ký tự' })
  name?: string;

  @IsOptional()
  @IsString({ message: 'Số điện thoại phải là chuỗi ký tự' })
  @Matches(/^[0-9+\-\s()]+$/, { message: 'S<PERSON> điện thoại không hợp lệ' })
  phoneNumber?: string;

  @IsEnum(UserRole, { message: '<PERSON>ai trò không hợp lệ' })
  role: UserRole;

  @IsOptional()
  @IsBoolean({ message: 'Trạng thái hoạt động phải là boolean' })
  isActive?: boolean;

  @IsOptional()
  @IsBoolean({ message: 'Gửi email chào mừng phải là boolean' })
  sendWelcomeEmail?: boolean;
}
