# Queen Karaoke Booking System - Project Overview

## Brief Description

The Queen Karaoke Booking System is a web application designed to allow users to browse, book, and manage karaoke room reservations across multiple physical locations. It will provide a seamless experience for public users to make bookings, registered customers to manage their accounts and bookings, and administrators to manage the system's content, bookings, and view analytics. The system will support bilingual functionality, with Vietnamese as the primary language and English as an alternative.

## Tech Stack Overview

The project will leverage a modern, robust, and scalable technology stack:

- **Frontend:**
  - Next.js (TypeScript)
  - ReactJS
  - Tailwind CSS
  - State Management (e.g., Zustand or React Context)
  - Internationalization (i18n) library (e.g., `next-international`, `react-i18next`)
- **Backend:**
  - NestJS (TypeScript)
  - Prisma ORM
  - PostgreSQL (Database)
  - Redis (Session Store, Caching, Background Jobs)
  - Passport.js (Authentication)
- **Payment Gateway:**
  - PayOS.vn SDK
- **DevOps & Infrastructure:**
  - Docker & Docker Compose
  - Nginx (Reverse Proxy)
  - CI/CD (e.g., GitHub Actions)
  - Self-hosted VPS

## Deployment Goals

- **Self-Hosted:** The entire system will be deployed on a Virtual Private Server (VPS) controlled by the business, ensuring data ownership and operational control.
- **Scalable:** The architecture will be designed to handle future growth, including the addition of new locations, rooms, and an increasing number of users and bookings.
- **Secure:** Security will be a core consideration, implementing measures to protect user data, secure financial transactions, and prevent unauthorized access. This includes HTTPS, secure authentication, input validation, and adherence to best practices for data protection.
