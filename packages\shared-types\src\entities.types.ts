import {
  UserRole,
  BookingStatus,
  PaymentStatus,
  LocalizedString,
  OperatingHours,
  RoomTypeEnum,
  DecorStyle,
} from './common.types';

// User Entity
export interface User {
  id: string;
  email: string;
  name?: string;
  phoneNumber?: string;
  role: UserRole;
  isActive: boolean;
  emailVerified: boolean;
  lastLogin?: string; // ISO string
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
}

// Location Entity
export interface Location {
  id: string;
  name: LocalizedString;
  address: string;
  description?: LocalizedString;
  imageUrl?: string;
  phoneNumber?: string;
  email?: string;
  operatingHours?: OperatingHours;
  isActive: boolean;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
}

// Room Entity
export interface Room {
  id: string;
  name: LocalizedString;
  theme?: LocalizedString;
  description?: LocalizedString;
  capacity: number;
  pricePerHour: number; // Decimal as number
  images: string[]; // Array of image URLs
  amenities: LocalizedString[]; // Array of localized amenity descriptions
  roomType?: RoomTypeEnum;
  decorStyle: DecorStyle;
  isActive: boolean;
  locationId: string;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string

  // Relations (when populated)
  location?: Location;
}

// Availability Override Entity
export interface AvailabilityOverride {
  id: string;
  roomId: string;
  date: string; // ISO date string
  startTime: string; // ISO string
  endTime: string; // ISO string
  isAvailable: boolean;
  reason?: string;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string

  // Relations
  room?: Room;
}

// Booking Entity
export interface Booking {
  id: string;
  bookingReference: string; // User-friendly ID like QK-XXXXXX
  userId?: string;
  locationId: string;
  roomId: string;
  bookingDate: string; // ISO date string
  startTime: string; // ISO string
  endTime: string; // ISO string
  durationMinutes: number;
  numberOfGuests: number;
  totalPrice: number; // Decimal as number
  currency: string;
  status: BookingStatus;

  // Guest details (for non-registered users)
  guestName?: string;
  guestEmail?: string;
  guestPhone?: string;

  notes?: string;
  adminNotes?: string;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string

  // Relations (when populated)
  user?: User;
  location?: Location;
  room?: Room;
  payment?: Payment;
}

// Payment Entity
export interface Payment {
  id: string;
  bookingId: string;
  amount: number; // Decimal as number
  currency: string;
  paymentGateway: string;
  transactionId: string;
  paymentIntentId?: string;
  status: PaymentStatus;
  paymentMethod?: string; // e.g., "Credit Card", "Bank Transfer" (from PayOS)
  paidAt?: string; // ISO string
  refundedAt?: string; // ISO string
  metadata?: Record<string, any>; // JSONB as object
  createdAt: string; // ISO string
  updatedAt: string; // ISO string

  // Relations
  booking?: Booking;
}

// Audit Log Entity
export interface AuditLog {
  id: string;
  userId?: string;
  action: string;
  entityType: string;
  entityId: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string; // ISO string

  // Relations
  user?: User;
}
