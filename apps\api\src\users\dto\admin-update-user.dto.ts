import { IsOptional, IsString, IsBoolean, Matches } from 'class-validator';

export class AdminUpdateUserDto {
  @IsOptional()
  @IsString({ message: 'Tên phải là chuỗi ký tự' })
  name?: string;

  @IsOptional()
  @IsString({ message: 'Số điện thoại phải là chuỗi ký tự' })
  @Matches(/^[0-9+\-\s()]+$/, { message: 'Số điện thoại không hợp lệ' })
  phoneNumber?: string;

  @IsOptional()
  @IsBoolean({ message: '<PERSON><PERSON><PERSON> thực email phải là boolean' })
  emailVerified?: boolean;

  @IsOptional()
  @IsBoolean({ message: 'Trạng thái hoạt động phải là boolean' })
  isActive?: boolean;
}
