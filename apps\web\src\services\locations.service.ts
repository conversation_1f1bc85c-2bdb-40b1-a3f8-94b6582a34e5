import {
  CreateLocationDto,
  UpdateLocationDto,
  LocationQueryDto,
  ApiResponse,
  PaginatedResponse,
  Location,
  Room,
  OperatingHours,
} from 'shared-types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

class LocationsService {
  private getAuthToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('auth-token');
  }

  private getAuthHeaders(): HeadersInit {
    const token = this.getAuthToken();
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  private getAuthHeadersForFormData(): HeadersInit {
    const token = this.getAuthToken();
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `HTTP ${response.status}: ${response.statusText}`
      );
    }
    return response.json();
  }

  /**
   * Get all locations with optional filtering, pagination, and sorting
   */
  async getLocations(
    query?: LocationQueryDto
  ): Promise<PaginatedResponse<Location>> {
    const searchParams = new URLSearchParams();

    if (query) {
      Object.entries(query).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, value.toString());
        }
      });
    }

    const url = `${API_BASE_URL}/locations${searchParams.toString() ? `?${searchParams}` : ''}`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      return this.handleResponse<PaginatedResponse<Location>>(response);
    } catch (error) {
      console.error('Error fetching locations:', error);
      throw error;
    }
  }

  /**
   * Get a specific location by ID
   */
  async getLocation(id: string): Promise<Location> {
    try {
      const response = await fetch(`${API_BASE_URL}/locations/${id}`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      return this.handleResponse<Location>(response);
    } catch (error) {
      console.error(`Error fetching location ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new location (Admin/Super Admin only)
   */
  async createLocation(locationData: CreateLocationDto): Promise<Location> {
    try {
      const response = await fetch(`${API_BASE_URL}/locations`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(locationData),
      });

      return this.handleResponse<Location>(response);
    } catch (error) {
      console.error('Error creating location:', error);
      throw error;
    }
  }

  /**
   * Update an existing location (Admin/Super Admin only)
   */
  async updateLocation(
    id: string,
    locationData: UpdateLocationDto
  ): Promise<Location> {
    try {
      const response = await fetch(`${API_BASE_URL}/locations/${id}`, {
        method: 'PATCH',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(locationData),
      });

      return this.handleResponse<Location>(response);
    } catch (error) {
      console.error(`Error updating location ${id}:`, error);
      throw error;
    }
  }

  /**
   * Soft delete a location (Super Admin only)
   */
  async deleteLocation(id: string): Promise<Location> {
    try {
      const response = await fetch(`${API_BASE_URL}/locations/${id}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
      });

      return this.handleResponse<Location>(response);
    } catch (error) {
      console.error(`Error deleting location ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get rooms for a specific location
   */
  async getLocationRooms(locationId: string): Promise<Room[]> {
    try {
      const response = await fetch(
        `${API_BASE_URL}/locations/${locationId}/rooms`,
        {
          method: 'GET',
          headers: this.getAuthHeaders(),
        }
      );

      return this.handleResponse<Room[]>(response);
    } catch (error) {
      console.error(`Error fetching rooms for location ${locationId}:`, error);
      throw error;
    }
  }

  /**
   * Get operating hours for a specific location
   */
  async getLocationOperatingHours(locationId: string): Promise<OperatingHours> {
    try {
      const response = await fetch(
        `${API_BASE_URL}/locations/${locationId}/operating-hours`,
        {
          method: 'GET',
          headers: this.getAuthHeaders(),
        }
      );

      return this.handleResponse<OperatingHours>(response);
    } catch (error) {
      console.error(
        `Error fetching operating hours for location ${locationId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Upload a location image (Admin/Super Admin only)
   */
  async uploadLocationImage(
    locationId: string,
    image: File
  ): Promise<Location> {
    try {
      const formData = new FormData();
      formData.append('image', image);

      const response = await fetch(
        `${API_BASE_URL}/locations/${locationId}/image`,
        {
          method: 'POST',
          headers: this.getAuthHeadersForFormData(),
          body: formData,
        }
      );

      return this.handleResponse<Location>(response);
    } catch (error) {
      console.error(`Error uploading image for location ${locationId}:`, error);
      throw error;
    }
  }
}

// Export singleton instance
export const locationsService = new LocationsService();
export default locationsService;
