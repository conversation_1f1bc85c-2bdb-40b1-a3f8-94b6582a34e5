import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { BookingsService } from './bookings.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Public } from '../auth/decorators/public.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { UserRole } from '@shared-types/common.types';
import { User, Booking } from '../../generated/prisma';
import {
  BookingQueryDto,
  // UpdateBookingDto, // Comment out or remove if truly unused for now
  CancelBookingDto,
  AdminUpdateBookingStatusDto,
  AdminUpdateBookingDto,
  CreateBookingDto,
} from './dto';
import { PaginatedResponse, ApiResponse } from '@shared-types/common.types';
// DTOs will be imported later when defined
// import { CreateBookingDto, UpdateBookingDto, BookingQueryDto } from './dto';

@Controller('bookings')
@UseGuards(JwtAuthGuard, RolesGuard)
export class BookingsController {
  constructor(private readonly bookingsService: BookingsService) {}

  // Endpoint for users to create a booking
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @Roles(UserRole.CUSTOMER, UserRole.ADMIN, UserRole.SUPER_ADMIN) // Or just CUSTOMER if Admins use a separate flow
  async createBooking(
    @Body() createBookingDto: CreateBookingDto,
    @CurrentUser() user: User,
  ): Promise<ApiResponse<Booking>> {
    console.log('User creating booking:', user);
    try {
      const booking = await this.bookingsService.createBooking({
        ...createBookingDto,
        userId: user.id,
      });
      return {
        success: true,
        data: booking,
        message: 'Booking created successfully',
      };
    } catch (error) {
      console.error('Error in createBooking controller:', error);
      throw error; // Let NestJS handle the error response
    }
  }

  // Endpoint for guests to create a booking (public, but might need special handling)
  @Public()
  @Post('guest')
  @HttpCode(HttpStatus.CREATED)
  async createGuestBooking(
    @Body() createBookingDto: CreateBookingDto,
  ): Promise<ApiResponse<Booking>> {
    // Service will need to handle bookings without a userId
    try {
      const booking =
        await this.bookingsService.createBooking(createBookingDto);
      return {
        success: true,
        data: booking,
        message: 'Guest booking created successfully',
      };
    } catch (error) {
      console.error('Error in createGuestBooking controller:', error);
      throw error; // Let NestJS handle the error response
    }
  }

  // Endpoint for users to get their own bookings
  @Get('my-bookings')
  @Roles(UserRole.CUSTOMER, UserRole.ADMIN, UserRole.SUPER_ADMIN)
  findUserBookings(
    @CurrentUser() user: User,
    @Query() query: BookingQueryDto,
  ): Promise<PaginatedResponse<Booking>> {
    return this.bookingsService.findUserBookings(user.id, query);
  }

  // Endpoint for users to get a specific booking of theirs
  // Also for admins to get any booking by ID
  @Get(':id')
  @Roles(UserRole.CUSTOMER, UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async findOneBooking(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: User,
  ): Promise<Booking | null> {
    return this.bookingsService.findOneBooking(id, user.id);
  }

  // Endpoint for users to update their booking (primarily for cancellation)
  @Patch(':id')
  @Roles(UserRole.CUSTOMER) // Only customers can cancel their own this way
  async updateBookingStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() cancelBookingDto: CancelBookingDto, // Use CancelBookingDto for user cancellation
    @CurrentUser() user: User,
  ): Promise<Booking> {
    // User can only set status to CANCELLED_BY_USER via this DTO.
    // The service method handles the permission to only update own booking.
    return this.bookingsService.updateBookingStatus(
      id,
      cancelBookingDto.status, // This will be BookingStatus.CANCELLED_BY_USER
      user.id,
      cancelBookingDto.cancellationReason, // Pass cancellation reason as notes
    );
  }

  // --- Admin Only Endpoints ---

  // Admin endpoint for creating guest bookings
  @Post('admin/guest')
  @HttpCode(HttpStatus.CREATED)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async createGuestBookingByAdmin(
    @Body() createBookingDto: CreateBookingDto,
  ): Promise<ApiResponse<Booking>> {
    // Admin creates a guest booking without userId
    try {
      const booking =
        await this.bookingsService.createBooking(createBookingDto);
      return {
        success: true,
        data: booking,
        message: 'Guest booking created successfully by admin',
      };
    } catch (error) {
      console.error('Error in createGuestBookingByAdmin controller:', error);
      throw error; // Let NestJS handle the error response
    }
  }

  // Admin endpoint for creating bookings for existing users
  @Post('admin/user')
  @HttpCode(HttpStatus.CREATED)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async createUserBookingByAdmin(
    @Body() createBookingDto: CreateBookingDto & { userId: string },
  ): Promise<ApiResponse<Booking>> {
    try {
      // The service method createBooking is already designed to handle userId if present
      const booking =
        await this.bookingsService.createBooking(createBookingDto);
      return {
        success: true,
        data: booking,
        message: 'User booking created successfully by admin',
      };
    } catch (error) {
      console.error('Error in createUserBookingByAdmin controller:', error);
      throw error; // Let NestJS handle the error response
    }
  }

  @Get('admin/all')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async findAllBookingsByAdmin(
    @Query() query: BookingQueryDto,
  ): Promise<PaginatedResponse<Booking>> {
    return this.bookingsService.findAllBookings(query);
  }

  // Admin getting a specific booking by ID
  @Get('admin/:id')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async findBookingByAdmin(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<Booking | null> {
    return this.bookingsService.findOneBooking(id); // No userId, implies admin access
  }

  // Admin comprehensive booking update (including time changes)
  @Patch('admin/:id/comprehensive')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async comprehensivelyUpdateBookingByAdmin(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: AdminUpdateBookingDto,
  ): Promise<ApiResponse<Booking>> {
    try {
      const updatedBooking =
        await this.bookingsService.comprehensivelyUpdateBooking(id, updateDto);
      return {
        success: true,
        data: updatedBooking,
        message: 'Booking updated successfully',
      };
    } catch (error) {
      console.error(
        'Error in comprehensivelyUpdateBookingByAdmin controller:',
        error,
      );
      throw error;
    }
  }

  // Admin updating any booking (status and adminNotes)
  @Patch('admin/:id')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async updateBookingByAdmin(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() adminUpdateDto: AdminUpdateBookingStatusDto, // Use AdminUpdateBookingStatusDto
  ): Promise<Booking> {
    return this.bookingsService.updateBookingStatus(
      id,
      adminUpdateDto.status,
      undefined, // No requestingUserId, indicates admin action
      adminUpdateDto.adminNotes, // Pass adminNotes as notes
    );
  }
}
