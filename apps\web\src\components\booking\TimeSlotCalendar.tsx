'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Calendar,
  Clock,
  Info,
  CheckCircle,
  AlertTriangle,
  Loader2,
  RefreshCw,
  Star,
  Users,
  DollarSign,
  Zap,
  TrendingUp,
} from 'lucide-react';
import {
  availabilityService,
  TimeSlot,
  DayAvailability,
} from '../../services/availability.service';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';

interface TimeSlotCalendarProps {
  roomId: string;
  selectedDate: string;
  locationOperatingHours?: { [key: string]: string };
  onTimeSlotSelection: (startTime: string, endTime: string, duration: number) => void;
  selectedStartTime?: string;
  selectedEndTime?: string;
  roomPricePerHour?: number;
  roomCapacity?: number;
  showAlternatives?: boolean;
  showStatistics?: boolean;
  enableRealTimeRefresh?: boolean;
  minDuration?: number; // Minimum booking duration in hours
  maxDuration?: number; // Maximum booking duration in hours
}

interface SelectedTimeSlot {
  startTime: string;
  endTime: string;
  slots: TimeSlot[];
}

interface AlternativeSuggestion {
  startTime: string;
  endTime: string;
  duration: number;
  price: number;
  reason: string;
  popularity?: 'high' | 'medium' | 'low';
}

export const TimeSlotCalendar: React.FC<TimeSlotCalendarProps> = ({
  roomId,
  selectedDate,
  locationOperatingHours,
  onTimeSlotSelection,
  selectedStartTime,
  selectedEndTime,
  roomPricePerHour = 0,
  roomCapacity,
  showAlternatives = true,
  showStatistics = true,
  enableRealTimeRefresh = true,
  minDuration = 0.5, // 30 minutes minimum
  maxDuration = 8, // 8 hours maximum
}) => {
  const [dayAvailability, setDayAvailability] = useState<DayAvailability | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedSlots, setSelectedSlots] = useState<SelectedTimeSlot | null>(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const [alternatives, setAlternatives] = useState<AlternativeSuggestion[]>([]);
  const [showingSuggestions, setShowingSuggestions] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // Load availability when room or date changes
  useEffect(() => {
    if (roomId && selectedDate) {
      loadDayAvailability();
    }
  }, [roomId, selectedDate]);

  // Set up real-time refresh if enabled
  useEffect(() => {
    if (enableRealTimeRefresh && roomId && selectedDate) {
      // Refresh every 2 minutes
      const interval = setInterval(() => {
        loadDayAvailability(true); // Silent refresh
      }, 120000);
      
      setRefreshInterval(interval);
      
      return () => {
        if (interval) clearInterval(interval);
      };
    }
    
    return () => {
      if (refreshInterval) clearInterval(refreshInterval);
    };
  }, [enableRealTimeRefresh, roomId, selectedDate]);

  // Initialize selected slots from props
  useEffect(() => {
    if (selectedStartTime && selectedEndTime && dayAvailability) {
      const startTimeHM = selectedStartTime.split('T')[1]?.slice(0, 5);
      const endTimeHM = selectedEndTime.split('T')[1]?.slice(0, 5);
      
      if (startTimeHM && endTimeHM) {
        const slots = dayAvailability.timeSlots.filter(slot => 
          slot.startTime >= startTimeHM && slot.endTime <= endTimeHM
        );
        
        if (slots.length > 0) {
          setSelectedSlots({
            startTime: startTimeHM,
            endTime: endTimeHM,
            slots,
          });
        }
      }
    } else {
      setSelectedSlots(null);
    }
  }, [selectedStartTime, selectedEndTime, dayAvailability]);

  const loadDayAvailability = async (silent = false) => {
    if (!silent) setLoading(true);
    setError(null);

    try {
      const availability = await availabilityService.getRoomDayAvailabilityWithOverrides(
        roomId,
        selectedDate,
        locationOperatingHours
      );
      setDayAvailability(availability);
      setLastRefresh(new Date());
      
      // Load alternatives if no selection or if showing suggestions
      if (showAlternatives && (!selectedSlots || showingSuggestions)) {
        loadAlternatives(availability);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Lỗi khi tải lịch trống';
      setError(errorMessage);
      console.error('Error loading day availability:', err);
    } finally {
      if (!silent) setLoading(false);
    }
  };

  const loadAlternatives = async (availability?: DayAvailability) => {
    if (!showAlternatives) return;
    
    const currentAvailability = availability || dayAvailability;
    if (!currentAvailability) return;

    try {
      // Generate suggestions based on common booking patterns
      const suggestions: AlternativeSuggestion[] = [];
      const availableSlots = currentAvailability.timeSlots.filter(slot => slot.isAvailable);
      
      // Common durations based on business data
      const commonDurations = [
        { hours: 1, popularity: 'high' as const, reason: '1 giờ (phổ biến)' },
        { hours: 2, popularity: 'high' as const, reason: '2 giờ (được ưa thích)' },
        { hours: 3, popularity: 'medium' as const, reason: '3 giờ (phù hợp nhóm)' },
        { hours: 4, popularity: 'medium' as const, reason: '4 giờ (tiệc nhỏ)' },
        { hours: 6, popularity: 'low' as const, reason: '6 giờ (sự kiện)' },
      ];
      
      for (const { hours, popularity, reason } of commonDurations) {
        const slotsNeeded = hours * 2; // 30-minute slots
        
        for (let i = 0; i <= availableSlots.length - slotsNeeded; i++) {
          const startSlot = availableSlots[i];
          const endSlot = availableSlots[i + slotsNeeded - 1];
          
          // Check if slots are consecutive
          let isConsecutive = true;
          for (let j = i; j < i + slotsNeeded - 1; j++) {
            if (availableSlots[j].endTime !== availableSlots[j + 1].startTime) {
              isConsecutive = false;
              break;
            }
          }
          
          if (isConsecutive) {
            const price = hours * roomPricePerHour;
            suggestions.push({
              startTime: startSlot.startTime,
              endTime: endSlot.endTime,
              duration: hours,
              price,
              reason,
              popularity,
            });
          }
          
          // Limit suggestions per duration
          if (suggestions.filter(s => s.duration === hours).length >= 2) break;
        }
      }
      
      // Sort by popularity and time
      suggestions.sort((a, b) => {
        const popularityOrder = { high: 0, medium: 1, low: 2 };
        const popDiff = popularityOrder[a.popularity || 'low'] - popularityOrder[b.popularity || 'low'];
        if (popDiff !== 0) return popDiff;
        return a.startTime.localeCompare(b.startTime);
      });
      
      setAlternatives(suggestions.slice(0, 8)); // Show max 8 suggestions
    } catch (err) {
      console.error('Error loading alternatives:', err);
    }
  };

  const handleSlotClick = useCallback((clickedSlot: TimeSlot) => {
    if (!clickedSlot.isAvailable || !dayAvailability) return;

    const availableSlots = dayAvailability.timeSlots.filter(slot => slot.isAvailable);
    const clickedIndex = availableSlots.findIndex(slot => 
      slot.startTime === clickedSlot.startTime && slot.endTime === clickedSlot.endTime
    );

    if (clickedIndex === -1) return;

    // If we're starting a new selection or clicking on a non-consecutive slot
    if (!selectedSlots || !isConsecutiveSlot(clickedSlot, selectedSlots)) {
      // Start new selection from this slot
      const newSelection = {
        startTime: clickedSlot.startTime,
        endTime: clickedSlot.endTime,
        slots: [clickedSlot],
      };
      setSelectedSlots(newSelection);
      setIsSelecting(true);
      updateTimeSelection(newSelection);
      return;
    }

    // Extend existing selection
    const newSlots = [...selectedSlots.slots];
    const isExtendingForward = clickedSlot.startTime === selectedSlots.endTime;
    const isExtendingBackward = clickedSlot.endTime === selectedSlots.startTime;

    if (isExtendingForward) {
      // Check max duration constraint
      const newDuration = (selectedSlots.slots.length + 1) * 0.5;
      if (newDuration > maxDuration) {
        setError(`Thời gian tối đa là ${maxDuration} giờ`);
        setTimeout(() => setError(null), 3000);
        return;
      }

      // Extend forward
      newSlots.push(clickedSlot);
      const newSelection = {
        startTime: selectedSlots.startTime,
        endTime: clickedSlot.endTime,
        slots: newSlots,
      };
      setSelectedSlots(newSelection);
      updateTimeSelection(newSelection);
    } else if (isExtendingBackward) {
      // Check max duration constraint
      const newDuration = (selectedSlots.slots.length + 1) * 0.5;
      if (newDuration > maxDuration) {
        setError(`Thời gian tối đa là ${maxDuration} giờ`);
        setTimeout(() => setError(null), 3000);
        return;
      }

      // Extend backward
      newSlots.unshift(clickedSlot);
      const newSelection = {
        startTime: clickedSlot.startTime,
        endTime: selectedSlots.endTime,
        slots: newSlots,
      };
      setSelectedSlots(newSelection);
      updateTimeSelection(newSelection);
    } else {
      // Non-consecutive click - reset to new starting point
      const newSelection = {
        startTime: clickedSlot.startTime,
        endTime: clickedSlot.endTime,
        slots: [clickedSlot],
      };
      setSelectedSlots(newSelection);
      setIsSelecting(true);
      updateTimeSelection(newSelection);
    }
  }, [selectedSlots, dayAvailability, selectedDate, onTimeSlotSelection, maxDuration]);

  const handleAlternativeSelect = (alternative: AlternativeSuggestion) => {
    const startDateTime = `${selectedDate}T${alternative.startTime}:00`;
    const endDateTime = `${selectedDate}T${alternative.endTime}:00`;
    onTimeSlotSelection(startDateTime, endDateTime, alternative.duration);
    setShowingSuggestions(false);
  };

  const clearSelection = () => {
    setSelectedSlots(null);
    setIsSelecting(false);
    onTimeSlotSelection('', '', 0);
  };

  const isConsecutiveSlot = (slot: TimeSlot, selection: SelectedTimeSlot): boolean => {
    return slot.startTime === selection.endTime || slot.endTime === selection.startTime;
  };

  const updateTimeSelection = (selection: SelectedTimeSlot) => {
    const startDateTime = `${selectedDate}T${selection.startTime}:00`;
    const endDateTime = `${selectedDate}T${selection.endTime}:00`;
    const duration = selection.slots.length * 0.5; // Each slot is 30 minutes
    
    // Check minimum duration constraint
    if (duration < minDuration) {
      setError(`Thời gian tối thiểu là ${minDuration} giờ`);
      setTimeout(() => setError(null), 3000);
      return;
    }
    
    onTimeSlotSelection(startDateTime, endDateTime, duration);
  };

  const isSlotSelected = (slot: TimeSlot): boolean => {
    if (!selectedSlots) return false;
    return selectedSlots.slots.some(selectedSlot => 
      selectedSlot.startTime === slot.startTime && selectedSlot.endTime === slot.endTime
    );
  };

  const getSlotClassName = (slot: TimeSlot): string => {
    const baseClasses = "relative p-2 border rounded-md text-xs font-medium text-center transition-all duration-200 cursor-pointer hover:shadow-sm";
    
    if (!slot.isAvailable) {
      if (slot.reason === 'booked') {
        return `${baseClasses} bg-red-100 border-red-300 text-red-700 cursor-not-allowed opacity-60`;
      } else if (slot.reason === 'override_unavailable') {
        return `${baseClasses} bg-orange-100 border-orange-300 text-orange-700 cursor-not-allowed opacity-60`;
      } else {
        return `${baseClasses} bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed opacity-60`;
      }
    }

    if (isSlotSelected(slot)) {
      return `${baseClasses} bg-queens-gold border-queens-gold text-white shadow-md transform scale-105 z-10`;
    }

    return `${baseClasses} bg-green-100 border-green-300 text-green-700 hover:bg-green-200 hover:border-green-400 hover:transform hover:scale-105`;
  };

  const getAvailabilityStats = () => {
    if (!dayAvailability) return null;

    const stats = dayAvailability.timeSlots.reduce(
      (acc, slot) => {
        acc.total++;
        if (slot.isAvailable) {
          acc.available++;
        } else if (slot.reason === 'booked') {
          acc.booked++;
        } else {
          acc.blocked++;
        }
        return acc;
      },
      { total: 0, available: 0, booked: 0, blocked: 0 }
    );

    const availabilityRate = stats.total > 0 ? (stats.available / stats.total) * 100 : 0;
    const peakHours = dayAvailability.timeSlots.filter(slot => 
      slot.isAvailable && (slot.startTime >= '18:00' || slot.startTime <= '22:00')
    ).length;

    return (
      <div className="grid grid-cols-4 gap-3 mb-4">
        <div className="bg-green-50 p-3 rounded-lg border border-green-200">
          <div className="text-xs text-green-600 font-medium">Trống</div>
          <div className="text-lg font-bold text-green-700">{stats.available}</div>
        </div>
        <div className="bg-red-50 p-3 rounded-lg border border-red-200">
          <div className="text-xs text-red-600 font-medium">Đã đặt</div>
          <div className="text-lg font-bold text-red-700">{stats.booked}</div>
        </div>
        <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
          <div className="text-xs text-orange-600 font-medium">Chặn</div>
          <div className="text-lg font-bold text-orange-700">{stats.blocked}</div>
        </div>
        <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
          <div className="text-xs text-blue-600 font-medium flex items-center gap-1">
            <TrendingUp className="w-3 h-3" />
            Giờ vàng
          </div>
          <div className="text-lg font-bold text-blue-700">{peakHours}</div>
        </div>
        <div className="col-span-4 text-center">
          <div className="text-xs text-gray-600">Tỷ lệ trống</div>
          <div className={`text-sm font-bold ${availabilityRate > 50 ? 'text-green-600' : availabilityRate > 25 ? 'text-orange-600' : 'text-red-600'}`}>
            {availabilityRate.toFixed(1)}%
          </div>
        </div>
      </div>
    );
  };

  const getSelectionSummary = () => {
    if (!selectedSlots || selectedSlots.slots.length === 0) return null;

    const duration = selectedSlots.slots.length * 0.5;
    const totalPrice = duration * roomPricePerHour;
    const isPeakTime = selectedSlots.slots.some(slot => 
      slot.startTime >= '18:00' && slot.startTime <= '22:00'
    );

    return (
      <div className="bg-queens-gold/10 border border-queens-gold/30 rounded-lg p-4 mt-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-queens-gold" />
            <h4 className="font-semibold text-gray-800">Thời gian đã chọn</h4>
          </div>
          <Button
            onClick={clearSelection}
            variant="outline"
            size="sm"
            className="text-xs"
          >
            Xóa chọn
          </Button>
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Thời gian:</span>
            <p className="font-medium text-gray-900 flex items-center gap-1">
              {isPeakTime && <Zap className="w-3 h-3 text-yellow-500" />}
              {selectedSlots.startTime} - {selectedSlots.endTime}
            </p>
            {isPeakTime && (
              <p className="text-xs text-yellow-600">Giờ vàng (18:00-22:00)</p>
            )}
          </div>
          
          <div>
            <span className="text-gray-600">Thời lượng:</span>
            <p className="font-medium text-gray-900">
              {duration} giờ ({selectedSlots.slots.length} khung)
            </p>
          </div>
          
          {roomCapacity && (
            <div>
              <span className="text-gray-600">Sức chứa:</span>
              <p className="font-medium text-gray-900 flex items-center gap-1">
                <Users className="w-3 h-3" />
                {roomCapacity} người
              </p>
            </div>
          )}
          
          {roomPricePerHour > 0 && (
            <div>
              <span className="text-gray-600">Tổng giá:</span>
              <p className="font-bold text-queens-gold text-lg flex items-center gap-1">
                <DollarSign className="w-4 h-4" />
                {new Intl.NumberFormat('vi-VN', {
                  style: 'currency',
                  currency: 'VND',
                }).format(totalPrice)}
              </p>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderAlternatives = () => {
    if (!showAlternatives || alternatives.length === 0) return null;

    return (
      <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-medium text-blue-800 flex items-center gap-2">
            <Star className="w-4 h-4" />
            Gợi ý khung giờ phổ biến
          </h4>
          <Button
            onClick={() => setShowingSuggestions(!showingSuggestions)}
            variant="outline"
            size="sm"
            className="text-blue-700 border-blue-300"
          >
            {showingSuggestions ? 'Ẩn' : 'Hiện'} gợi ý
          </Button>
        </div>
        
        {showingSuggestions && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {alternatives.map((alt, index) => (
              <button
                key={index}
                onClick={() => handleAlternativeSelect(alt)}
                className="text-left p-3 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors duration-200 suggestion-card"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="font-medium text-blue-800 flex items-center gap-2">
                      {alt.startTime} - {alt.endTime}
                      {alt.popularity === 'high' && (
                        <Zap className="w-3 h-3 text-yellow-500" />
                      )}
                    </div>
                    <div className="text-xs text-blue-600">{alt.reason}</div>
                    <Badge 
                      variant={alt.popularity === 'high' ? 'default' : 'secondary'} 
                      className="text-xs mt-1"
                    >
                      {alt.popularity === 'high' ? 'Phổ biến' : 
                       alt.popularity === 'medium' ? 'Được ưa thích' : 'Ít người chọn'}
                    </Badge>
                  </div>
                  <Badge variant="secondary" className="text-xs ml-2">
                    {new Intl.NumberFormat('vi-VN', {
                      style: 'currency',
                      currency: 'VND',
                    }).format(alt.price)}
                  </Badge>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>
    );
  };

  const formatDate = (dateStr: string): string => {
    const date = new Date(dateStr);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Hôm nay';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Ngày mai';
    } else {
      return date.toLocaleDateString('vi-VN', {
        weekday: 'long',
        day: 'numeric',
        month: 'numeric',
      });
    }
  };

  const renderTimeSlots = () => {
    if (loading) {
      return (
        <div className="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-12 gap-2">
          {Array.from({ length: 48 }, (_, index) => (
            <div key={index} className="p-2 border rounded-md bg-gray-100 animate-pulse loading-skeleton">
              <div className="h-4 bg-gray-200 rounded w-12 mx-auto"></div>
            </div>
          ))}
        </div>
      );
    }

    if (!dayAvailability?.timeSlots?.length) {
      return (
        <div className="text-center py-8">
          <Clock className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-500">Không có thời gian khả dụng cho ngày này</p>
          <Button
            onClick={() => loadDayAvailability()}
            variant="outline"
            size="sm"
            className="mt-2"
          >
            Tải lại
          </Button>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-12 gap-2 time-slot-grid">
        {dayAvailability.timeSlots.map((slot: TimeSlot, index: number) => (
          <div
            key={`${slot.startTime}-${slot.endTime}`}
            className={`time-slot animate-scale-in ${getSlotClassName(slot)}`}
            style={{ animationDelay: `${index * 0.02}s` }}
            onClick={() => handleSlotClick(slot)}
            title={`${slot.startTime} - ${slot.endTime} (${
              slot.isAvailable ? 'Trống' : 
              slot.reason === 'booked' ? 'Đã đặt' : 
              slot.reason === 'override_unavailable' ? 'Bị chặn' : 'Không khả dụng'
            )`}
          >
            <div>{slot.startTime}</div>
            {!slot.isAvailable && (
              <div className="absolute top-0 right-0 w-2 h-2">
                <div className={`w-full h-full rounded-full ${
                  slot.reason === 'booked' ? 'bg-red-500' : 'bg-orange-500'
                }`}></div>
              </div>
            )}
            {/* Peak time indicator */}
            {slot.isAvailable && slot.startTime >= '18:00' && slot.startTime <= '22:00' && (
              <div className="absolute bottom-0 left-0 w-2 h-2">
                <Zap className="w-2 h-2 text-yellow-500" />
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  return (
    <Card className="transition-all duration-300 shadow-lg availability-container">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Calendar className="w-5 h-5 text-queens-gold" />
            Chọn khung giờ - {formatDate(selectedDate)}
            {enableRealTimeRefresh && lastRefresh && (
              <Badge variant="secondary" className="text-xs ml-2">
                Cập nhật: {lastRefresh.toLocaleTimeString('vi-VN', { timeStyle: 'short' })}
              </Badge>
            )}
          </div>
          {!loading && dayAvailability && (
            <Button
              onClick={() => loadDayAvailability()}
              variant="outline"
              size="sm"
              className="gap-2"
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              Làm mới
            </Button>
          )}
        </CardTitle>
        
        <div className="text-sm text-gray-600 space-y-1">
          <p>• Chọn một khung giờ để bắt đầu (tối thiểu {minDuration}h, tối đa {maxDuration}h)</p>
          <p>• Chọn thêm các khung kề nhau để kéy dài thời gian</p>
          <p>• Khung có <Zap className="inline w-3 h-3 text-yellow-500" /> là giờ vàng (18:00-22:00)</p>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {error && (
          <div className="flex items-center gap-2 p-4 bg-red-50 border border-red-200 rounded-md animate-slide-in-down">
            <AlertTriangle className="h-5 w-5 text-red-500 flex-shrink-0" />
            <span className="text-red-700 flex-1">{error}</span>
            <Button
              onClick={() => loadDayAvailability()}
              variant="outline"
              size="sm"
              className="border-red-300 text-red-700 hover:bg-red-50"
            >
              Thử lại
            </Button>
          </div>
        )}

        {/* Availability Statistics */}
        {showStatistics && !loading && dayAvailability && (
          <div className="animate-fade-in">
            {getAvailabilityStats()}
          </div>
        )}

        {/* Time Slots Grid */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">
            Khung giờ khả dụng (30 phút/khung)
          </h4>

          <div className="availability-transition">
            {renderTimeSlots()}
          </div>

          {/* Legend */}
          {!loading && dayAvailability && (
            <div className="flex flex-wrap gap-4 text-xs animate-fade-in">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
                <span className="text-gray-600">Trống</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-queens-gold border border-queens-gold rounded"></div>
                <span className="text-gray-600">Đã chọn</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-100 border border-red-300 rounded"></div>
                <span className="text-gray-600">Đã đặt</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-orange-100 border border-orange-300 rounded"></div>
                <span className="text-gray-600">Bị chặn</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="w-3 h-3 text-yellow-500" />
                <span className="text-gray-600">Giờ vàng</span>
              </div>
            </div>
          )}
        </div>

        {/* Selection Summary */}
        <div className="animate-slide-in-up">
          {getSelectionSummary()}
        </div>

        {/* Alternative Suggestions */}
        {renderAlternatives()}
      </CardContent>
    </Card>
  );
};