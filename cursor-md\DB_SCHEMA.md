# Queen Karaoke Booking System - Database Schema (PostgreSQL with Prisma)

This document describes the database schema for the Queen Karaoke Booking System. Prisma will be used as the ORM.

**General Notes:**

- All `id` fields are UUIDs (e.g., `String @id @default(uuid())` in Prisma) unless specified otherwise.
- `createdAt` and `updatedAt` timestamps are managed automatically by Prisma (`DateTime @default(now()) @updatedAt`).
- Enums are used for predefined sets of values (e.g., booking status, user roles).
- Indexing is suggested for frequently queried fields and foreign keys.
- All monetary values should be stored as `Decimal` or `Int` (e.g., in cents/smallest currency unit) to avoid floating-point inaccuracies. Here, `Decimal` is used assuming direct currency value storage.

**Internationalization (i18n) Note:**

- Fields that require translation (e.g., names, descriptions) will primarily use JSONB type to store language variants (e.g., `{"vi": "Vietnamese Text", "en": "English Text"}`).
- The backend will be responsible for selecting the appropriate language string from the JSONB object based on the `Accept-Language` header or defaulting to Vietnamese.
- Email templates and similar notification content will also require a bilingual approach, potentially by storing different template versions or using server-side i18n for rendering them.

**ACID Guarantees:**

PostgreSQL is an ACID-compliant RDBMS. Transactions will be used for critical operations, especially for the booking process (checking availability, creating booking, processing payment) to ensure atomicity, consistency, isolation, and durability. Prisma facilitates transactions through its interactive transaction API (`$transaction`).

## Enums

```prisma
enum UserRole {
  CUSTOMER
  ADMIN
  SUPER_ADMIN // Optional: For managing other admins
}

enum BookingStatus {
  PENDING_PAYMENT
  CONFIRMED
  CANCELLED_BY_USER
  CANCELLED_BY_ADMIN
  COMPLETED // After the booking time has passed
  NO_SHOW
}

enum PaymentStatus {
  PENDING
  SUCCESSFUL
  FAILED
  REFUNDED
}
```

## Tables

### 1. `User` (Customers & Admins)

Stores information about registered users, including customers and administrators.

```prisma
model User {
  id            String     @id @default(uuid())
  email         String     @unique
  passwordHash  String     // Bcrypt hashed password
  name          String?
  phoneNumber   String?    @unique
  role          UserRole   @default(CUSTOMER)
  isActive      Boolean    @default(true) // For disabling accounts
  emailVerified Boolean    @default(false)
  lastLogin     DateTime?  // Tracks last login time
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  bookings    Booking[]  // Relation to Bookings made by this user
  auditLogs   AuditLog[] // Admin actions performed by this user

  @@index([email])
  @@index([role])
}
```

### 2. `Location`

Stores details of each physical karaoke branch.

```prisma
model Location {
  id          String  @id @default(uuid())
  name        Json    // Localized: e.g., {"vi": "Quảng Phú", "en": "Quang Phu"}
  address     String
  description Json?   // Localized: e.g., {"vi": "Mô tả tiếng Việt", "en": "English Description"}
  imageUrl    String?
  phoneNumber String?
  email       String?
  operatingHours Json?  // e.g., { "monday": "10:00-23:00", ... }
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  rooms     Room[]     // Relation to Rooms available at this location
  bookings  Booking[]  // Relation to Bookings made for this location
  auditLogs AuditLog[] // Audit logs related to this location

  @@index([name])
}
```

### 3. `Room`

Stores details about individual karaoke rooms within a location.

```prisma
model Room {
  id           String   @id @default(uuid())
  name         Json     // Localized: e.g., {"vi": "Giấc Mơ Ngân Hà", "en": "Galaxy Dream"}
  theme        Json?    // Localized
  description  Json?    // Localized
  capacity     Int      // Maximum number of people
  pricePerHour Decimal  // Price in VND
  images       String[] // Array of image URLs
  amenities    Json[] // Localized: e.g., [{"vi": "TV Màn Hình Lớn", "en": "Large Screen TV"}]
  isActive     Boolean  @default(true) // For temporarily disabling a room
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  locationId String   // Foreign key to Location
  location   Location @relation(fields: [locationId], references: [id], onDelete: Cascade)

  bookings             Booking[]             // Relation to Bookings for this room
  availabilityOverrides AvailabilityOverride[] // Specific overrides for room availability
  auditLogs            AuditLog[]            // Audit logs related to this room

  @@index([locationId])
  @@index([name, locationId], name: "UniqueRoomNamePerLocation") // Ensure room names are unique within a location
}
```

### 4. `TimeSlot` (Implicit or Explicit)

This can be handled in a few ways:

- **Implicitly:** Calculated based on `Room.operatingHours` and booking durations. This is often more flexible.
- **Explicitly:** A `TimeSlot` table defining every possible bookable slot. This can be very large and less flexible for dynamic pricing or duration changes.

For this project, an **implicit** approach combined with `AvailabilityOverride` is generally preferred for flexibility. The system would define standard slot durations (e.g., 1 hour, 2 hours) and calculate availability based on existing bookings and overrides.

If explicit time slots for standard scheduling are needed (e.g. admin defines specific bookable times per day of week for a room type):

```prisma
// Optional: If explicit standard time slots are strictly required per room/day type
// model StandardTimeSlot {
//   id        String   @id @default(uuid())
//   roomId    String
//   room      Room     @relation(fields: [roomId], references: [id])
//   dayOfWeek Int      // 0 for Sunday, 1 for Monday, etc.
//   startTime TimeOnly // TIME type in SQL
//   endTime   TimeOnly // TIME type in SQL
//   isActive  Boolean  @default(true)
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
//
//   @@unique([roomId, dayOfWeek, startTime])
// }
```

### 5. `AvailabilityOverride`

Allows admins to manually mark rooms as unavailable (e.g., for maintenance) or open up special slots outside normal hours.

```prisma
model AvailabilityOverride {
  id          String   @id @default(uuid())
  roomId      String
  room        Room     @relation(fields: [roomId], references: [id], onDelete: Cascade)
  date        DateTime // Date of the override (YYYY-MM-DD)
  startTime   DateTime // Full DateTime for start (UTC)
  endTime     DateTime // Full DateTime for end (UTC)
  isAvailable Boolean  // True if making available, False if blocking
  reason      String?  // e.g., "Maintenance", "Special Event"
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  auditLogs AuditLog[] // Audit logs related to this override

  @@index([roomId, date])
}
```

### 6. `Booking`

Core table storing all booking information.

```prisma
model Booking {
  id             String        @id @default(uuid())
  bookingReference String      @unique @default(cuid()) // User-friendly, shorter booking ID, e.g., QK-XXXXXX

  userId         String?       // Foreign key to User (nullable for guest bookings)
  user           User?         @relation(fields: [userId], references: [id], onDelete: SetNull)

  locationId     String
  location       Location      @relation(fields: [locationId], references: [id], onDelete: Restrict) // Prevent location deletion if bookings exist

  roomId         String
  room           Room          @relation(fields: [roomId], references: [id], onDelete: Restrict)    // Prevent room deletion if bookings exist

  bookingDate    DateTime      // Date of the booking (YYYY-MM-DD)
  startTime      DateTime      // Full DateTime for booking start (UTC)
  endTime        DateTime      // Full DateTime for booking end (UTC)
  durationMinutes Int          // Calculated: endTime - startTime

  numberOfGuests Int
  totalPrice     Decimal       // Final price paid
  currency       String        @default("VND")

  status         BookingStatus @default(PENDING_PAYMENT)

  // Guest details (if userId is null)
  guestName      String?
  guestEmail     String? // Emails to guests (confirmations, reminders) should be in their preferred language if known, or default to Vietnamese.
  guestPhone     String?

  notes          String?       // Special requests or notes from customer or admin
  adminNotes     String?       // Internal notes by admin

  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  payment        Payment?      // Relation to Payment (one-to-one)
  auditLogs      AuditLog[]    // Audit logs related to this booking

  @@index([userId])
  @@index([locationId])
  @@index([roomId])
  @@index([bookingDate])
  @@index([status])
  @@index([guestEmail])
  @@index([startTime, endTime, roomId], name: "RoomTimeSlotUniqueness") // Ensure no overlapping bookings for the same room
}
```

### 7. `Payment`

Tracks payment details for each booking, linked to PayOS.vn transactions.

```prisma
model Payment {
  id                String        @id @default(uuid())
  bookingId         String        @unique // Foreign key to Booking (one-to-one)
  booking           Booking       @relation(fields: [bookingId], references: [id], onDelete: Cascade)

  amount            Decimal
  currency          String        @default("VND")
  paymentGateway    String        @default("PayOS.vn")
  transactionId     String        @unique // ID from PayOS.vn
  paymentIntentId   String?       @unique // Broader intent ID from PayOS if applicable
  status            PaymentStatus @default(PENDING)
  paymentMethod     String?       // e.g., "Credit Card", "Bank Transfer" (from PayOS)
  paidAt            DateTime?     // Timestamp when payment was successful
  rawResponse       Json?         // Store the raw webhook response from PayOS for reconciliation

  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  auditLogs         AuditLog[]    // Audit logs related to this payment

  @@index([transactionId])
  @@index([status])
}
```

### 8. `SystemSetting`

Stores system-wide configurations manageable by admins.

```prisma
model SystemSetting {
  id          String @id @default(uuid())
  key         String @unique // e.g., "PAYOS_API_KEY_PUBLIC", "CANCELLATION_WINDOW_HOURS"
  value       String // Value of the setting (might be JSON string for complex objects, potentially localized if user-facing)
  description String? // Potentially localized if user-facing
  isSensitive Boolean @default(false) // Indicates if value should be masked or handled carefully
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  auditLogs AuditLog[] // Audit logs related to this setting

  @@index([key])
}
```

### 9. `AuditLog`

Records significant actions performed in the system, especially by admins, for security and tracking.

```prisma
model AuditLog {
  id        String   @id @default(uuid())
  timestamp DateTime @default(now())
  userId    String?  // Admin user who performed the action (FK to User)
  user      User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  action    String   // e.g., "CREATE_ROOM", "UPDATE_BOOKING_STATUS", "ADMIN_LOGIN_SUCCESS"
  entity    String?  // Name of the entity affected, e.g., "Room", "Booking"
  entityId  String?  // ID of the affected entity
  details   Json?    // Additional details, e.g., old value vs new value for an update
  ipAddress String?

  // Optional direct relations if you want to query logs by entity easily
  // These are denormalized and need careful management if used.
  bookingId  String?  @map("booking_id")
  booking    Booking? @relation(fields: [bookingId], references: [id], onDelete:SetNull)

  locationId String?  @map("location_id")
  location   Location? @relation(fields: [locationId], references: [id], onDelete:SetNull)

  roomId     String?  @map("room_id")
  room       Room?    @relation(fields: [roomId], references: [id], onDelete:SetNull)

  paymentId  String?  @map("payment_id")
  payment    Payment? @relation(fields: [paymentId], references: [id], onDelete:SetNull)

  settingId  String?  @map("system_setting_id")
  setting    SystemSetting? @relation(fields: [settingId], references: [id], onDelete:SetNull)

  availabilityOverrideId String? @map("availability_override_id")
  availabilityOverride AvailabilityOverride? @relation(fields: [availabilityOverrideId], references: [id], onDelete:SetNull)

  @@index([userId])
  @@index([action])
  @@index([entity, entityId])
  @@index([timestamp])
}
```

### 10. `EmailVerificationToken` / `PasswordResetToken` (Optional, if implementing these features)

For email verification and password reset functionalities.

```prisma
// model EmailVerificationToken {
//   id        String   @id @default(uuid())
//   token     String   @unique
//   userId    String
//   user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
//   expiresAt DateTime
//   createdAt DateTime @default(now())
// }

// model PasswordResetToken {
//   id        String   @id @default(uuid())
//   token     String   @unique
//   userId    String
//   user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
//   expiresAt DateTime
//   createdAt DateTime @default(now())
// }
```

This schema provides a comprehensive starting point. It might evolve as development progresses and more specific requirements emerge.
