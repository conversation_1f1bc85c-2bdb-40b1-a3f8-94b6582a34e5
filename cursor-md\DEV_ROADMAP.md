# Queen Karaoke Booking System - Development Roadmap

This roadmap outlines a phased approach to developing the Queen Karaoke Booking System. Each phase includes key deliverables and milestones. Sprints are assumed to be 2 weeks long. Parallel coordination between frontend and backend teams is crucial.

**Legend:**

- FE: Frontend (Next.js)
- BE: Backend (NestJS)
- DB: Database (PostgreSQL + Prisma)
- CI/CD: Continuous Integration/Continuous Deployment
- Shared: Tasks involving both FE and BE (e.g., API integration, shared types)

## Phase 0: Foundation & Setup (Sprint 0-1) - ~2 Weeks

**Goal:** Establish the project structure, development environment, and core backend functionalities.

**Deliverables:**

- Initialized Monorepo (npm/yarn workspaces).
- Basic Next.js boilerplate (`apps/web`).
- Basic NestJS boilerplate (`apps/api`).
- `tsconfig.base.json` and path aliases configured.
- Docker Compose setup for `db`, `api`, `web`.
- Prisma schema (initial version - User, Location, Room).
- Basic CI pipeline (linting, type checking).
- Core Authentication (Customer & Admin registration/login - BE).
- Shared types package (`packages/shared-types`) for User, Auth payloads.
- FE: Setup i18n library (e.g., `next-international`), basic language switcher UI, and structure for translation files (VI, EN).

**Milestones:**

- [ ] Monorepo initialized and buildable.
- [ ] Docker environment runs locally.
- [ ] Prisma connected to DB, migrations working.
- [ ] BE: User registration and login (JWT) for customers and admins implemented and tested (API level).
- [ ] FE: Basic project structure, placeholder pages for login/register.
- [ ] Shared: Initial User and Auth types defined and usable by FE/BE.
- [ ] CI: Linting and type checks pass automatically.
- [ ] FE: Basic i18n setup complete.

**Coordination:**

- BE defines initial auth API spec and shares with FE.
- FE sets up basic app layout, routing structure, and i18n infrastructure.
- Agreement on `shared-types` structure.

## Phase 1: Core Booking Flow & Admin CRUD (Sprint 2-5) - ~8 Weeks

**Goal:** Implement the public booking flow and essential admin CRUD operations for locations and rooms, with bilingual support for content.

**Sub-Phases/Sprints:**

### Sprint 2: Location & Room Management (Admin)

- **BE/DB:**
  - CRUD API endpoints for Locations (supporting localized `name`, `description`).
  - CRUD API endpoints for Rooms (supporting localized `name`, `theme`, `description`, `amenities`).
  - Refine Prisma schema for Location, Room (using JSONB for localizable fields).
- **FE (Admin Panel):**
  - Admin login page (FE strings translated).
  - Dashboard layout for Admin panel (FE strings translated).
  - UI for Listing, Creating, Editing, Deleting Locations, with input fields for Vietnamese and English for relevant text fields.
  - UI for Listing, Creating, Editing, Deleting Rooms, with input fields for Vietnamese and English for relevant text fields.
- **Shared:** API integration for Location & Room CRUD. Update `shared-types` to reflect localized fields (e.g., using a `LocalizedString` type like `{ vi: string, en: string }`).
- **Content:** Translate initial seed data for locations/rooms into VI and EN.
- **Milestones:**
  - [ ] Admins can log in and manage locations and rooms via UI, including inputting bilingual content.
  - [ ] API for locations and rooms fully functional with tests, serving localized content based on `Accept-Language` header.

### Sprint 3: Public - Location & Room Viewing, Date/Time Selection

- **BE/DB:**
  - Public API endpoints to list Locations (serving localized content).
  - Public API endpoints to list Rooms per Location (serving localized content).
  - API endpoint for Room availability (fetching available time slots for a room on a given date).
  - Prisma schema for `AvailabilityOverride` and logic for calculating available slots.
- **FE (Public Site):**
  - Homepage: Display locations.
  - Location page: Display rooms for selected location.
  - Room detail page (or part of listing): Show room details, images.
  - Calendar UI for date selection.
  - Time slot selection UI based on availability from API.
  - All public site UI text translated (VI/EN).
- **Shared:** API integration for public viewing of locations, rooms, and availability. Update `shared-types`.
- **Content:** Ensure all displayed public content (location names, room details) is translated.
- **Milestones:**
  - [ ] Public users can browse locations and rooms in their selected language.
  - [ ] Public users can select a date and see available time slots for a chosen room.
  - [ ] Availability logic (BE) considers existing bookings (once booking is done) and overrides.

### Sprint 4: Booking Creation & Payment Integration (Initial)

- **BE/DB:**
  - API endpoint to create a booking (PENDING_PAYMENT status initially).
  - Prisma schema for `Booking` and `Payment` tables.
  - Integration with PayOS.vn SDK for creating payment intent/order.
  - Webhook endpoint for PayOS.vn to confirm payment (updates booking and payment status).
- **FE (Public Site):**
  - Booking form (collect user details if guest, or use logged-in user) - all form labels and messages translated.
  - Booking summary page - all text translated.
  - Redirect to PayOS.vn / PayOS UI integration.
  - Frontend handling of payment success/failure callbacks from PayOS - messages translated.
  - Booking confirmation page (on-screen) - translated.
- **Shared:** API integration for booking creation and payment. `shared-types` for Booking, Payment.
- **Content:** Ensure all booking flow related UI text is translated.
- **Milestones:**
  - [ ] Users can complete the booking flow up to payment initiation.
  - [ ] BE can process PayOS webhook and update booking status.
  - [ ] Basic transaction management for booking creation (ACID).

### Sprint 5: Customer Accounts & Basic Booking Management (FE/BE)

- **BE:**
  - API endpoints for logged-in customers to view their upcoming/past bookings.
  - API endpoint for customers to cancel a booking (with policy checks).
  - API endpoints for updating customer profile (`/auth/me`).
- **FE (Customer Dashboard):**
  - Customer login/registration pages integrated with BE - all text translated.
  - Dashboard page for customers - translated.
  - View upcoming/past bookings - translated.
  - Functionality to cancel bookings - translated.
  - Profile update page - translated.
- **BE/Shared:** Email notification service (basic) for booking confirmation and reminders - email templates are bilingual.
- **Content:** Ensure all customer dashboard UI text and email templates are translated.
- **Milestones:**
  - [ ] Registered customers can log in, view their bookings, and manage their profile.
  - [ ] Customers can cancel bookings (subject to rules).
  - [ ] Email confirmations are sent upon successful booking.

## Phase 2: Advanced Admin Features & Polish (Sprint 6-8) - ~6 Weeks

**Goal:** Enhance admin capabilities, implement analytics, and refine the user experience.

### Sprint 6: Advanced Booking Management & Availability (Admin)

- **BE/DB:**
  - Admin API endpoints to view all bookings (with search/filter).
  - Admin API to manually create/update/cancel bookings.
  - Admin API to manage `AvailabilityOverride` (block/unblock slots, special events).
- **FE (Admin Panel):**
  - UI for comprehensive booking management (list, view details, search, filter).
  - UI for manually creating/editing bookings.
  - UI for managing room availability overrides (calendar view ideally).
- **Shared:** API integration for advanced admin booking and availability management.
- **Content:** Translate admin panel elements as decided.
- **Milestones:**
  - [ ] Admins have full control over bookings and room availability.

### Sprint 7: Admin Analytics & User Management

- **BE/DB:**
  - API endpoints for booking analytics (occupancy, revenue per room/location).
  - API endpoints for admin to manage customer accounts (view, activate/deactivate backend logic).
- **FE (Admin Panel):**
  - Dashboard displaying key analytics (charts, tables).
  - UI for customer account management (view, activate/deactivate).
- **Shared:** API integration for analytics.
- \*\*API integration for user account status updates (backend wired).
- **Content:** Translate admin panel elements as decided.
- **Milestones:**
  - [x] Admins can view key business analytics.
  - [x] Admins can manage customer accounts (view, activate/deactivate).

### Sprint 8: System Settings, Email Reminders & Polish

- **BE/DB:**
  - API for `SystemSetting` CRUD (e.g., payment keys, cancellation policies).
  - Implement logic for automated email reminders for upcoming bookings (e.g., via Redis-backed cron/job).
- **FE (Admin Panel):**
  - UI for managing system settings - (as above for translation scope).
- **FE/BE (Overall):**
  - Responsive design checks and improvements across public and admin UIs.
  - Accessibility (ARIA attributes, keyboard navigation) review.
  - Error handling improvements, user feedback messages.
  - Input validation refinement (FE & BE).
- **Milestones:**
  - [ ] Core system settings are configurable by admins.
  - [ ] Automated email reminders are functional.
  - [ ] Application is responsive and has improved UX/accessibility.

## Phase 3: Deployment Prep & Security Hardening (Sprint 9-10) - ~4 Weeks

**Goal:** Prepare for production deployment, focus on security, testing, and documentation.

### Sprint 9: CI/CD Pipeline & Security Hardening

- **CI/CD:**
  - Full CI/CD pipeline: automated tests (unit, integration - basic), build, Docker image creation, push to registry.
  - Deployment scripts/GitHub Actions for deploying to VPS.
- **BE/Security:**
  - Implement rate limiting.
  - Review and implement security best practices (HTTPS setup with Nginx, session security with Redis, input validation review, XSS/CSRF protection basics).
  - Setup logging and monitoring endpoints (health checks).
  - Role-based access control (RBAC) thorough review and testing.
- **Shared:** Comprehensive testing (integration, E2E - if time allows for basic scenarios).
- **Milestones:**
  - [ ] CI/CD pipeline automates build and deployment to a staging environment.
  - [ ] Key security measures implemented and tested.

### Sprint 10: Final Testing, Documentation & Go-Live Prep

- **Overall:**
  - End-to-end testing of all user stories.
  - User Acceptance Testing (UAT) with stakeholders.
  - Performance testing (basic load tests).
  - Finalize Nginx reverse proxy configuration for TLS and domain mapping.
  - Prepare VPS environment (install Docker, Nginx, etc.).
  - Create deployment documentation (`DEPLOYMENT.md`).
  - Update all other Markdown documents (`PROJECT_OVERVIEW.md`, etc.) to reflect final state.
  - Code freeze.
- **Milestones:**
  - [ ] Successful UAT.
  - [ ] System deployed to staging and passes all tests.
  - [ ] Production deployment plan finalized.
  - [ ] All project documentation complete.

## Post-Launch (Ongoing)

- Monitoring and maintenance.
- Bug fixes and minor enhancements.
- Phase-based rollout of new features (e.g., SUPER_ADMIN role, advanced reporting, promotions module).

**Parallel Frontend/Backend Coordination Notes:**

- **Regular Sync-ups:** Daily or bi-daily short stand-ups between FE and BE leads/teams.
- **API Contract First:** BE should define and share API specifications (e.g., OpenAPI/Swagger or updated `API_SPEC.md`) _before_ FE starts integration. Mock APIs (e.g., using Postman mock servers or MSW) can be used by FE until BE endpoints are ready.
- **Shared Types:** Maintain and version the `packages/shared-types` diligently. Any change to data structures requires agreement and update in this package.
- **Feature Branching:** Use feature branches for both FE and BE work that correspond to a user story or a specific feature. Merge to main/develop branches frequently after review and testing.
- **Integrated Testing:** As soon as an API endpoint is ready on BE and FE has a corresponding UI, conduct integration testing.
- **Clear Task Breakdown:** Ensure user stories and tasks are broken down granularly enough so that FE and BE can work on different aspects of the same feature concurrently where possible.
