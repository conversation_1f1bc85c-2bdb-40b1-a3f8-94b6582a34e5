import { useState, useCallback, useRef, useMemo } from 'react';
import { availabilityService } from '../services/availability.service';
import type { RoomAvailabilityResponseDto } from 'shared-types';

export type AvailabilityStatus =
  | 'idle'
  | 'checking'
  | 'available'
  | 'unavailable'
  | 'error';

// Alternative time slot type (temporary until shared-types exports are fixed)
export interface AlternativeTimeSlot {
  startTime: string;
  endTime: string;
  durationMinutes: number;
  price: number;
  reason?: string;
}

interface CachedAvailability {
  data: RoomAvailabilityResponseDto;
  timestamp: number;
}

export interface AvailabilityState {
  status: AvailabilityStatus;
  data: RoomAvailabilityResponseDto | null;
  error: string | null;
  isChecking: boolean;
  alternatives: AlternativeTimeSlot[];
  suggestionsLoading: boolean;
  // Add transition states for smoother UX
  isTransitioning: boolean;
  lastCheckedParams: string | null;
}

export const useAvailabilityCheck = () => {
  const [state, setState] = useState<AvailabilityState>({
    status: 'idle',
    data: null,
    error: null,
    isChecking: false,
    alternatives: [],
    suggestionsLoading: false,
    isTransitioning: false,
    lastCheckedParams: null,
  });

  // Cache for availability results (30 seconds TTL)
  const cache = useRef<Map<string, CachedAvailability>>(new Map());
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);
  const transitionTimer = useRef<NodeJS.Timeout | null>(null);
  const isCheckingRef = useRef(false); // Prevent concurrent checks

  const getCacheKey = (roomId: string, startTime: string, endTime: string) =>
    `${roomId}-${startTime}-${endTime}`;

  const getCachedResult = (
    roomId: string,
    startTime: string,
    endTime: string
  ): RoomAvailabilityResponseDto | null => {
    const key = getCacheKey(roomId, startTime, endTime);
    const cached = cache.current.get(key);

    // Cache for 30 seconds to balance freshness and performance
    if (cached && Date.now() - cached.timestamp < 30000) {
      return cached.data;
    }
    return null;
  };

  const setCachedResult = (
    roomId: string,
    startTime: string,
    endTime: string,
    data: RoomAvailabilityResponseDto
  ) => {
    const key = getCacheKey(roomId, startTime, endTime);
    cache.current.set(key, {
      data,
      timestamp: Date.now(),
    });

    // Clean old cache entries (keep max 50 entries)
    if (cache.current.size > 50) {
      const oldestKey = cache.current.keys().next().value;
      if (oldestKey) {
        cache.current.delete(oldestKey);
      }
    }
  };

  const performAvailabilityCheck = async (
    roomId: string,
    startTime: string,
    endTime: string
  ) => {
    const currentParams = `${roomId}-${startTime}-${endTime}`;

    // Prevent concurrent checks
    if (isCheckingRef.current) {
      return;
    }

    isCheckingRef.current = true;

    try {
      // Start with checking state
      setState(prev => ({
        ...prev,
        status: 'checking',
        isChecking: true,
        error: null,
        alternatives: [],
        suggestionsLoading: false,
        lastCheckedParams: currentParams,
        isTransitioning: false,
      }));

      // Check cache first
      const cachedResult = getCachedResult(roomId, startTime, endTime);
      if (cachedResult) {
        // Add minimum delay for smoother UX even with cached results
        await new Promise(resolve => setTimeout(resolve, 200));

        setState(prev => ({
          ...prev,
          status: cachedResult.isGenerallyAvailable
            ? 'available'
            : 'unavailable',
          data: cachedResult,
          error: null,
          isChecking: false,
          alternatives: [],
          suggestionsLoading: false,
          isTransitioning: false,
        }));

        // If unavailable, fetch alternatives
        if (!cachedResult.isGenerallyAvailable) {
          fetchAlternativeTimeSlots(roomId, startTime, endTime);
        }

        return cachedResult;
      }

      // Add minimum loading time for better perceived performance
      const minLoadingTime = 800;
      const startTime_loading = Date.now();

      // Make API call
      const result = await availabilityService.checkRoomAvailability(roomId, {
        startTime,
        endTime,
      });

      // Cache the result
      setCachedResult(roomId, startTime, endTime, result);

      // Ensure minimum loading time
      const elapsedTime = Date.now() - startTime_loading;
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

      if (remainingTime > 0) {
        await new Promise(resolve => setTimeout(resolve, remainingTime));
      }

      // Update state
      setState(prev => ({
        ...prev,
        status: result.isGenerallyAvailable ? 'available' : 'unavailable',
        data: result,
        error: null,
        isChecking: false,
        alternatives: [],
        suggestionsLoading: false,
        isTransitioning: false,
      }));

      // If unavailable, fetch alternatives
      if (!result.isGenerallyAvailable) {
        fetchAlternativeTimeSlots(roomId, startTime, endTime);
      }

      return result;
    } catch (error) {
      console.error('Availability check failed:', error);

      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Không thể kiểm tra tình trạng phòng. Vui lòng thử lại.';

      setState(prev => ({
        ...prev,
        status: 'error',
        data: null,
        error: errorMessage,
        isChecking: false,
        alternatives: [],
        suggestionsLoading: false,
        isTransitioning: false,
      }));

      throw error;
    } finally {
      isCheckingRef.current = false;
    }
  };

  const fetchAlternativeTimeSlots = async (
    roomId: string,
    startTime: string,
    endTime: string
  ) => {
    try {
      setState(prev => ({ ...prev, suggestionsLoading: true }));

      const alternatives = await availabilityService.findAlternativeTimeSlots(
        roomId,
        {
          startTime,
          endTime,
          searchRangeHours: 4,
          maxSuggestions: 5,
          preferredDuration: true,
        }
      );

      // Add a minimum loading time for better perceived performance
      const minLoadingTime = 600;
      const startTime_loading = Date.now();

      const updateAlternatives = () => {
        setState(prev => ({
          ...prev,
          alternatives: alternatives.alternatives || [],
          suggestionsLoading: false,
        }));
      };

      const elapsedTime = Date.now() - startTime_loading;
      if (elapsedTime < minLoadingTime) {
        setTimeout(updateAlternatives, minLoadingTime - elapsedTime);
      } else {
        updateAlternatives();
      }
    } catch (error) {
      console.warn('Failed to fetch alternative time slots:', error);

      // Gracefully handle suggestion errors
      setTimeout(() => {
        setState(prev => ({
          ...prev,
          suggestionsLoading: false,
          alternatives: [],
        }));
      }, 300);
    }
  };

  const checkAvailability = useCallback(
    (roomId: string, startTime: string, endTime: string) => {
      // Clear any existing timers
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
      if (transitionTimer.current) {
        clearTimeout(transitionTimer.current);
      }

      // Validate inputs
      if (!roomId || !startTime || !endTime) {
        setState(prev => ({
          ...prev,
          status: 'idle',
          data: null,
          error: null,
          alternatives: [],
          suggestionsLoading: false,
          lastCheckedParams: null,
          isTransitioning: false,
        }));
        return;
      }

      // Validate time logic with midnight crossover handling
      const start = new Date(startTime);
      let end = new Date(endTime);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        setState(prev => ({
          ...prev,
          status: 'error',
          data: null,
          error:
            'Thời gian không hợp lệ. Vui lòng kiểm tra lại thời gian đã chọn.',
          isChecking: false,
          alternatives: [],
          suggestionsLoading: false,
          isTransitioning: false,
        }));
        return;
      }

      // Handle midnight crossover: if end time is earlier than start time,
      // assume end time is on the next day (common for karaoke venues)
      if (end <= start) {
        end = new Date(end.getTime() + 24 * 60 * 60 * 1000); // Add 24 hours
      }

      // After handling midnight crossover, validate that end is actually after start
      if (end <= start) {
        setState(prev => ({
          ...prev,
          status: 'error',
          data: null,
          error: 'Thời gian không hợp lệ. Giờ kết thúc phải sau giờ bắt đầu.',
          isChecking: false,
          alternatives: [],
          suggestionsLoading: false,
          isTransitioning: false,
        }));
        return;
      }

      // Check if this is the same request as currently being processed
      const currentParams = `${roomId}-${startTime}-${endTime}`;
      if (isCheckingRef.current) {
        return; // Already checking this or another request
      }

      // Debounce the actual check
      debounceTimer.current = setTimeout(() => {
        performAvailabilityCheck(roomId, startTime, endTime);
      }, 600);
    },
    []
  ); // Remove all dependencies to prevent infinite loops

  const clearAvailability = useCallback(() => {
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }
    if (transitionTimer.current) {
      clearTimeout(transitionTimer.current);
    }

    isCheckingRef.current = false;

    setState({
      status: 'idle',
      data: null,
      error: null,
      isChecking: false,
      alternatives: [],
      suggestionsLoading: false,
      lastCheckedParams: null,
      isTransitioning: false,
    });
  }, []);

  const retryCheck = useCallback(
    (roomId: string, startTime: string, endTime: string) => {
      // Clear cache for this specific check to force fresh data
      const key = getCacheKey(roomId, startTime, endTime);
      cache.current.delete(key);

      checkAvailability(roomId, startTime, endTime);
    },
    [checkAvailability]
  );

  return useMemo(
    () => ({
      ...state,
      checkAvailability,
      clearAvailability,
      retryCheck,
    }),
    [
      state.status,
      state.data,
      state.error,
      state.isChecking,
      state.alternatives,
      state.suggestionsLoading,
      state.isTransitioning,
      state.lastCheckedParams,
      checkAvailability,
      clearAvailability,
      retryCheck,
    ]
  );
};
