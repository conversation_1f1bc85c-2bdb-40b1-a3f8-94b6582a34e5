import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from 'shared-types';
import { AdminCreateUserDto } from './dto/admin-create-user.dto';
import { AdminUpdateUserDto } from './dto/admin-update-user.dto';
import { UpdateUserRoleDto } from './dto/update-user-role.dto';
import { UpdateUserStatusDto } from './dto/update-user-status.dto';
import { AdminChangePasswordDto } from './dto/admin-change-password.dto';

@Controller('users')
@UseGuards(JwtAuthGuard, RolesGuard)
export class UsersController {
  constructor(private usersService: UsersService) {}

  @Get()
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async findAll(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('search') search?: string,
    @Query('role') role?: UserRole,
    @Query('isActive') isActive?: string,
    @Query('emailVerified') emailVerified?: string,
  ) {
    const isActiveBoolean =
      isActive === 'true' ? true : isActive === 'false' ? false : undefined;
    const emailVerifiedBoolean =
      emailVerified === 'true'
        ? true
        : emailVerified === 'false'
          ? false
          : undefined;

    return this.usersService.findAll(
      page,
      limit,
      search,
      role,
      isActiveBoolean,
      emailVerifiedBoolean,
    );
  }

  @Get('stats')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async getUserStats() {
    return this.usersService.getUserStats();
  }

  @Get(':id')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async findOne(@Param('id') id: string) {
    return this.usersService.findOne(id);
  }

  @Get(':id/details')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async findOneWithDetails(@Param('id') id: string) {
    return this.usersService.findByIdWithDetails(id);
  }

  @Post()
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async create(@Body() createUserDto: AdminCreateUserDto) {
    return this.usersService.createUser(createUserDto);
  }

  @Patch(':id')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: AdminUpdateUserDto,
  ) {
    return this.usersService.updateUser(id, updateUserDto);
  }

  @Patch(':id/role')
  @Roles(UserRole.SUPER_ADMIN)
  async updateRole(
    @Param('id') id: string,
    @Body() updateRoleDto: UpdateUserRoleDto,
    @Request() req: any,
  ) {
    return this.usersService.updateUserRole(id, updateRoleDto, req.user.id);
  }

  @Patch(':id/status')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async updateStatus(
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateUserStatusDto,
    @Request() req: any,
  ) {
    return this.usersService.updateUserStatus(id, updateStatusDto, req.user.id);
  }

  @Delete(':id')
  @Roles(UserRole.SUPER_ADMIN)
  async delete(@Param('id') id: string, @Request() req: any) {
    await this.usersService.deleteUser(id, req.user.id);
    return { message: 'Người dùng đã được xóa thành công' };
  }

  @Patch(':id/password')
  @Roles(UserRole.SUPER_ADMIN)
  async changePassword(
    @Param('id') id: string,
    @Body() changePasswordDto: AdminChangePasswordDto,
    @Request() req: any,
  ) {
    return this.usersService.adminChangePassword(
      id,
      changePasswordDto,
      req.user.id,
    );
  }

  @Patch(':id/verify-email')
  @Roles(UserRole.SUPER_ADMIN)
  async verifyEmail(@Param('id') id: string) {
    return this.usersService.adminVerifyEmail(id);
  }
}
