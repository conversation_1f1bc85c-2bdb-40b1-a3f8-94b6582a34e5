'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '../../../components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';
import { analyticsService } from '../../../services/analytics.service';
import BookingTrendChart from '../../../components/dashboard/charts/BookingTrendChart';
import RoomTypeChart from '../../../components/dashboard/charts/RoomTypeChart';
import PeakHoursChart from '../../../components/dashboard/charts/PeakHoursChart';
import PopularRoomsChart from '../../../components/dashboard/charts/PopularRoomsChart';
import StatCard from '../../../components/dashboard/StatCard';
import {
  DashboardAnalyticsDto,
  RoomTypeStatsDto,
  BookingTrendDto,
  PopularRoomDto,
  PeakHoursDto,
} from 'shared-types';
import { DollarSign, Calendar, TrendingUp, Clock } from 'lucide-react';

// Temporary placeholder icons
const Icons = {
  TrendingUp: () => <div className="w-6 h-6 bg-queens-primary rounded"></div>,
  TrendingDown: () => <div className="w-6 h-6 bg-ruby-primary rounded"></div>,
  BarChart: () => <div className="w-6 h-6 bg-sapphire-primary rounded"></div>,
  PieChart: () => <div className="w-6 h-6 bg-opal-primary rounded"></div>,
  Clock: () => <div className="w-6 h-6 bg-pearl-primary rounded"></div>,
  Users: () => <div className="w-6 h-6 bg-gold-600 rounded"></div>,
  Refresh: () => <div className="w-6 h-6 bg-gray-600 rounded"></div>,
};

export default function AnalyticsPage() {
  const [dashboardStats, setDashboardStats] =
    useState<DashboardAnalyticsDto | null>(null);
  const [roomTypeStats, setRoomTypeStats] = useState<RoomTypeStatsDto[]>([]);
  const [bookingTrends, setBookingTrends] = useState<BookingTrendDto[]>([]);
  const [popularRooms, setPopularRooms] = useState<PopularRoomDto[]>([]);
  const [peakHours, setPeakHours] = useState<PeakHoursDto[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [trendDays, setTrendDays] = useState(30);
  const [popularRoomsLimit, setPopularRoomsLimit] = useState(10);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if user is authenticated
      if (!analyticsService.isAuthenticated()) {
        setError(
          'You must be logged in to view analytics. Please log in again.'
        );
        return;
      }

      const [dashboard, roomTypes, trends, popular, peak] = await Promise.all([
        analyticsService.getDashboardStats(),
        analyticsService.getRoomTypeDistribution(),
        analyticsService.getBookingTrends(trendDays),
        analyticsService.getPopularRooms(popularRoomsLimit),
        analyticsService.getPeakHours(),
      ]);

      setDashboardStats(dashboard);
      setRoomTypeStats(roomTypes);
      setBookingTrends(trends);
      setPopularRooms(popular);
      setPeakHours(peak);
    } catch (err) {
      console.error('Analytics fetch error:', err);
      if (err instanceof Error) {
        if (
          err.message.includes('401') ||
          err.message.includes('Unauthorized')
        ) {
          setError(
            'Your session has expired. Please log in again to view analytics.'
          );
        } else if (
          err.message.includes('403') ||
          err.message.includes('Forbidden')
        ) {
          setError(
            'You do not have permission to view analytics. Admin access required.'
          );
        } else {
          setError(err.message);
        }
      } else {
        setError('Failed to fetch analytics data. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, [trendDays, popularRoomsLimit]);

  const handleRefresh = () => {
    fetchAnalyticsData();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-queens-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải dữ liệu phân tích...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <Icons.TrendingDown />
          </div>
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={handleRefresh} variant="outline">
            Thử lại
          </Button>
        </div>
      </div>
    );
  }

  const revenueGrowth = dashboardStats
    ? analyticsService.calculateGrowthPercentage(
        dashboardStats.thisMonthRevenue,
        dashboardStats.previousMonthRevenue
      )
    : 0;

  const bookingGrowth = dashboardStats
    ? analyticsService.calculateGrowthPercentage(
        dashboardStats.thisMonthBookings,
        dashboardStats.previousMonthBookings
      )
    : 0;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-h1 text-gray-800 font-semibold">
            Phân tích kinh doanh
          </h1>
          <p className="text-lg text-gray-600">
            Tổng quan chi tiết về hiệu quả hoạt động karaoke
          </p>
        </div>
        <Button
          onClick={handleRefresh}
          variant="outline"
          className="flex items-center space-x-2"
        >
          <Icons.Refresh />
          <span>Làm mới</span>
        </Button>
      </div>

      {/* Key Metrics */}
      {dashboardStats && (
        <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Tổng doanh thu"
            value={analyticsService.formatCurrency(dashboardStats.totalRevenue)}
            subtitle="Tất cả thời gian"
            icon={DollarSign}
            iconColor="text-queens-primary"
            valueColor="text-queens-primary"
            growth={{
              value: Math.abs(revenueGrowth),
              trend: revenueGrowth >= 0 ? 'up' : 'down',
            }}
          />
          <StatCard
            title="Tổng đặt phòng"
            value={dashboardStats.totalBookings.toString()}
            subtitle="Tất cả thời gian"
            icon={Calendar}
            iconColor="text-sapphire-primary"
            valueColor="text-sapphire-primary"
            growth={{
              value: Math.abs(bookingGrowth),
              trend: bookingGrowth >= 0 ? 'up' : 'down',
            }}
          />
          <StatCard
            title="Tỷ lệ lấp đầy"
            value={`${dashboardStats.occupancyRate.toFixed(1)}%`}
            subtitle="Hiệu suất sử dụng"
            icon={TrendingUp}
            iconColor="text-opal-primary"
            valueColor="text-opal-primary"
          />
          <StatCard
            title="Thời gian đặt TB"
            value={`${dashboardStats.averageBookingDuration.toFixed(1)}h`}
            subtitle="Thời lượng trung bình"
            icon={Clock}
            iconColor="text-pearl-primary"
            valueColor="text-pearl-primary"
          />
        </div>
      )}

      {/* Analytics Tabs */}
      <Tabs defaultValue="trends" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="trends">Xu hướng đặt phòng</TabsTrigger>
          <TabsTrigger value="rooms">Phân tích phòng</TabsTrigger>
          <TabsTrigger value="peak">Giờ cao điểm</TabsTrigger>
          <TabsTrigger value="popular">Phòng phổ biến</TabsTrigger>
        </TabsList>

        {/* Booking Trends */}
        <TabsContent value="trends" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-h2 text-gray-800 font-semibold">
              Xu hướng đặt phòng
            </h2>
            <Select
              value={trendDays.toString()}
              onValueChange={value => setTrendDays(Number(value))}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Chọn khoảng thời gian" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">7 ngày qua</SelectItem>
                <SelectItem value="30">30 ngày qua</SelectItem>
                <SelectItem value="90">90 ngày qua</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Số lượng đặt phòng</CardTitle>
              </CardHeader>
              <CardContent>
                <BookingTrendChart data={bookingTrends} height={300} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Doanh thu & Đặt phòng</CardTitle>
              </CardHeader>
              <CardContent>
                <BookingTrendChart
                  data={bookingTrends}
                  height={300}
                  showRevenue
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Room Type Analysis */}
        <TabsContent value="rooms" className="space-y-6">
          <h2 className="text-h2 text-gray-800 font-semibold">
            Phân tích theo loại phòng
          </h2>

          <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Phân bố loại phòng</CardTitle>
              </CardHeader>
              <CardContent>
                <RoomTypeChart
                  data={roomTypeStats}
                  chartType="pie"
                  height={300}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Doanh thu theo loại phòng</CardTitle>
              </CardHeader>
              <CardContent>
                <RoomTypeChart
                  data={roomTypeStats}
                  chartType="bar"
                  height={300}
                  metric="revenue"
                />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Tỷ lệ lấp đầy theo loại phòng</CardTitle>
            </CardHeader>
            <CardContent>
              <RoomTypeChart
                data={roomTypeStats}
                chartType="bar"
                height={300}
                metric="occupancyRate"
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Peak Hours */}
        <TabsContent value="peak" className="space-y-6">
          <h2 className="text-h2 text-gray-800 font-semibold">
            Phân tích giờ cao điểm
          </h2>

          <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Số đặt phòng theo giờ</CardTitle>
              </CardHeader>
              <CardContent>
                <PeakHoursChart data={peakHours} height={300} chartType="bar" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Doanh thu & Đặt phòng theo giờ</CardTitle>
              </CardHeader>
              <CardContent>
                <PeakHoursChart data={peakHours} height={300} showRevenue />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Popular Rooms */}
        <TabsContent value="popular" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-h2 text-gray-800 font-semibold">
              Phòng phổ biến nhất
            </h2>
            <Select
              value={popularRoomsLimit.toString()}
              onValueChange={value => setPopularRoomsLimit(Number(value))}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Số lượng phòng" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">Top 5</SelectItem>
                <SelectItem value="10">Top 10</SelectItem>
                <SelectItem value="15">Top 15</SelectItem>
                <SelectItem value="20">Top 20</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Theo số đặt phòng</CardTitle>
              </CardHeader>
              <CardContent>
                <PopularRoomsChart
                  data={popularRooms}
                  height={400}
                  metric="bookingCount"
                  layout="horizontal"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Theo doanh thu</CardTitle>
              </CardHeader>
              <CardContent>
                <PopularRoomsChart
                  data={popularRooms}
                  height={400}
                  metric="revenue"
                  layout="horizontal"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
