'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  ArrowLeft,
  Edit,
  MapPin,
  Phone,
  Mail,
  Clock,
  Plus,
  Building,
  Trash2,
} from 'lucide-react';
import { locationsService } from '../../../../services/locations.service';
import { roomsService } from '../../../../services/rooms.service';
import { Location, Room } from 'shared-types';
import { Button } from '../../../../components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../../components/ui/card';
import { useDialogs } from '../../../../components/ui/modal-provider';

interface LocationDetailsState {
  location: Location | null;
  rooms: Room[];
  loading: boolean;
  error: string | null;
}

export default function LocationDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const { alert, confirm, confirmDelete } = useDialogs();
  const locationId = params.id as string;

  const [state, setState] = useState<LocationDetailsState>({
    location: null,
    rooms: [],
    loading: true,
    error: null,
  });

  const fetchLocationDetails = async () => {
    if (!locationId) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const [location, rooms] = await Promise.all([
        locationsService.getLocation(locationId),
        locationsService.getLocationRooms(locationId).catch(() => []), // Graceful fail for rooms
      ]);

      setState(prev => ({
        ...prev,
        location,
        rooms,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Lỗi không xác định',
        loading: false,
      }));
    }
  };

  useEffect(() => {
    fetchLocationDetails();
  }, [locationId]);

  const getLocationDisplayName = (location: Location): string => {
    return location.name?.vi || location.name?.en || 'Không có tên';
  };

  const getLocationDisplayDescription = (location: Location): string => {
    return location.description?.vi || location.description?.en || '';
  };

  const handleDeleteLocation = async () => {
    if (!state.location) return;

    const confirmed = await confirmDelete(
      `Bạn có chắc chắn muốn xóa chi nhánh "${getLocationDisplayName(state.location)}"? Hành động này không thể hoàn tác.`,
      'Xác nhận xóa'
    );

    if (!confirmed) return;

    try {
      await locationsService.deleteLocation(state.location.id);
      await alert('Xóa chi nhánh thành công', 'Thành công', 'success');
      router.push('/dashboard/locations');
    } catch (error) {
      console.error('Error deleting location:', error);
      await alert('Có lỗi xảy ra khi xóa chi nhánh', 'Lỗi', 'error');
    }
  };

  if (state.loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-queens-gold"></div>
      </div>
    );
  }

  if (state.error || !state.location) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">
          {state.error || 'Không tìm thấy chi nhánh'}
        </p>
        <Button onClick={() => router.back()} variant="outline">
          Quay lại
        </Button>
      </div>
    );
  }

  const { location, rooms } = state;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-h1 font-bold text-gray-900">
              {getLocationDisplayName(location)}
            </h1>
            <p className="text-lg text-gray-700">
              Chi tiết chi nhánh và quản lý phòng
            </p>
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={() =>
              router.push(`/dashboard/locations/${location.id}/edit`)
            }
            variant="secondary"
            className="gap-2 bg-white border-2 border-gold-600 text-gold-700 hover:bg-gold-50"
          >
            <Edit className="h-4 w-4" />
            Chỉnh Sửa
          </Button>
          <Button
            onClick={handleDeleteLocation}
            variant="destructive"
            className="gap-2 bg-red-600 hover:bg-red-700 text-white font-medium"
          >
            <Trash2 className="h-4 w-4" />
            Xóa Chi Nhánh
          </Button>
          <Button
            onClick={() =>
              router.push(`/dashboard/rooms/new?locationId=${location.id}`)
            }
            className="gap-2 bg-queens-gold hover:bg-queens-gold/90 text-white font-medium"
          >
            <Plus className="h-4 w-4" />
            Thêm Phòng
          </Button>
        </div>
      </div>

      {/* Location Information */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-queens-gold" />
              Thông Tin Cơ Bản
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Location Image */}
            {location.imageUrl && (
              <div className="relative w-full h-64 rounded-lg overflow-hidden mb-6">
                <img
                  src={location.imageUrl}
                  alt={getLocationDisplayName(location)}
                  className="w-full h-full object-cover"
                />
              </div>
            )}

            <div>
              <h3 className="font-semibold text-gray-900 mb-2">
                Tên chi nhánh
              </h3>
              <p className="text-gray-700">
                {getLocationDisplayName(location)}
              </p>
            </div>

            {getLocationDisplayDescription(location) && (
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Mô tả</h3>
                <p className="text-gray-700">
                  {getLocationDisplayDescription(location)}
                </p>
              </div>
            )}

            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Địa chỉ</h3>
              <p className="text-gray-700">{location.address}</p>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Trạng thái</h3>
              <div
                className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${
                  location.isActive
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                {location.isActive ? 'Đang hoạt động' : 'Ngừng hoạt động'}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5 text-queens-gold" />
              Thông Tin Liên Hệ
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {location.phoneNumber && (
              <div className="flex items-center gap-3">
                <Phone className="h-4 w-4 text-queens-gold" />
                <div>
                  <h3 className="font-semibold text-gray-900">Điện thoại</h3>
                  <p className="text-gray-700">{location.phoneNumber}</p>
                </div>
              </div>
            )}

            {location.email && (
              <div className="flex items-center gap-3">
                <Mail className="h-4 w-4 text-queens-gold" />
                <div>
                  <h3 className="font-semibold text-gray-900">Email</h3>
                  <p className="text-gray-700">{location.email}</p>
                </div>
              </div>
            )}

            <div className="flex items-center gap-3">
              <Clock className="h-4 w-4 text-queens-gold" />
              <div>
                <h3 className="font-semibold text-gray-900">Ngày tạo</h3>
                <p className="text-gray-700">
                  {new Date(location.createdAt).toLocaleDateString('vi-VN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Operating Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-queens-gold" />
            Giờ Hoạt Động
          </CardTitle>
        </CardHeader>
        <CardContent>
          {location.operatingHours ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[
                { key: 'monday', label: 'Thứ Hai' },
                { key: 'tuesday', label: 'Thứ Ba' },
                { key: 'wednesday', label: 'Thứ Tư' },
                { key: 'thursday', label: 'Thứ Năm' },
                { key: 'friday', label: 'Thứ Sáu' },
                { key: 'saturday', label: 'Thứ Bảy' },
                { key: 'sunday', label: 'Chủ Nhật' },
              ].map(({ key, label }) => {
                const hours = (location.operatingHours as any)?.[key];
                return (
                  <div key={key} className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-1">{label}</h4>
                    <p className="text-sm text-gray-700">
                      {hours ? hours : 'Đóng cửa'}
                    </p>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-6">
              <Clock className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Chưa có thông tin giờ hoạt động</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Rooms Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Building className="h-5 w-5 text-queens-gold" />
              Phòng Karaoke ({rooms.length})
            </span>
            <Button
              onClick={() =>
                router.push(`/dashboard/rooms/new?locationId=${location.id}`)
              }
              className="gap-2 bg-queens-gold hover:bg-queens-gold/90 text-white font-medium"
            >
              <Plus className="h-4 w-4" />
              Thêm Phòng
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {rooms.length === 0 ? (
            <div className="text-center py-12">
              <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-700 mb-4">
                Chưa có phòng nào tại chi nhánh này
              </p>
              <Button
                onClick={() =>
                  router.push(`/dashboard/rooms/new?locationId=${location.id}`)
                }
                className="bg-queens-gold hover:bg-queens-gold/90 text-white"
              >
                Tạo phòng đầu tiên
              </Button>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {rooms.map(room => (
                <Card
                  key={room.id}
                  className="luxury-hover border border-gray-200"
                >
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg text-gray-900">
                      {room.name?.vi || room.name?.en || 'Không có tên'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-3 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-700 font-medium">
                          Sức chứa:
                        </span>
                        <span className="font-semibold text-gray-900">
                          {room.capacity} người
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-700 font-medium">
                          Giá/giờ:
                        </span>
                        <span className="font-semibold text-queens-gold text-base">
                          {roomsService.formatPrice(
                            parseFloat(room.pricePerHour as any)
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-700 font-medium">
                          Trạng thái:
                        </span>
                        <span
                          className={`font-semibold ${room.isActive ? 'text-green-600' : 'text-gray-500'}`}
                        >
                          {room.isActive ? 'Hoạt động' : 'Ngừng hoạt động'}
                        </span>
                      </div>
                    </div>

                    <Button
                      variant="action"
                      size="sm"
                      onClick={() => router.push(`/dashboard/rooms/${room.id}`)}
                      className="w-full bg-gold-700 hover:bg-gold-600 text-white font-medium"
                    >
                      Xem Chi Tiết
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
