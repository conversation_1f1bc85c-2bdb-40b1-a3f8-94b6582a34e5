import React from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import {
  Plus,
  Calendar,
  Users,
  Building2,
  BarChart3,
  Settings,
  FileText,
  Search,
} from 'lucide-react';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  path: string;
  color: string;
}

const QuickActions: React.FC = () => {
  const router = useRouter();

  const actions: QuickAction[] = [
    {
      id: 'new-booking',
      title: 'Tạo đặt phòng',
      description: 'Đặt phòng mới cho khách hàng',
      icon: <Plus className="w-5 h-5" />,
      path: '/dashboard/bookings/new',
      color: 'bg-green-500 hover:bg-green-600 text-white hover:text-white',
    },
    {
      id: 'view-bookings',
      title: 'Quản lý đặt phòng',
      description: 'Xem và quản lý đặt phòng',
      icon: <Calendar className="w-5 h-5" />,
      path: '/dashboard/bookings',
      color: 'bg-blue-500 hover:bg-blue-600 text-white hover:text-white',
    },
    {
      id: 'manage-users',
      title: 'Quản lý người dùng',
      description: 'Xem và quản lý tài khoản',
      icon: <Users className="w-5 h-5" />,
      path: '/dashboard/users',
      color: 'bg-purple-500 hover:bg-purple-600 text-white hover:text-white',
    },
    {
      id: 'manage-locations',
      title: 'Quản lý chi nhánh',
      description: 'Cấu hình chi nhánh và phòng',
      icon: <Building2 className="w-5 h-5" />,
      path: '/dashboard/locations',
      color: 'bg-orange-500 hover:bg-orange-600 text-white hover:text-white',
    },
    {
      id: 'analytics',
      title: 'Thống kê chi tiết',
      description: 'Xem báo cáo và phân tích',
      icon: <BarChart3 className="w-5 h-5" />,
      path: '/dashboard/analytics',
      color: 'bg-indigo-500 hover:bg-indigo-600 text-white hover:text-white',
    },
    {
      id: 'room-management',
      title: 'Quản lý phòng',
      description: 'Cấu hình phòng và thiết bị',
      icon: <Settings className="w-5 h-5" />,
      path: '/dashboard/rooms',
      color: 'bg-gray-500 hover:bg-gray-600 text-white hover:text-white',
    },
  ];

  const handleActionClick = (path: string) => {
    router.push(path);
  };

  return (
    <Card className="transition-all duration-300 animate-in fade-in slide-in-from-right duration-500">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-gray-800 transition-all duration-200">
          Thao tác nhanh
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-3">
          {actions.map((action, index) => (
            <Button
              key={action.id}
              variant="outline"
              className={`h-auto p-4 flex flex-col items-center text-center space-y-2 transition-all duration-300 transform hover:scale-105 hover:shadow-lg opacity-0 animate-in fade-in slide-in-from-bottom ${action.color}`}
              onClick={() => handleActionClick(action.path)}
              style={{
                animationDelay: `${index * 100}ms`,
                animationDuration: '400ms',
                animationFillMode: 'forwards',
              }}
            >
              <div className="flex-shrink-0 transition-transform duration-200 transform group-hover:scale-110">
                {action.icon}
              </div>
              <div className="space-y-1">
                <div className="font-medium text-sm leading-tight transition-all duration-200">
                  {action.title}
                </div>
                <div className="text-xs opacity-80 leading-tight transition-all duration-200">
                  {action.description}
                </div>
              </div>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickActions;
