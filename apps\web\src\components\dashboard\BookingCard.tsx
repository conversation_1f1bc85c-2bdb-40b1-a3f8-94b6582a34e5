'use client';

import { Booking, LocalizedString } from 'shared-types';
import StatusBadge from '../ui/status-badge';
import {
  Clock,
  MapPin,
  Users,
  CreditCard,
  Phone,
  Mail,
  Eye,
  Edit,
  Trash2,
  Hash,
} from 'lucide-react';

interface BookingCardProps {
  booking: Booking;
  onView?: (booking: Booking) => void;
  onEdit?: (booking: Booking) => void;
  onCancel?: (booking: Booking) => void;
  showActions?: boolean;
  compact?: boolean;
}

const BookingCard: React.FC<BookingCardProps> = ({
  booking,
  onView,
  onEdit,
  onCancel,
  showActions = true,
  compact = false,
}) => {
  const formatTime = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime);

    const startStr = start.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
    });
    const endStr = end.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
    });

    return `${startStr} - ${endStr}`;
  };

  const formatDate = (dateTime: string) => {
    const date = new Date(dateTime);
    return date.toLocaleDateString('vi-VN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  };

  const getLocalizedString = (str: LocalizedString | string): string => {
    if (typeof str === 'string') return str;
    return str?.vi || str?.en || 'N/A';
  };

  const getDuration = () => {
    const start = new Date(booking.startTime);
    const end = new Date(booking.endTime);
    const hours = Math.round(
      (end.getTime() - start.getTime()) / (1000 * 60 * 60)
    );
    return `${hours} giờ`;
  };

  const customerName =
    booking.user?.name || booking.guestName || 'Khách vãng lai';
  const customerContact =
    booking.user?.email ||
    booking.guestEmail ||
    booking.user?.phoneNumber ||
    booking.guestPhone;

  return (
    <div className="bg-white rounded-lg border-2 border-gray-200 hover:border-gold-300 transition-all duration-200 shadow-sm hover:shadow-md">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-100">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              {/* Enhanced Booking Reference */}
              <div className="flex items-center gap-2 bg-gradient-to-r from-gold-50 to-gold-100 px-3 py-1.5 rounded-lg border border-gold-200">
                <Hash className="w-4 h-4 text-gold-600" />
                <span className="text-h6 font-bold text-gold-800 tracking-wide">
                  {booking.bookingReference}
                </span>
              </div>
              <StatusBadge status={booking.status} />
            </div>

            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <MapPin className="w-4 h-4 text-gold-600" />
                <span>
                  {getLocalizedString(booking.location?.name || 'N/A')}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4 text-gold-600" />
                <span>{formatDate(booking.startTime)}</span>
              </div>
            </div>
          </div>

          {showActions && (
            <div className="flex items-center gap-2">
              {onView && (
                <button
                  onClick={() => onView(booking)}
                  className="p-2 text-gray-600 hover:text-gold-600 hover:bg-gold-50 rounded-lg transition-all duration-150"
                  title="Xem chi tiết"
                >
                  <Eye className="w-4 h-4" />
                </button>
              )}

              {onEdit && (
                <button
                  onClick={() => onEdit(booking)}
                  className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-150"
                  title="Chỉnh sửa"
                >
                  <Edit className="w-4 h-4" />
                </button>
              )}

              {onCancel && (
                <button
                  onClick={() => onCancel(booking)}
                  className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-150"
                  title="Hủy đặt phòng"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="px-6 py-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Room Information */}
          <div className="space-y-2">
            <h4 className="text-h6 font-medium text-gray-900">
              Thông tin phòng
            </h4>
            <div className="space-y-1 text-sm text-gray-600">
              <div>{getLocalizedString(booking.room?.name || 'N/A')}</div>
              <div className="flex items-center gap-1">
                <Users className="w-4 h-4 text-gold-600" />
                <span>{booking.numberOfGuests} khách</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4 text-gold-600" />
                <span>
                  {formatTime(booking.startTime, booking.endTime)} (
                  {getDuration()})
                </span>
              </div>
            </div>
          </div>

          {/* Customer Information */}
          <div className="space-y-2">
            <h4 className="text-h6 font-medium text-gray-900">
              Thông tin khách hàng
            </h4>
            <div className="space-y-1 text-sm text-gray-600">
              <div className="font-medium">{customerName}</div>
              {customerContact && (
                <div className="flex items-center gap-1">
                  {customerContact.includes('@') ? (
                    <Mail className="w-4 h-4 text-gold-600" />
                  ) : (
                    <Phone className="w-4 h-4 text-gold-600" />
                  )}
                  <span>{customerContact}</span>
                </div>
              )}
            </div>
          </div>

          {/* Booking Details */}
          <div className="space-y-2">
            <h4 className="text-h6 font-medium text-gray-900">
              Chi tiết đặt phòng
            </h4>
            <div className="space-y-1 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <CreditCard className="w-4 h-4 text-gold-600" />
                <span className="font-semibold text-gray-900">
                  {formatPrice(booking.totalPrice)}
                </span>
              </div>
              <div>
                Đặt lúc:{' '}
                {new Date(booking.createdAt).toLocaleDateString('vi-VN')}
              </div>
              {booking.adminNotes && (
                <div className="text-xs bg-gray-50 p-2 rounded border-l-4 border-gold-400">
                  <span className="font-medium">Ghi chú admin:</span>{' '}
                  {booking.adminNotes}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Notes */}
        {booking.notes && !compact && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <h4 className="text-h6 font-medium text-gray-900 mb-2">
              Ghi chú khách hàng
            </h4>
            <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
              {booking.notes}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default BookingCard;
