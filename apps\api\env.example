# Database
DATABASE_URL="postgresql://username:password@localhost:5432/queendb?schema=public"

# JWT Secrets
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_REFRESH_SECRET="your-super-secret-refresh-jwt-key-here"
JWT_EXPIRES_IN="24h"
JWT_REFRESH_EXPIRES_IN="7d"

# Redis Configuration
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_PASSWORD=""

# PayOS Configuration
PAYOS_CLIENT_ID="your-payos-client-id"
PAYOS_API_KEY="your-payos-api-key"
PAYOS_CHECKSUM_KEY="your-payos-checksum-key"

# Email Configuration (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"
FROM_EMAIL="<EMAIL>"

# Application Settings
NODE_ENV="development"
PORT=3001
API_BASE_URL="http://localhost:3001"
FRONTEND_URL="http://localhost:3000"

# File Upload (Cloudinary or local)
CLOUDINARY_CLOUD_NAME="your-cloudinary-cloud-name"
CLOUDINARY_API_KEY="your-cloudinary-api-key"
CLOUDINARY_API_SECRET="your-cloudinary-api-secret"

# Security
BCRYPT_ROUNDS=12
CORS_ORIGINS="http://localhost:3000,http://localhost:3001"

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100 

# Email Configuration - For Ethereal (values will be auto-generated by Nodemailer on first use)
EMAIL_HOST=smtp.ethereal.email
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_ethereal_password
EMAIL_FROM_NAME="Queen Booking System"
EMAIL_FROM_ADDRESS="<EMAIL>"

# Optional: Specify a test email address to send all dev emails to, if not using Ethereal
# DEV_EMAIL_RECIPIENT=