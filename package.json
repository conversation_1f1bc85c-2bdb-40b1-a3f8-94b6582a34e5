{"name": "queen-booking-system-monorepo", "version": "1.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently -k -n \"API,WEB\" -c \"cyan,green\" \"npm:dev-api\" \"npm:dev-web\"", "dev-api": "npm --workspace apps/api run start:dev", "dev-web": "npm --workspace apps/web run dev", "build-all": "npm run build --workspaces", "build:shared": "npm --workspace packages/shared-types run build", "build:api": "npm --workspace apps/api run build", "build:web": "npm --workspace apps/web run build", "start-all": "docker-compose up --build", "start:api": "npm --workspace apps/api run start:prod", "start:web": "npm --workspace apps/web run start", "stop-all": "docker-compose down", "lint": "eslint . --ext .ts,.tsx,.js", "lint:fix": "eslint . --ext .ts,.tsx,.js --fix", "format": "prettier --write \"**/*.{ts,tsx,js,json,md}\"", "prisma:generate": "npm --workspace apps/api run prisma:generate", "prisma:migrate:dev": "npm --workspace apps/api run prisma:migrate:dev", "prisma:studio": "npm --workspace apps/api run prisma:studio", "prepare": "husky install"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "concurrently": "^8.2.2", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.0.11", "lint-staged": "^15.2.2", "prettier": "^3.2.5", "typescript": "^5.3.3"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "dependencies": {"uuid": "^11.1.0"}}