import * as React from 'react';
import { cn } from '../../lib/utils';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          'flex h-12 w-full rounded-lg border border-gray-200 bg-white px-4 py-3 text-sm text-gray-900 transition-all duration-200 ease-out placeholder:text-gray-400 focus:border-gold-500 focus:ring-2 focus:ring-gold-500 focus:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50 hover:border-gray-300',
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
Input.displayName = 'Input';

export { Input };
