import {
  DashboardAnalyticsDto,
  RoomTypeStatsDto,
  BookingTrendDto,
  PopularRoomDto,
  PeakHoursDto,
  AnalyticsQueryDto,
} from 'shared-types';

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

class AnalyticsService {
  private getAuthToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('auth-token');
  }

  private getHeaders() {
    const token = this.getAuthToken();
    return {
      'Content-Type': 'application/json',
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    };
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorText = await response.text();
      let errorMessage = `Analytics API Error: ${response.status}`;

      try {
        const errorJson = JSON.parse(errorText);
        errorMessage += ` - ${errorJson.message || errorText}`;
      } catch {
        errorMessage += ` - ${errorText}`;
      }

      throw new Error(errorMessage);
    }
    return response.json();
  }

  private async authenticatedRequest<T>(endpoint: string): Promise<T> {
    const token = this.getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${API_BASE}${endpoint}`, {
      method: 'GET',
      headers: this.getHeaders(),
    });
    return this.handleResponse<T>(response);
  }

  async getDashboardStats(): Promise<DashboardAnalyticsDto> {
    return this.authenticatedRequest('/analytics/dashboard');
  }

  async getRoomTypeDistribution(): Promise<RoomTypeStatsDto[]> {
    return this.authenticatedRequest('/analytics/room-types');
  }

  async getBookingTrends(days: number = 30): Promise<BookingTrendDto[]> {
    return this.authenticatedRequest(`/analytics/booking-trends?days=${days}`);
  }

  async getPopularRooms(limit: number = 10): Promise<PopularRoomDto[]> {
    return this.authenticatedRequest(`/analytics/popular-rooms?limit=${limit}`);
  }

  async getPeakHours(): Promise<PeakHoursDto[]> {
    return this.authenticatedRequest('/analytics/peak-hours');
  }

  // Utility methods for formatting and calculations
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      notation: 'compact',
      maximumFractionDigits: 1,
    }).format(amount);
  }

  formatNumber(num: number): string {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}Tr`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  }

  calculateGrowthPercentage(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }

  getGrowthTrend(current: number, previous: number): 'up' | 'down' | 'stable' {
    const growth = this.calculateGrowthPercentage(current, previous);
    if (growth > 5) return 'up';
    if (growth < -5) return 'down';
    return 'stable';
  }

  getRoomTypeColor(roomType: string): {
    primary: string;
    secondary: string;
    background: string;
  } {
    switch (roomType) {
      case 'QUEENS_EYES':
        return {
          primary: 'text-queens-primary',
          secondary: 'text-queens-secondary',
          background: 'bg-queens-bg',
        };
      case 'RUBY':
        return {
          primary: 'text-ruby-primary',
          secondary: 'text-ruby-secondary',
          background: 'bg-ruby-bg',
        };
      case 'SAPPHIRE':
        return {
          primary: 'text-sapphire-primary',
          secondary: 'text-sapphire-secondary',
          background: 'bg-sapphire-bg',
        };
      case 'OPAL':
        return {
          primary: 'text-opal-primary',
          secondary: 'text-opal-secondary',
          background: 'bg-opal-bg',
        };
      case 'PEARL':
        return {
          primary: 'text-pearl-primary',
          secondary: 'text-pearl-secondary',
          background: 'bg-pearl-bg',
        };
      case 'HALL':
        return {
          primary: 'text-hall-primary',
          secondary: 'text-hall-secondary',
          background: 'bg-hall-bg',
        };
      default: // STANDARD
        return {
          primary: 'text-standard-primary',
          secondary: 'text-standard-secondary',
          background: 'bg-standard-bg',
        };
    }
  }

  getRoomTypeDisplayName(roomType: string): string {
    switch (roomType) {
      case 'QUEENS_EYES':
        return 'Queens Eyes';
      case 'RUBY':
        return 'Ruby';
      case 'SAPPHIRE':
        return 'Sapphire';
      case 'OPAL':
        return 'Opal';
      case 'PEARL':
        return 'Pearl';
      case 'HALL':
        return 'Hall';
      default:
        return 'Standard';
    }
  }

  formatHour(hour: number): string {
    return `${hour.toString().padStart(2, '0')}:00`;
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
    });
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!this.getAuthToken();
  }
}

export const analyticsService = new AnalyticsService();
