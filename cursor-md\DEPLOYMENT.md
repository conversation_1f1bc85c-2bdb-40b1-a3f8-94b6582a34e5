# Queen Karaoke Booking System - Deployment Guide

This document provides an overview of the deployment process for the Queen Karaoke Booking System on a self-hosted Virtual Private Server (VPS) using Docker Compose and Nginx.

## 1. Target Environment: Self-Hosted VPS

- **Operating System:** A modern Linux distribution (e.g., Ubuntu 22.04 LTS).
- **Software Prerequisites:**
  - Docker Engine
  - Docker Compose (V2 or later recommended)
  - Nginx (latest stable version)
  - Certbot (for Let's Encrypt SSL certificates)
  - Firewall configured (e.g., UFW) to allow HTTP (80), HTTPS (443), and SSH (22) traffic.
- **Hardware:** Sufficient CPU, RAM, and disk space for PostgreSQL, Redis, API, and Web services. Start with a modest VPS and scale as needed.

## 2. Docker Compose Service Graph (`docker-compose.yml`)

This is a root `docker-compose.yml` file that defines the services, networks, and volumes.

```yaml
version: '3.8'

services:
  db:
    image: postgres:15 # Use a specific version
    container_name: queen_db
    restart: always
    ports:
      - '5432:5432' # Expose only if direct access from outside Docker network is needed (usually not for prod)
    environment:
      POSTGRES_USER: ${DB_USER:-queendb_user} # Use .env file or pass variables
      POSTGRES_PASSWORD: ${DB_PASSWORD:-StrongP@sswOrd123}
      POSTGRES_DB: ${DB_NAME:-queendb}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - queen_network
    healthcheck:
      test:
        [
          'CMD-SHELL',
          'pg_isready -U ${DB_USER:-queendb_user} -d ${DB_NAME:-queendb}',
        ]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7 # Use a specific version
    container_name: queen_redis
    restart: always
    ports:
      - '6379:6379' # Expose only if needed outside Docker network
    command: redis-server --requirepass ${REDIS_PASSWORD:-StrongRedisP@ss}
    volumes:
      - redis_data:/data
    networks:
      - queen_network
    healthcheck:
      test:
        ['CMD', 'redis-cli', '-a', '${REDIS_PASSWORD:-StrongRedisP@ss}', 'ping']
      interval: 10s
      timeout: 5s
      retries: 5

  api:
    # image: yourdockerusername/queen-karaoke-api:latest # Pulled from registry by CI/CD
    build: # Or build locally if not using pre-built images
      context: .
      dockerfile: apps/api/Dockerfile
    container_name: queen_api
    restart: always
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    ports:
      - '3001:3001' # NestJS app port (internal to Docker network, Nginx will proxy)
    environment:
      NODE_ENV: production
      PORT: 3001
      DATABASE_URL: postgresql://${DB_USER:-queendb_user}:${DB_PASSWORD:-StrongP@sswOrd123}@db:5432/${DB_NAME:-queendb}?schema=public
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-StrongRedisP@ss}
      JWT_SECRET: ${JWT_SECRET:-YourVeryStrongJwtSecretKey}
      PAYOS_CLIENT_ID: ${PAYOS_CLIENT_ID}
      PAYOS_API_KEY: ${PAYOS_API_KEY}
      PAYOS_CHECKSUM_KEY: ${PAYOS_CHECKSUM_KEY}
      # Add other necessary API environment variables
    volumes:
      - ./apps/api/uploads:/app/uploads # Example if API handles file uploads directly
    networks:
      - queen_network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3001/api/health'] # Assuming /api/health endpoint
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s # Give time for the app to start

  web:
    # image: yourdockerusername/queen-karaoke-web:latest # Pulled from registry by CI/CD
    build: # Or build locally
      context: .
      dockerfile: apps/web/Dockerfile
    container_name: queen_web
    restart: always
    depends_on:
      - api
    ports:
      - '3000:3000' # Next.js app port (internal to Docker network, Nginx will proxy)
    environment:
      NODE_ENV: production
      PORT: 3000
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-http://localhost:3001/api} # URL for browser to reach API (via Nginx in prod)
      # Add other necessary Web environment variables
    networks:
      - queen_network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/health'] # Assuming /health endpoint
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  queen_network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
```

**Key points for `docker-compose.yml`:**

- **Environment Variables:** Uses `.env` file at the root (or environment variables set in CI/CD for deployment) for sensitive data like database credentials, JWT secrets, API keys.
- **Image Sources:** Images for `api` and `web` are ideally pulled from a Docker registry (populated by the CI/CD pipeline). The `build` section is an alternative for local development or if building images directly on the server.
- **Networking:** All services are on a custom bridge network (`queen_network`) allowing them to communicate using service names (e.g., `api` can reach `db` at `db:5432`).
- **Volumes:** Named volumes (`postgres_data`, `redis_data`) are used for data persistence.
- **`depends_on` & `healthcheck`:** Ensures services start in the correct order and are healthy before dependent services start (especially `api` depending on `db` and `redis`).
- **Ports:** Services (`api`, `web`, `db`, `redis`) expose ports. Only ports that Nginx needs to proxy to (or that need direct external access, which is rare for DB/Redis in prod) should be mapped to host ports if Nginx runs outside Docker. If Nginx also runs as a Docker container on `queen_network`, then port mapping to host is less critical for backend services.

## 3. Nginx Reverse Proxy Configuration

Nginx will handle SSL/TLS termination, serve static content (if any, though Next.js handles its own), and proxy requests to the appropriate Dockerized applications.

**Example Nginx Configuration (`/etc/nginx/sites-available/queenkaraoke.com.vn`):**

```nginx
# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name booking.queenkaraoke.com.vn www.booking.queenkaraoke.com.vn;

    # For Let's Encrypt ACME challenge
    location /.well-known/acme-challenge/ {
        root /var/www/html; # Or a dedicated directory for Certbot
        allow all;
    }

    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl http2;
    server_name booking.queenkaraoke.com.vn www.booking.queenkaraoke.com.vn;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/booking.queenkaraoke.com.vn/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/booking.queenkaraoke.com.vn/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf; # Recommended SSL parameters by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # Recommended DH parameters by Certbot

    # Security Headers (example, tailor as needed)
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    # add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; ..." always; # Define a strong CSP
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

    # Logging
    access_log /var/log/nginx/queenkaraoke.access.log;
    error_log /var/log/nginx/queenkaraoke.error.log;

    # Proxy to Next.js Web App (running on host port 3000, mapped from Docker)
    location / {
        proxy_pass http://localhost:3000; # Next.js app
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 600s; # Adjust as necessary
        proxy_send_timeout 600s;
    }

    # Proxy to NestJS API App (running on host port 3001, mapped from Docker)
    location /api/ {
        proxy_pass http://localhost:3001/api/; # NestJS API (note the trailing slash if your API base is /api)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;

        # Optional: Strip /api prefix if your NestJS app doesn't expect it
        # rewrite ^/api/(.*)$ /$1 break;
        # proxy_pass http://localhost:3001/$1; # (If rewrite used above)
    }

    # Health check endpoints can be exposed directly or via proxy
    location = /health_nginx {
        access_log off;
        return 200 "Nginx OK";
        add_header Content-Type text/plain;
    }
}
```

**Nginx Setup Steps:**

1.  Install Nginx: `sudo apt update && sudo apt install nginx`.
2.  Configure Firewall: `sudo ufw allow 'Nginx Full'`.
3.  Install Certbot: `sudo apt install certbot python3-certbot-nginx`.
4.  Obtain SSL Certificate: `sudo certbot --nginx -d booking.queenkaraoke.com.vn -d www.booking.queenkaraoke.com.vn` (follow prompts).
5.  Create the Nginx site configuration file (e.g., `/etc/nginx/sites-available/queenkaraoke.com.vn`) with the content above.
6.  Enable the site: `sudo ln -s /etc/nginx/sites-available/queenkaraoke.com.vn /etc/nginx/sites-enabled/`.
7.  Test Nginx configuration: `sudo nginx -t`.
8.  Reload Nginx: `sudo systemctl reload nginx`.

## 4. VPS Environment Setup & Deployment Steps

1.  **Provision VPS:** Choose a provider and set up a new VPS with a Linux distribution.
2.  **Initial Server Setup:**
    - Update system: `sudo apt update && sudo apt upgrade -y`.
    - Create a non-root user with sudo privileges.
    - Configure SSH (e.g., disable root login, use key-based authentication).
    - Set up firewall (UFW).
3.  **Install Prerequisites:** Docker, Docker Compose, Nginx, Certbot (as outlined above).
4.  **Domain & DNS:**
    - Point your domain (`booking.queenkaraoke.com.vn`) A record to the VPS IP address.
5.  **Clone Repository (or use CI/CD to deploy files):**
    - `git clone <your-repo-url> /home/<USER>/queen-booking-system`
    - `cd /home/<USER>/queen-booking-system`
6.  **Environment Configuration:**
    - Create a `.env` file in the project root with production values for all environment variables listed in `docker-compose.yml` (DB credentials, JWT secret, API keys, etc.).
    - Ensure this `.env` file is secured and not committed to the repository (add to `.gitignore`).
7.  \*\*Build & Start Docker Containers (if not using pre-built images from CI/CD):
    - `docker-compose build` (if `build:` directive is used in `docker-compose.yml`)
    - `docker-compose up -d`
8.  \*\*Pull & Start Docker Containers (if using pre-built images from CI/CD):
    - Ensure your CI/CD pipeline pushes images to a registry.
    - On the server: `docker-compose pull` (pulls images specified in `docker-compose.yml` with `image:` directive).
    - `docker-compose up -d`.
9.  **Database Migrations:**
    - Run Prisma migrations after the API service is up and connected to the database:
      `docker-compose exec api npm run prisma:migrate:deploy`
10. **Configure Nginx:** Set up Nginx as a reverse proxy (as described in Section 3).
11. **Obtain SSL Certificates:** Use Certbot for Let's Encrypt.
12. **Test:** Thoroughly test the application by accessing it via the domain name.
13. **Set up Monitoring/Logging:** Configure log aggregation, uptime monitoring, etc.

## 5. Updates & Maintenance

- **Application Updates:**
  1.  The CI/CD pipeline builds and pushes new Docker images.
  2.  On the server: `cd /home/<USER>/queen-booking-system`
  3.  `docker-compose pull` (pulls the new `api` and `web` images tagged as `latest` or a specific version configured in `docker-compose.yml`).
  4.  `docker-compose up -d --force-recreate api web` (recreates only the `api` and `web` services, leaving `db` and `redis` running with their data).
  5.  Run database migrations if needed: `docker-compose exec api npm run prisma:migrate:deploy`.
- **System Updates:** Regularly update the VPS operating system and installed packages (`sudo apt update && sudo apt upgrade`).
- **Backup Strategy:** Implement a regular backup strategy for the PostgreSQL database and any persistent volumes (e.g., using `pg_dump` and rsyncing to offsite storage).

This deployment guide provides a comprehensive plan for self-hosting the Queen Karaoke Booking System.
