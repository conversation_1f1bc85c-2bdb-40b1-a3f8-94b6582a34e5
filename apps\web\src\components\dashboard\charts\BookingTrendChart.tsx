'use client';

import React from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine,
} from 'recharts';
import { BookingTrendDto } from 'shared-types';
import { analyticsService } from '../../../services/analytics.service';

interface BookingTrendChartProps {
  data: BookingTrendDto[];
  height?: number;
  showRevenue?: boolean;
}

const BookingTrendChart: React.FC<BookingTrendChartProps> = ({
  data,
  height = 300,
  showRevenue = false,
}) => {
  const formatXAxisLabel = (dateString: string) => {
    return analyticsService.formatDate(dateString);
  };

  const formatTooltipLabel = (value: string) => {
    const date = new Date(value);
    return date.toLocaleDateString('vi-VN', {
      weekday: 'long',
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const formatTooltipValue = (value: number, name: string) => {
    if (name === 'revenue') {
      return [analyticsService.formatCurrency(value), 'Doanh thu'];
    }
    if (name === 'bookings') {
      return [value, 'Số đặt phòng'];
    }
    if (name === 'occupancyRate') {
      return [`${value.toFixed(1)}%`, 'Tỷ lệ lấp đầy'];
    }
    return [value, name];
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) return null;

    return (
      <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
        <p className="text-sm font-medium text-gray-800 mb-2">
          {formatTooltipLabel(label)}
        </p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center space-x-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-sm text-gray-600">
              {formatTooltipValue(entry.value, entry.dataKey)[1]}:
            </span>
            <span className="text-sm font-semibold text-gray-800">
              {formatTooltipValue(entry.value, entry.dataKey)[0]}
            </span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div style={{ height: `${height}px` }} className="w-full">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 20,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="date"
            tickFormatter={formatXAxisLabel}
            stroke="#6b7280"
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          <YAxis
            yAxisId="bookings"
            orientation="left"
            stroke="#6b7280"
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          {showRevenue && (
            <YAxis
              yAxisId="revenue"
              orientation="right"
              stroke="#ab8d59"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={value => analyticsService.formatNumber(value)}
            />
          )}
          <Tooltip content={<CustomTooltip />} />

          <Line
            yAxisId="bookings"
            type="monotone"
            dataKey="bookings"
            stroke="#3b82f6"
            strokeWidth={3}
            dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
            name="bookings"
          />

          {showRevenue && (
            <Line
              yAxisId="revenue"
              type="monotone"
              dataKey="revenue"
              stroke="#ab8d59"
              strokeWidth={3}
              dot={{ fill: '#ab8d59', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#ab8d59', strokeWidth: 2 }}
              name="revenue"
            />
          )}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default BookingTrendChart;
