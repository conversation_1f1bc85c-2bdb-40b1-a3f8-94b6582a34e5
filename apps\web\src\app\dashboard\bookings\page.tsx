'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import type {
  BookingQueryDto,
  Booking,
  UserListResponseDto,
  UserResponseDto,
  User,
} from 'shared-types';
import { UserRole, BookingStatus } from 'shared-types';
import BookingCard from '../../../components/dashboard/BookingCard';
import BookingFilters from '../../../components/dashboard/BookingFilters';
import { bookingsService } from '../../../services/bookings.service';
import { usersService } from '../../../services/users.service';
import { useDialogs } from '../../../components/ui/modal-provider';
import {
  Plus,
  RefreshCw,
  FileText,
  Download,
  ArrowLeft,
  User as UserIcon,
} from 'lucide-react';

const BookingsPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const customerEmail = searchParams.get('email');

  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<BookingQueryDto>({
    page: 1,
    limit: 12,
    ...(customerEmail && { guestEmail: customerEmail }),
  });
  const [pagination, setPagination] = useState({
    total: 0,
    pages: 0,
    currentPage: 1,
  });
  const [customerInfo, setCustomerInfo] = useState<User | null>(null);

  const { alert, confirmDelete } = useDialogs();

  // Fetch customer info when filtering by email
  useEffect(() => {
    if (customerEmail) {
      usersService
        .getUsers({ search: customerEmail, limit: 1 })
        .then((response: UserListResponseDto) => {
          if (response.users && response.users.length > 0) {
            // Find exact email match
            const exactMatch = response.users.find(
              (user: UserResponseDto) => user.email === customerEmail
            );
            if (exactMatch) {
              setCustomerInfo({
                id: exactMatch.id,
                email: exactMatch.email,
                name: exactMatch.name || exactMatch.email,
                role: exactMatch.role,
                isActive: exactMatch.isActive,
                emailVerified: exactMatch.emailVerified,
                createdAt: exactMatch.createdAt,
                updatedAt: exactMatch.updatedAt,
              });
            }
          }
        })
        .catch((error: unknown) => {
          console.error('Error fetching customer info:', error);
          // Set basic info even if user lookup fails
          setCustomerInfo({
            id: '',
            email: customerEmail,
            name: customerEmail,
            role: UserRole.CUSTOMER,
            isActive: true,
            emailVerified: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          });
        });
    }
  }, [customerEmail]);

  const fetchBookings = useCallback(async () => {
    try {
      setLoading(true);
      const response = await bookingsService.getAllBookings(filters);

      setBookings(response.items || []);
      setPagination({
        total: response.total || 0,
        pages: response.totalPages || 0,
        currentPage: response.page || 1,
      });
    } catch (error) {
      console.error('Error fetching bookings:', error);
      setBookings([]);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchBookings();
  }, [fetchBookings]);

  const handleFiltersChange = (newFilters: BookingQueryDto) => {
    setFilters({
      ...newFilters,
      page: 1, // Reset to first page when filters change
      ...(customerEmail && { guestEmail: customerEmail }), // Preserve email filter
    });
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleRefresh = () => {
    fetchBookings();
  };

  const handleExport = async () => {
    // TODO: Implement export functionality
    console.log('Export functionality will be implemented');
  };

  const handleViewBooking = (booking: Booking) => {
    router.push(`/dashboard/bookings/${booking.id}`);
  };

  const handleEditBooking = (booking: Booking) => {
    router.push(`/dashboard/bookings/${booking.id}/edit`);
  };

  const handleDeleteBooking = async (booking: Booking) => {
    try {
      const confirmed = await confirmDelete(
        `Bạn có chắc chắn muốn hủy đặt phòng ${booking.bookingReference}?\n\nThao tác này không thể hoàn tác.`,
        'Xác nhận hủy đặt phòng'
      );

      if (confirmed) {
        await bookingsService.updateBookingAdmin(booking.id, {
          status: BookingStatus.CANCELLED_BY_ADMIN,
          adminNotes: 'Hủy bởi quản trị viên',
        });

        await alert(
          `Đặt phòng ${booking.bookingReference} đã được hủy thành công.`,
          'Thành công',
          'success'
        );

        // Refresh the bookings list
        fetchBookings();
      }
    } catch (error) {
      console.error('Error cancelling booking:', error);
      await alert(
        'Có lỗi xảy ra khi hủy đặt phòng. Vui lòng thử lại.',
        'Lỗi',
        'error'
      );
    }
  };

  const renderPagination = () => {
    if (pagination.pages <= 1) return null;

    const pages = [];
    const startPage = Math.max(1, pagination.currentPage - 2);
    const endPage = Math.min(pagination.pages, pagination.currentPage + 2);

    if (startPage > 1) {
      pages.push(
        <button
          key={1}
          onClick={() => handlePageChange(1)}
          className="px-3 py-2 text-sm border border-gray-300 text-gray-900 hover:bg-gray-50"
        >
          1
        </button>
      );
      if (startPage > 2) {
        pages.push(
          <span
            key="start-ellipsis"
            className="px-3 py-2 text-sm text-gray-900"
          >
            ...
          </span>
        );
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => handlePageChange(i)}
          className={`px-3 py-2 text-sm border ${
            i === pagination.currentPage
              ? 'bg-gold-500 text-white border-gold-500'
              : 'border-gray-300 text-gray-900 hover:bg-gray-50'
          }`}
        >
          {i}
        </button>
      );
    }

    if (endPage < pagination.pages) {
      if (endPage < pagination.pages - 1) {
        pages.push(
          <span key="end-ellipsis" className="px-3 py-2 text-sm text-gray-900">
            ...
          </span>
        );
      }
      pages.push(
        <button
          key={pagination.pages}
          onClick={() => handlePageChange(pagination.pages)}
          className="px-3 py-2 text-sm border border-gray-300 text-gray-900 hover:bg-gray-50"
        >
          {pagination.pages}
        </button>
      );
    }

    return (
      <div className="flex justify-center items-center gap-1 mt-8">
        <button
          onClick={() => handlePageChange(pagination.currentPage - 1)}
          disabled={pagination.currentPage === 1}
          className="px-3 py-2 text-sm border border-gray-300 text-gray-900 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Trước
        </button>
        {pages}
        <button
          onClick={() => handlePageChange(pagination.currentPage + 1)}
          disabled={pagination.currentPage === pagination.pages}
          className="px-3 py-2 text-sm border border-gray-300 text-gray-900 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Sau
        </button>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          {customerEmail && customerInfo ? (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <button
                  onClick={() => router.push('/dashboard/users')}
                  className="p-2 text-gray-900 hover:text-black hover:bg-gray-100 rounded-lg transition-colors"
                  title="Quay lại danh sách người dùng"
                >
                  <ArrowLeft className="w-4 h-4" />
                </button>
                <div className="flex items-center gap-2">
                  <UserIcon className="w-5 h-5 text-gray-900" />
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">
                      Đặt phòng của {customerInfo.name || customerEmail}
                    </h1>
                    <p className="text-sm text-gray-900">
                      Email: {customerEmail}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Quản lý đặt phòng
              </h1>
              <p className="text-gray-900">
                Tổng quan và quản lý tất cả đặt phòng trong hệ thống
              </p>
            </div>
          )}
        </div>

        <div className="flex gap-3">
          <button
            onClick={handleRefresh}
            className="flex items-center px-4 py-2 text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            title="Làm mới dữ liệu"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Làm mới
          </button>

          <button
            onClick={handleExport}
            className="flex items-center px-4 py-2 text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            title="Xuất báo cáo"
          >
            <Download className="w-4 h-4 mr-2" />
            Xuất danh sách
          </button>

          {!customerEmail && (
            <button
              onClick={() => router.push('/dashboard/bookings/new')}
              className="flex items-center px-4 py-2 bg-gold-500 text-white rounded-lg hover:bg-gold-600 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              Tạo đặt phòng mới
            </button>
          )}
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <BookingFilters
          onFiltersChange={handleFiltersChange}
          initialFilters={filters}
        />
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-gray-900">
        <span>
          Hiển thị {bookings.length} trong tổng số {pagination.total} đặt phòng
          {customerEmail && ` của ${customerInfo?.name || customerEmail}`}
        </span>
        {pagination.pages > 1 && (
          <span>
            Trang {pagination.currentPage} / {pagination.pages}
          </span>
        )}
      </div>

      {/* Booking Cards */}
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <RefreshCw className="w-8 h-8 animate-spin text-gold-500" />
          <span className="ml-3 text-gray-900">Đang tải đặt phòng...</span>
        </div>
      ) : bookings.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="w-16 h-16 mx-auto text-gray-900 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {customerEmail
              ? 'Không tìm thấy đặt phòng nào'
              : 'Chưa có đặt phòng nào'}
          </h3>
          <p className="text-gray-900 mb-6">
            {customerEmail
              ? `Khách hàng ${customerInfo?.name || customerEmail} chưa có đặt phòng nào với bộ lọc hiện tại.`
              : 'Hệ thống chưa có đặt phòng nào hoặc không có đặt phòng nào phù hợp với bộ lọc hiện tại.'}
          </p>
          {!customerEmail && (
            <button
              onClick={() => router.push('/dashboard/bookings/new')}
              className="inline-flex items-center px-4 py-2 bg-gold-500 text-white rounded-lg hover:bg-gold-600 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              Tạo đặt phòng đầu tiên
            </button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6">
          {bookings.map(booking => (
            <BookingCard
              key={booking.id}
              booking={booking}
              onView={handleViewBooking}
              onEdit={handleEditBooking}
              onCancel={handleDeleteBooking}
              showActions={true}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      {renderPagination()}
    </div>
  );
};

export default BookingsPage;
