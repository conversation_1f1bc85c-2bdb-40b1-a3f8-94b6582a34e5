'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, MapPin, Phone, Mail, Edit, Trash2, Eye } from 'lucide-react';
import { locationsService } from '../../../services/locations.service';
import { Location, LocationQueryDto, PaginatedResponse } from 'shared-types';
import { Button } from '../../../components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../components/ui/card';
import { useDialogs } from '../../../components/ui/modal-provider';

interface LocationsPageState {
  locations: Location[];
  loading: boolean;
  error: string | null;
}

export default function LocationsPage() {
  const router = useRouter();
  const { alert, confirm } = useDialogs();
  const [state, setState] = useState<LocationsPageState>({
    locations: [],
    loading: true,
    error: null,
  });

  const fetchLocations = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const query: LocationQueryDto = {
        page: 1,
        limit: 50,
        sortBy: 'createdAt' as any,
        sortOrder: 'desc' as any,
      };

      const response: PaginatedResponse<Location> =
        await locationsService.getLocations(query);

      setState(prev => ({
        ...prev,
        locations: response.items,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Lỗi không xác định',
        loading: false,
      }));
    }
  };

  useEffect(() => {
    fetchLocations();
  }, []);

  const handleDeleteLocation = async (locationId: string) => {
    const confirmed = await confirm('Bạn có chắc chắn muốn xóa chi nhánh này?');
    if (!confirmed) return;

    try {
      await locationsService.deleteLocation(locationId);

      // Update local state by removing the deleted location
      setState(prev => ({
        ...prev,
        locations: prev.locations.filter(
          location => location.id !== locationId
        ),
      }));

      await alert('Xóa chi nhánh thành công', 'Thành công', 'success');
    } catch (error) {
      console.error('Error deleting location:', error);
      await alert('Có lỗi xảy ra khi xóa chi nhánh', 'Lỗi', 'error');
    }
  };

  const getLocationDisplayName = (location: Location): string => {
    return location.name?.vi || location.name?.en || 'Không có tên';
  };

  const getLocationDisplayDescription = (location: Location): string => {
    return location.description?.vi || location.description?.en || '';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-h1 font-bold text-gray-900">Quản Lý Chi Nhánh</h1>
          <p className="text-lg text-gray-600">
            Quản lý các chi nhánh Queen Karaoke trên toàn quốc
          </p>
        </div>
        <Button
          onClick={() => router.push('/dashboard/locations/new')}
          className="gap-2 bg-queens-gold hover:bg-queens-gold/90 text-white font-medium"
        >
          <Plus className="h-4 w-4" />
          Thêm Chi Nhánh Mới
        </Button>
      </div>

      {/* Locations Grid */}
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Danh Sách Chi Nhánh ({state.locations.length})</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {state.loading ? (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-queens-gold"></div>
              </div>
            ) : state.error ? (
              <div className="text-center py-12">
                <p className="text-red-600">{state.error}</p>
                <Button
                  onClick={fetchLocations}
                  className="mt-4"
                  variant="outline"
                >
                  Thử lại
                </Button>
              </div>
            ) : state.locations.length === 0 ? (
              <div className="text-center py-12">
                <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Không tìm thấy chi nhánh nào</p>
                <Button
                  onClick={() => router.push('/dashboard/locations/new')}
                  className="mt-4 bg-queens-gold hover:bg-queens-gold/90 text-white"
                >
                  Tạo chi nhánh đầu tiên
                </Button>
              </div>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {state.locations.map(location => (
                  <Card
                    key={location.id}
                    className="luxury-hover border border-gray-200"
                  >
                    {/* Location Image */}
                    {location.imageUrl && (
                      <div className="relative w-full h-48 overflow-hidden">
                        <img
                          src={location.imageUrl}
                          alt={getLocationDisplayName(location)}
                          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                        />
                      </div>
                    )}
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg font-semibold text-gray-900 mb-1">
                            {getLocationDisplayName(location)}
                          </CardTitle>
                          {getLocationDisplayDescription(location) && (
                            <p className="text-sm text-gray-600 line-clamp-2">
                              {getLocationDisplayDescription(location)}
                            </p>
                          )}
                        </div>
                        <div
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            location.isActive
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {location.isActive ? 'Hoạt động' : 'Ngừng hoạt động'}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-2 mb-4">
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <MapPin className="h-4 w-4" />
                          <span className="line-clamp-1">
                            {location.address}
                          </span>
                        </div>
                        {location.phoneNumber && (
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Phone className="h-4 w-4" />
                            <span>{location.phoneNumber}</span>
                          </div>
                        )}
                        {location.email && (
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Mail className="h-4 w-4" />
                            <span>{location.email}</span>
                          </div>
                        )}
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="action"
                          size="sm"
                          onClick={() =>
                            router.push(`/dashboard/locations/${location.id}`)
                          }
                          className="flex-1 bg-gold-700 hover:bg-gold-600 text-white"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Xem
                        </Button>
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() =>
                            router.push(
                              `/dashboard/locations/${location.id}/edit`
                            )
                          }
                          className="flex-1 bg-white border-2 border-gold-600 text-gold-700 hover:bg-gold-50"
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Sửa
                        </Button>
                        <Button
                          variant="delete"
                          size="sm"
                          onClick={() => handleDeleteLocation(location.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
