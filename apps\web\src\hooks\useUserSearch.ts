import { useState, useEffect, useCallback } from 'react';
import { UserResponseDto } from 'shared-types';
import { usersService } from '../services/users.service';

interface UseUserSearchState {
  query: string;
  results: UserResponseDto[];
  loading: boolean;
  error: string | null;
  selectedUser: UserResponseDto | null;
}

interface UseUserSearchReturn extends UseUserSearchState {
  setQuery: (query: string) => void;
  selectUser: (user: UserResponseDto) => void;
  clearSelection: () => void;
  clearResults: () => void;
}

export const useUserSearch = (
  debounceMs: number = 300
): UseUserSearchReturn => {
  const [state, setState] = useState<UseUserSearchState>({
    query: '',
    results: [],
    loading: false,
    error: null,
    selectedUser: null,
  });

  // Debounced search function
  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery || searchQuery.length < 2) {
      setState(prev => ({
        ...prev,
        results: [],
        loading: false,
        error: null,
      }));
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await usersService.getUsers({
        search: searchQuery,
        limit: 10,
        isActive: true,
      });

      setState(prev => ({
        ...prev,
        results: response.users,
        loading: false,
        error: null,
      }));
    } catch (error) {
      console.error('Error searching users:', error);
      setState(prev => ({
        ...prev,
        results: [],
        loading: false,
        error: 'Có lỗi xảy ra khi tìm kiếm khách hàng',
      }));
    }
  }, []);

  // Debounce effect
  useEffect(() => {
    const timer = setTimeout(() => {
      performSearch(state.query);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [state.query, debounceMs, performSearch]);

  const setQuery = useCallback((query: string) => {
    setState(prev => ({ ...prev, query }));
  }, []);

  const selectUser = useCallback((user: UserResponseDto) => {
    setState(prev => ({
      ...prev,
      selectedUser: user,
      results: [],
      query: '',
    }));
  }, []);

  const clearSelection = useCallback(() => {
    setState(prev => ({
      ...prev,
      selectedUser: null,
      query: '',
      results: [],
      error: null,
    }));
  }, []);

  const clearResults = useCallback(() => {
    setState(prev => ({
      ...prev,
      results: [],
      error: null,
    }));
  }, []);

  return {
    ...state,
    setQuery,
    selectUser,
    clearSelection,
    clearResults,
  };
};
