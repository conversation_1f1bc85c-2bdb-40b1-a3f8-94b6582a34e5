# Queen Karaoke Booking System - Design Language

## 🎯 Design Philosophy

**Brand Identity**: Modern luxury karaoke experience with sophisticated gold accents  
**Core Principles**: Elegance, accessibility, consistency, and premium feel  
**Target Audience**: Vietnamese karaoke businesses and their customers  
**Technical Stack**: Next.js 15, TailwindCSS v4, TypeScript, React

### Key Design Rules

1. **Always prioritize accessibility** - WCAG AA compliance minimum
2. **Consistency is king** - Use established patterns across all components
3. **Mobile-first approach** - Design and develop for mobile, enhance for desktop
4. **Performance matters** - Optimize animations and images for all devices
5. **Vietnamese-first content** - Admin interface in Vietnamese, with English support

---

## 🎨 Color System & Usage Rules

### Primary Brand Colors - Queens Gold Palette

**WHEN TO USE**: Primary actions, brand elements, premium features, main navigation

```css
/* Gold Hierarchy - Primary Brand Colors */
--gold-50: #f9f6f0 /* Subtle backgrounds, disabled states */ --gold-100: #f3ede1
  /* Light backgrounds, hover states */ --gold-200: #e7dbc3
  /* Borders, dividers, inactive elements */ --gold-300: #dbc9a5
  /* Muted elements, placeholders */ --gold-400: #bda476
  /* Secondary buttons, less important icons */ --gold-500: #ab8d59
  /* Primary buttons, main brand color */ --gold-600: #987947
  /* Primary button hover, important links */ --gold-700: #806439
  /* Active states, pressed buttons */ --gold-800: #674e2d
  /* Dark text on light backgrounds */ --gold-900: #4e3921
  /* Highest contrast, headings */
  /* RESERVED BRAND COLOR - Use sparingly for maximum impact */
  --queens-gold: #ab8d59 /* Main brand actions only */;
```

**USAGE RULES**:

- `queens-gold` (#ab8d59): ONLY for primary CTAs like "Book Now", "Create", "Save"
- `gold-600`: Secondary important actions like "Edit", "View Details"
- `gold-700`: Hover states for primary actions
- `gold-50-200`: Backgrounds and subtle accents
- `gold-800-900`: Text on light backgrounds

### Room Type Color System

**WHEN TO USE**: Room cards, booking interfaces, type-specific features

#### QUEENS_EYES (Flagship Premium)

```css
--queens-primary: #065f46 /* Deep emerald - VIP features */
  --queens-secondary: #059669 /* Bright emerald - highlights */
  --queens-accent: #674e2d /* Gold-800 for luxury feel */ --queens-bg: #ecfdf5
  /* Soft background */;
```

**USAGE**: Largest rooms, VIP experiences, minibar features, premium pricing

#### RUBY (Luxury Experience)

```css
--ruby-primary: #8b2635 /* Deep ruby red */ --ruby-secondary: #c4394b
  /* Brighter ruby */ --ruby-accent: #987947 /* Gold accent */
  --ruby-bg: #fdf2f4 /* Soft ruby background */;
```

**USAGE**: Premium rooms, romantic settings, special events

#### SAPPHIRE (Professional & Business)

```css
--sapphire-primary: #1e3a8a /* Deep sapphire blue */
  --sapphire-secondary: #3b82f6 /* Professional blue */
  --sapphire-accent: #987947 /* Gold accent */ --sapphire-bg: #f0f4ff
  /* Soft blue background */;
```

**USAGE**: Business meetings, corporate events, professional settings

#### OPAL (Creative & Youthful)

```css
--opal-primary: #7c3aed /* Purple base */ --opal-secondary: #a855f7
  /* Bright purple */ --opal-accent: #987947 /* Gold accent */
  --opal-bg: #faf5ff /* Soft purple background */
  --opal-gradient: linear-gradient(
    135deg,
    #7c3aed 0%,
    #ec4899 50%,
    #f59e0b 100%
  );
```

**USAGE**: Creative sessions, younger demographics, party atmospheres

#### PEARL (Intimate & Elegant)

```css
--pearl-primary: #374151 /* Dark gray for contrast */ --pearl-secondary: #6b7280
  /* Medium gray */ --pearl-accent: #987947 /* Gold accent */
  --pearl-bg: #fefefe /* Pure white */;
```

**USAGE**: Intimate gatherings, elegant settings, refined experiences

#### HALL (Large Groups & Events)

```css
--hall-primary: #1f2937 /* Formal dark */ --hall-secondary: #374151
  /* Medium dark */ --hall-accent: #ab8d59 /* Gold-500 accent */
  --hall-bg: #f9fafb /* Light gray */;
```

**USAGE**: Large groups, corporate events, formal gatherings

#### STANDARD (Regular Bookings)

```css
--standard-primary: #987947 /* Gold-600 */ --standard-secondary: #ab8d59
  /* Gold-500 */ --standard-accent: #806439 /* Gold-700 */
  --standard-bg: #f9f6f0 /* Gold-50 */;
```

**USAGE**: Regular bookings, standard pricing, everyday use

### Supporting Colors - Strict Usage Rules

```css
/* Gray Scale - ONLY for text and neutral elements */
--gray-50: #f9fafb /* Page backgrounds only */ --gray-100: #f3f4f6
  /* Card backgrounds, subtle areas */ --gray-200: #e5e7eb
  /* Borders, dividers - DEFAULT border color */ --gray-300: #d1d5db
  /* Disabled elements, inactive states */ --gray-400: #9ca3af
  /* Placeholder text, secondary icons */ --gray-500: #6b7280
  /* Secondary text, captions */ --gray-600: #4b5563
  /* Primary text - MAIN text color */ --gray-700: #374151
  /* Headings, important text */ --gray-800: #1f2937
  /* High contrast headings */ --gray-900: #111827
  /* Maximum contrast, critical text */
  /* Status Colors - ONLY for system feedback */ --success: #059669
  /* Success messages, confirmations */ --warning: #d97706
  /* Warnings, pending states */ --error: #dc2626
  /* Errors, destructive actions */ --info: #2563eb
  /* Information, neutral notifications */;
```

**CRITICAL RULES**:

- `gray-600`: DEFAULT text color for all body text
- `gray-700`: Headings and important labels
- `gray-200`: DEFAULT border color
- Status colors: ONLY for system feedback, never decorative

---

## 📝 Typography System & Usage Rules

### Font Declaration

```css
font-family: 'var(--font-roboto-condensed)', system-ui, sans-serif;
```

### Typography Hierarchy - Definitive Usage

```css
/* HEADINGS - Strict hierarchy rules */
.text-display:  /* 3.75rem, 700 - ONLY for hero sections, landing pages */
.text-h1:       /* 3rem, 600 - Page titles, main headings */
.text-h2:       /* 2.25rem, 600 - Section titles within pages */
.text-h3:       /* 1.875rem, 600 - Card titles, important subsections */
.text-h4:       /* 1.5rem, 500 - Form sections, minor headings */
.text-h5:       /* 1.25rem, 500 - Labels, small section titles */
.text-h6:       /* 1.125rem, 500 - Field labels, UI element labels */

/* BODY TEXT - Content hierarchy */
.text-lg:       /* 1.125rem, 400 - Important body text, descriptions */
.text-base:     /* 1rem, 400 - DEFAULT body text size */
.text-sm:       /* 0.875rem, 400 - Secondary information, metadata */
.text-xs:       /* 0.75rem, 400 - Captions, fine print, badges */

/* SPECIAL PURPOSE - Use only for specific contexts */
.text-luxury:   /* 2.5rem, 700 - Premium room displays */
.text-price:    /* 1.5rem, 700 - Room prices, financial data */
.text-caption:  /* 0.875rem, 500, gray-500 - Image captions, helper text */
```

**USAGE RULES**:

- **Never skip heading levels** (h1 → h2 → h3, never h1 → h3)
- **One h1 per page maximum**
- **text-base is the default** - use others only when necessary
- **text-price only for monetary values**
- **text-luxury only for premium features**

### Text Color Rules

```css
/* Primary text colors - Strict usage */
.text-gray-900: /* Maximum contrast - critical information only */
.text-gray-800: /* High contrast headings */
.text-gray-700: /* DEFAULT heading color */
.text-gray-600: /* DEFAULT body text color */
.text-gray-500: /* Secondary text, captions */
.text-gray-400: /* Placeholder text, disabled */

/* Brand text colors - Limited usage */
.text-queens-gold:  /* ONLY for brand highlights */
.text-gold-700:     /* Important links, active states */
.text-gold-600:     /* Secondary brand elements */

/* Status text colors - System feedback only */
.text-green-600:    /* Success states */
.text-red-600:      /* Error states */
.text-yellow-600:   /* Warning states */
.text-blue-600:     /* Information states */
```

---

## 🎛️ Component Specifications & Usage Rules

### Button System - Definitive Usage Guide

#### Primary Button (Queens Gold) - MOST IMPORTANT ACTIONS ONLY

```tsx
// WHEN TO USE: Main CTAs, form submissions, primary navigation
// EXAMPLES: "Book Now", "Create Room", "Save Changes", "Confirm Booking"
<Button className="bg-queens-gold hover:bg-queens-gold/90 text-white">
  Book Now
</Button>

// CONTRAST RULE: MUST use white text for accessibility
// Tested: 4.52:1 contrast ratio (WCAG AA compliant)
```

#### Secondary Button - ALTERNATIVE ACTIONS

```tsx
// WHEN TO USE: Alternative actions, cancellations, secondary navigation
// EXAMPLES: "Cancel", "View Details", "Edit", "Back"
<Button
  variant="secondary"
  className="bg-white border-2 border-gold-600 text-gold-700"
>
  View Details
</Button>
```

#### Destructive Button - DANGEROUS ACTIONS ONLY

```tsx
// WHEN TO USE: Delete, remove, destructive operations
// EXAMPLES: "Delete Room", "Cancel Booking", "Remove Image"
<Button variant="destructive" className="bg-red-600 text-white">
  Delete Room
</Button>
```

#### Outline Button - SUBTLE ACTIONS

```tsx
// WHEN TO USE: Less important actions, utility functions
// EXAMPLES: "Refresh", "Reset", "Learn More"
<Button variant="outline">Learn More</Button>
```

#### Room Type Buttons - CONTEXT-SPECIFIC ACTIONS

```tsx
// QUEENS_EYES - Premium actions
<Button className="bg-queens-primary text-white">
  Book VIP Experience
</Button>

// RUBY - Luxury bookings
<Button className="bg-ruby-primary text-white">
  Reserve Ruby Room
</Button>

// SAPPHIRE - Business actions
<Button className="bg-sapphire-primary text-white">
  Book Meeting Room
</Button>
```

### Button Size Rules

```tsx
// Size usage guidelines
size = 'sm'; // 32px height - Compact spaces, card actions
size = 'default'; // 40px height - STANDARD size, most common
size = 'lg'; // 48px height - Important actions, forms
size = 'xl'; // 56px height - Hero sections only
size = 'icon'; // 40x40px - Icon-only buttons
```

### Card System - Structured Content Display

#### Standard Card - DEFAULT LAYOUT

```tsx
// WHEN TO USE: Content grouping, form sections, information display
<Card className="border border-gray-200 shadow-md">
  <CardHeader>
    <CardTitle>Section Title</CardTitle>
    <CardDescription>Optional description</CardDescription>
  </CardHeader>
  <CardContent>{/* Main content */}</CardContent>
  <CardFooter>{/* Actions */}</CardFooter>
</Card>
```

#### Interactive Card - CLICKABLE CONTENT

```tsx
// WHEN TO USE: Room listings, location cards, selectable items
<Card className="luxury-hover cursor-pointer border border-gray-200">
  {/* Content */}
</Card>

// luxury-hover class provides:
// - transform hover:scale-[1.02] hover:-translate-y-1
// - hover:shadow-2xl
// - transition-all duration-300 ease-luxury
```

#### Room Type Cards - SPECIALIZED DISPLAY

```tsx
// QUEENS_EYES - Premium treatment
<Card className="bg-gradient-to-br from-queens-bg via-white to-gold-50 border-2 border-queens-secondary/30 shadow-2xl">

// RUBY - Luxury treatment
<Card className="bg-gradient-to-br from-ruby-bg to-white border border-ruby-secondary/20 shadow-lg">

// OPAL - Special gradient treatment
<Card className="bg-gradient-to-br from-opal-bg to-white border-2 border-transparent shadow-lg relative">
```

### Form Elements - Consistent Input Design

#### Input Field Rules

```tsx
// DEFAULT styling - ALWAYS use this base
<Input className="w-full px-4 py-3 border border-gray-200 rounded-lg
                  focus:ring-2 focus:ring-gold-500 focus:border-gold-500
                  transition-all duration-200 text-gray-900 bg-white
                  hover:border-gray-300" />

// ERROR state - Add red styling
<Input className="border-red-500 focus:border-red-500 focus:ring-red-500" />

// REQUIRED fields - Always mark with asterisk
<Label htmlFor="email">Email Address *</Label>
```

#### Label System

```tsx
// STANDARD label - Default styling
<Label htmlFor="field" className="block text-sm font-medium text-gray-700 mb-2">
  Field Name
</Label>

// REQUIRED field - Add red asterisk
<Label htmlFor="required-field" className="flex items-center">
  Field Name
  <span className="text-red-500 ml-1">*</span>
</Label>

// ERROR state - Change text color
<Label className="text-red-600">
  Field Name
</Label>
```

#### Select/Dropdown Rules

**CRITICAL PATTERN**: Never use SelectValue component directly as it displays raw enum values instead of user-friendly labels.

```tsx
// ❌ WRONG - This will show "CONFIRMED" instead of "Đã xác nhận"
<Select value={status} onValueChange={setStatus}>
  <SelectTrigger>
    <SelectValue placeholder="Chọn trạng thái" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="CONFIRMED">Đã xác nhận</SelectItem>
  </SelectContent>
</Select>;

// ✅ CORRECT - Create helper function and display proper label
const getCurrentStatusLabel = (): string => {
  if (!status) return 'Chọn trạng thái';
  return getStatusLabel(status as BookingStatus);
};

<Select value={status} onValueChange={setStatus}>
  <SelectTrigger className="text-black">
    <span className="text-black">{getCurrentStatusLabel()}</span>
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="CONFIRMED">
      <span className="text-black">Đã xác nhận</span>
    </SelectItem>
  </SelectContent>
</Select>;
```

**REQUIRED PATTERN for ALL selectors**:

1. **Never import SelectValue** - Remove from imports
2. **Always create getCurrentXLabel() helper** - Returns proper display label
3. **Always use black text** - Both in trigger and items
4. **Always wrap SelectItem content** - In span with text-black class

```tsx
// STANDARD implementation pattern for enum selectors
const getCurrentRoleLabel = (): string => {
  if (!selectedRole) return 'Chọn vai trò';
  return getRoleLabel(selectedRole as UserRole);
};

const getCurrentLocationLabel = (): string => {
  if (!locationId) return 'Chọn cơ sở';
  const location = locations.find(l => l.id === locationId);
  return location?.name?.vi || location?.name?.en || 'Không xác định';
};
```

**Why this pattern is CRITICAL**:

- SelectValue automatically displays the `value` prop (backend enum)
- This shows technical values like "CUSTOMER" instead of "Khách hàng"
- User experience is severely impacted by technical jargon
- Vietnamese localization is broken without proper labels

#### Textarea Styling

```tsx
// CONSISTENT with Input styling
<textarea
  className="w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md
             focus:outline-none focus:ring-2 focus:ring-queens-gold 
             focus:border-transparent resize-vertical text-gray-900 bg-white"
  rows={4}
/>
```

#### Search Bar - STANDARD IMPLEMENTATION

**CRITICAL PATTERN**: All search bars must function with real-time filtering and consistent styling.

```tsx
// STANDARD search bar implementation - Use this exact pattern
<div className="relative">
  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-900 w-4 h-4" />
  <input
    type="text"
    placeholder="Tìm kiếm theo mã đặt phòng, tên khách hàng..."
    value={filters.search || ''}
    onChange={e => handleFilterChange('search', e.target.value)}
    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-transparent text-gray-900"
  />
</div>
```

**SEARCH BAR BEHAVIOR RULES**:

1. **Real-time filtering** - Search executes on every keystroke without debouncing
2. **Consistent styling** - Always use the exact classes above including `text-gray-900`
3. **Icon positioning** - Search icon positioned at `left-3` with gray-900 color
4. **Placeholder text** - Use descriptive Vietnamese placeholder text
5. **Focus states** - Gold ring (focus:ring-gold-500) and border removal (focus:border-transparent)

**FILTER INTEGRATION PATTERN**:

```tsx
// Search must integrate with filter state management
const handleFilterChange = (key: keyof QueryDto, value: any) => {
  const newFilters = { ...filters, [key]: value };
  setFilters(newFilters);
  onFiltersChange(newFilters); // Immediate update to parent
};

// Search value preservation in filter objects
const [filters, setFilters] = useState<QueryDto>({
  search: '', // Always include search in initial state
  page: 1,
  limit: 12,
});
```

**USAGE LOCATIONS**:

- Booking management pages (all variations)
- User management pages
- Any list/table interface requiring search
- Must function identically across all implementations

### Modal System - User Interaction Dialogs

#### Alert Modal - INFORMATION DISPLAY

```tsx
// SUCCESS messages
await alert('Operation completed successfully', 'Success', 'success');

// ERROR messages
await alert('An error occurred', 'Error', 'error');

// WARNING messages
await alert('Please review your input', 'Warning', 'warning');

// INFO messages
await alert('Additional information', 'Information', 'info');
```

#### Confirm Modal - USER DECISIONS

```tsx
// STANDARD confirmation
const confirmed = await confirm('Are you sure you want to continue?');

// DANGEROUS actions - Use confirmDelete
const confirmed = await confirmDelete('This action cannot be undone');
```

---

## 🎯 Layout Patterns & Grid Systems

### Container System - Page Structure

#### Page Container - MAIN CONTENT WRAPPER

```tsx
// ALWAYS use for main page content
<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  {/* Page content */}
</div>
```

#### Section Container - CONTENT SECTIONS

```tsx
// For major page sections
<div className="py-16 px-4 sm:px-6 lg:px-8">{/* Section content */}</div>
```

### Grid Systems - Responsive Layouts

#### Room Grid - ROOM LISTINGS

```tsx
// STANDARD room grid layout
<div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
  {rooms.map(room => (
    <RoomCard key={room.id} room={room} />
  ))}
</div>
```

#### Feature Grid - AMENITIES/FEATURES

```tsx
// For amenities, features, small items
<div className="grid gap-4 grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
  {features.map(feature => (
    <FeatureCard key={feature.id} feature={feature} />
  ))}
</div>
```

#### Dashboard Grid - ADMIN LAYOUT

```tsx
// For dashboard statistics, admin cards
<div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
  {stats.map(stat => (
    <StatCard key={stat.id} stat={stat} />
  ))}
</div>
```

#### Form Grid - FORM LAYOUTS

```tsx
// Two-column form layout
<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
  <div className="space-y-2">
    <Label>First Name</Label>
    <Input />
  </div>
  <div className="space-y-2">
    <Label>Last Name</Label>
    <Input />
  </div>
</div>
```

---

## 🎬 Animation Guidelines & Usage Rules

### Animation Timing - STRICT STANDARDS

```css
/* NEVER deviate from these timings */
--duration-micro: 150ms /* Button hover, icon changes */ --duration-small: 200ms
  /* Input focus, simple transitions */ --duration-medium: 300ms
  /* Card hover, modal entrance */ --duration-large: 400ms
  /* Page transitions */ --duration-xl: 600ms /* Complex animations only */
  /* EASING - Use these only */ --ease-out: cubic-bezier(0.4, 0, 0.2, 1)
  /* DEFAULT easing */ --ease-in: cubic-bezier(0.4, 0, 1, 1) /* Entrance only */
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1) /* Both directions */
  --ease-luxury: cubic-bezier(0.25, 0.46, 0.45, 0.94) /* Premium feel */;
```

### Animation Classes - PREDEFINED USAGE

#### Hover Effects - INTERACTIVE ELEMENTS

```tsx
// Luxury card hover - Premium feel
<Card className="luxury-hover">
  {/* Card content */}
</Card>

// Button hover - Quick response
<Button className="button-hover">
  Click Me
</Button>

// Image hover - Smooth zoom
<img className="image-hover" />
```

#### Loading States - USER FEEDBACK

```tsx
// Skeleton loading
<div className="skeleton h-4 w-48 rounded"></div>

// Spinner loading
<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-queens-gold"></div>

// Shimmer effect
<div className="shimmer bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200"></div>
```

#### Entrance Animations - PAGE CONTENT

```tsx
// Staggered list entrance
<div className="stagger-list">
  {items.map((item, index) => (
    <div key={item.id} style={{ animationDelay: `${index * 80}ms` }}>
      {item.content}
    </div>
  ))}
</div>

// Fade in animation
<div className="fade-in">
  {/* Content */}
</div>
```

### Animation Rules

1. **Hover animations: 150-200ms maximum**
2. **Modal entrance: 300ms exactly**
3. **Page transitions: 400ms maximum**
4. **Respect prefers-reduced-motion**
5. **Never stack animations without purpose**

---

## 📱 Responsive Design Rules

### Breakpoint System - MOBILE-FIRST APPROACH

```css
/* Tailwind breakpoints - NEVER use custom ones */
xs:   475px   /* Large phones - custom breakpoint */
sm:   640px   /* Small tablets, large phones */
md:   768px   /* Tablets */
lg:   1024px  /* Small laptops */
xl:   1280px  /* Large laptops */
2xl:  1536px  /* Large screens */
3xl:  1920px  /* Very large screens - custom */
```

### Responsive Implementation Rules

#### Grid Responsiveness - CONSISTENT PATTERNS

```tsx
// ALWAYS follow this pattern for grids
<div className="grid gap-4
                grid-cols-1
                sm:grid-cols-2
                lg:grid-cols-3
                xl:grid-cols-4">
```

#### Typography Responsiveness - READABLE ON ALL DEVICES

```tsx
// Responsive headings
<h1 className="text-2xl sm:text-3xl lg:text-h1">

// Responsive body text - Keep consistent
<p className="text-sm sm:text-base lg:text-lg">
```

#### Button Responsiveness - TOUCH-FRIENDLY

```tsx
// MINIMUM 44px height on mobile (accomplished with default size)
<Button size="default" className="w-full sm:w-auto">
  Mobile-friendly Button
</Button>
```

#### Navigation Responsiveness - MOBILE NAVIGATION

```tsx
// Hide/show elements based on screen size
<div className="hidden lg:flex">Desktop Navigation</div>
<div className="flex lg:hidden">Mobile Navigation</div>
```

---

## ♿ Accessibility Requirements - NON-NEGOTIABLE

### Color Contrast - WCAG AA COMPLIANCE MANDATORY

```css
/* APPROVED text/background combinations */
/* Queens Gold backgrounds (#ab8d59) + white text = 4.52:1 ✓ */
/* Gray-600 text (#4B5563) + white background = 9.48:1 ✓ */
/* Gray-700 text (#374151) + white background = 12.63:1 ✓ */

/* FORBIDDEN combinations */
/* Gold text on white background = FAIL */
/* Light gray text on colored backgrounds = CHECK FIRST */
```

### Interactive Elements - ACCESSIBILITY STANDARDS

#### Focus Management - KEYBOARD NAVIGATION

```tsx
// ALWAYS include focus styles
<Button className="focus:ring-2 focus:ring-gold-500 focus:ring-offset-2">
  Accessible Button
</Button>

// Custom focus for interactive elements
<div className="focus:outline-none focus:ring-2 focus:ring-gold-500 rounded-lg"
     tabIndex={0}
     role="button">
```

#### ARIA Labels - SCREEN READER SUPPORT

```tsx
// REQUIRED for icon-only buttons
<Button aria-label="Delete room" className="p-2">
  <Trash2 className="h-4 w-4" />
</Button>

// REQUIRED for form inputs
<Label htmlFor="email">Email Address</Label>
<Input id="email" aria-describedby="email-help" />
<p id="email-help" className="text-sm text-gray-500">
  We'll never share your email
</p>
```

#### Semantic HTML - PROPER STRUCTURE

```tsx
// ALWAYS use semantic elements
<main>
  <section>
    <h1>Page Title</h1>
    <article>
      <h2>Section Title</h2>
      <p>Content...</p>
    </article>
  </section>
</main>
```

---

## 🏗️ Implementation Rules for Developers

### Component Development Guidelines - MANDATORY PRACTICES

#### 1. Component Structure - CONSISTENT PATTERN

```tsx
// ALWAYS follow this structure
interface ComponentProps {
  // Props definition
}

const Component: React.FC<ComponentProps> = ({ prop1, prop2 }) => {
  // State and effects

  // Event handlers with 'handle' prefix
  const handleClick = () => {};
  const handleSubmit = () => {};

  // Early returns for loading/error states
  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage />;

  return <div className="component-container">{/* JSX content */}</div>;
};

export default Component;
```

#### 2. Styling Rules - NO EXCEPTIONS

```tsx
// NEVER use inline styles
// BAD: <div style={{ color: 'red' }}>

// ALWAYS use Tailwind classes
// GOOD: <div className="text-red-600">

// CONDITIONAL styling with cn() utility
import { cn } from '@/lib/utils';

<div className={cn(
  "base-classes",
  isActive && "active-classes",
  error && "error-classes"
)}>
```

#### 3. Event Handler Naming - STRICT CONVENTION

```tsx
// ALWAYS prefix with 'handle'
const handleClick = () => {};
const handleSubmit = () => {};
const handleChange = () => {};
const handleKeyDown = () => {};

// NEVER use generic names
// BAD: const onClick = () => {};
// BAD: const submit = () => {};
```

#### 4. TypeScript Usage - FULL TYPE SAFETY

```tsx
// ALWAYS define proper interfaces
interface RoomCardProps {
  room: Room;
  onSelect: (roomId: string) => void;
  className?: string;
}

// NEVER use 'any' type
// BAD: const data: any = {};
// GOOD: const data: Room = {};
```

### Room Type Implementation - SPECIALIZED COMPONENTS

#### Room Card Component - TYPE-SPECIFIC STYLING

```tsx
const RoomCard: React.FC<RoomCardProps> = ({ room, onSelect }) => {
  // ALWAYS determine room type styling
  const getRoomTypeStyles = (roomType: RoomTypeEnum) => {
    switch (roomType) {
      case RoomTypeEnum.QUEENS_EYES:
        return {
          cardClass:
            'bg-gradient-to-br from-queens-bg via-white to-gold-50 border-2 border-queens-secondary/30',
          badgeClass: 'bg-queens-primary text-white',
          priceClass: 'text-queens-primary',
        };
      case RoomTypeEnum.RUBY:
        return {
          cardClass:
            'bg-gradient-to-br from-ruby-bg to-white border border-ruby-secondary/20',
          badgeClass: 'bg-ruby-primary text-white',
          priceClass: 'text-ruby-primary',
        };
      // ... other room types
      default:
        return {
          cardClass: 'bg-white border border-gray-200',
          badgeClass: 'bg-gold-600 text-white',
          priceClass: 'text-gold-700',
        };
    }
  };

  const styles = getRoomTypeStyles(room.roomType);

  return (
    <Card className={cn('luxury-hover cursor-pointer', styles.cardClass)}>
      {/* Room card content */}
    </Card>
  );
};
```

### Animation Implementation - PERFORMANCE FOCUSED

#### Framer Motion Usage - ONLY WHEN NECESSARY

```tsx
import { motion } from 'framer-motion';

// ONLY use for complex animations
const RoomGrid: React.FC = ({ rooms }) => {
  return (
    <motion.div
      className="room-grid"
      variants={staggerContainer}
      initial="hidden"
      animate="visible"
    >
      {rooms.map((room, index) => (
        <motion.div
          key={room.id}
          variants={staggerItem}
          whileHover={{
            scale: 1.02,
            y: -4,
            transition: { duration: 0.2 }, // ALWAYS specify duration
          }}
        >
          <RoomCard room={room} />
        </motion.div>
      ))}
    </motion.div>
  );
};

// PREDEFINED stagger patterns
const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.08 }, // 80ms between items
  },
};
```

#### CSS Animations - PREFER OVER JAVASCRIPT

```tsx
// USE CSS classes for simple animations
<div className="transform transition-all duration-200 hover:scale-105">

// AVOID JavaScript animations for simple effects
// BAD: useEffect(() => { element.style.transform = 'scale(1.05)' })
```

---

## 📋 Quality Checklist - MANDATORY VERIFICATION

### Before Implementing Any Component

#### Design Compliance ✅

- [ ] Follows established color palette
- [ ] Uses correct typography scale
- [ ] Implements proper spacing (Tailwind standards)
- [ ] Matches brand personality (luxury, elegant)

#### Accessibility ✅

- [ ] WCAG AA contrast compliance verified
- [ ] Keyboard navigation implemented
- [ ] Screen reader support (ARIA labels)
- [ ] Focus management working
- [ ] Semantic HTML structure

#### Responsive Design ✅

- [ ] Mobile-first implementation
- [ ] Tested on all breakpoints (375px to 1920px)
- [ ] Touch-friendly targets (44px minimum)
- [ ] Readable typography on all devices

#### Performance ✅

- [ ] Animations optimized (CSS preferred over JS)
- [ ] Images optimized and responsive
- [ ] No layout shift during loading
- [ ] Respects prefers-reduced-motion

#### Code Quality ✅

- [ ] TypeScript types defined properly
- [ ] No 'any' types used
- [ ] Event handlers follow naming convention
- [ ] Component structure follows pattern
- [ ] Error handling implemented

### Room Type Specific Checklist ✅

- [ ] Correct color scheme applied
- [ ] Room type badge displayed
- [ ] Appropriate styling for room category
- [ ] Pricing displayed with correct formatting
- [ ] Capacity and amenities shown clearly

---

## 🚫 Forbidden Practices - NEVER DO THESE

### Styling Violations

- ❌ **NEVER use inline styles** - Always use Tailwind classes
- ❌ **NEVER use custom CSS without approval** - Stick to design system
- ❌ **NEVER ignore contrast ratios** - Accessibility is mandatory
- ❌ **NEVER mix font families** - Roboto Condensed only
- ❌ **NEVER use deprecated colors** - Follow current palette

### Component Violations

- ❌ **NEVER skip TypeScript types** - Full type safety required
- ❌ **NEVER use generic event handler names** - Always prefix with 'handle'
- ❌ **NEVER ignore error states** - Handle all failure cases
- ❌ **NEVER skip loading states** - Always show user feedback
- ❌ **NEVER hardcode text** - Use proper Vietnamese labels

### Animation Violations

- ❌ **NEVER exceed 600ms duration** - Keep animations snappy
- ❌ **NEVER animate without easing** - Always use proper curves
- ❌ **NEVER ignore reduced motion** - Respect user preferences
- ❌ **NEVER stack unnecessary animations** - One animation per interaction
- ❌ **NEVER animate layout properties** - Use transform only

### Accessibility Violations

- ❌ **NEVER ignore keyboard navigation** - Tab order must be logical
- ❌ **NEVER omit alt text** - Images need descriptions
- ❌ **NEVER use color alone for meaning** - Include text/icons
- ❌ **NEVER skip focus indicators** - Visible focus required
- ❌ **NEVER use insufficient contrast** - WCAG AA minimum

---

## 🎯 Success Metrics

### Design System Adoption

- **100% component consistency** across all pages
- **Zero accessibility violations** in audit tools
- **Sub-300ms animation timing** for all interactions
- **Mobile-first responsive** implementation verified

### User Experience Goals

- **Intuitive navigation** with clear visual hierarchy
- **Fast loading** with optimized animations
- **Accessible to all users** regardless of ability
- **Consistent branding** reinforcing luxury positioning

This design language serves as the definitive guide for all UI/UX decisions in the Queen Karaoke Booking System. Every component, color choice, and interaction must align with these specifications to maintain brand consistency and user experience excellence.

**Expected Behavior**:

- Real-time filtering as user types
- Debounced search (300ms delay) for performance
- Clear visual feedback when no results found
- Consistent placeholder text across all search implementations
- Search icon positioned on the left side of input

### User Management - ADMIN FEATURES

**CRITICAL PATTERN**: SuperAdmin-only features must be clearly distinguished and secured.

```tsx
// SuperAdmin password management section
{
  canChangePassword && (
    <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
      <div className="flex items-center justify-between mb-4">
        <h4 className="font-medium text-yellow-900 flex items-center gap-2">
          <Key className="h-4 w-4" />
          Quản lý mật khẩu
        </h4>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => setShowPasswordSection(!showPasswordSection)}
          className="text-yellow-700 border-yellow-300 hover:bg-yellow-100"
        >
          {showPasswordSection ? 'Ẩn' : 'Thay đổi mật khẩu'}
        </Button>
      </div>
      {/* Password change form */}
    </div>
  );
}
```

**Permission Checks**:

- Password changes: `currentUser?.role === UserRole.SUPER_ADMIN && user.id !== currentUser?.id`
- Status changes: `usersService.canDeactivateUser(user, currentUser?.id)`
- Role changes: `usersService.canChangeRole(user, currentUser?.id, currentUser?.role)`

**Security Requirements**:

- Never allow self-modification of critical attributes
- Always validate permissions on both frontend and backend
- Provide clear visual feedback for restricted actions
- Use warning colors (yellow) for sensitive operations

## Email Templates Design

### Colors

- Background: #f9f6f0 (gold-50)
- Container Background: Linear gradient from #ffffff to #f9f6f0
- Header Background: Linear gradient from #4e3921 (gold-900) to #674e2d (gold-800)
- Header Border: #ab8d59 (queens-gold)
- Header Text: #d4af37 (bright gold)
- Content Text: #4b5563 (gray-600)
- Headings: #4e3921 (gold-900)
- Details Box Background: #ffffff
- Details Box Border: #dbc9a5 (gold-300)
- Button Background: Linear gradient from #ab8d59 (queens-gold) to #987947 (gold-600)
- Footer Background: Linear gradient from #1f2937 (gray-800) to #374151 (gray-700)
- Footer Text: #bda476 (gold-400)

### Typography

- Font Family: 'Roboto Condensed', 'Helvetica Neue', Helvetica, Arial, sans-serif
- Header: 2.25rem (36px), 700 weight, uppercase, 1.5px letter-spacing
- Content Headings: 1.5rem (24px), 600 weight
- Body Text: 1rem (16px), 400 weight
- Details Labels: 0.875rem (14px), 500 weight
- Footer Text: 0.875rem (14px)

### Layout & Spacing

- Container Max Width: 600px
- Container Margin: 40px auto
- Content Padding: 40px 30px
- Details Box Padding: 25px
- Button Padding: 16px 32px
- Footer Padding: 30px 20px

### Components

1. Container

   - Border Radius: 12px
   - Box Shadow: 0 4px 20px rgba(152, 121, 71, 0.15)
   - Border: 1px solid #e7dbc3

2. Header

   - Gradient Background
   - Bottom Border: 3px solid queens-gold
   - Text Shadow: 0 2px 4px rgba(0,0,0,0.2)

3. Booking Details Box

   - White Background
   - Border Radius: 8px
   - Box Shadow: 0 2px 10px rgba(219, 201, 165, 0.2)
   - Table Layout with Left-aligned Labels

4. Contact Info Box

   - Light Background (#fdfbf6)
   - Border Radius: 8px
   - Border: 1px solid #e7dbc3

5. Action Button
   - Gradient Background
   - Border Radius: 8px
   - Box Shadow: 0 2px 8px rgba(152, 121, 71, 0.3)
   - Uppercase Text
   - 0.5px Letter Spacing

### Responsive Design

- Mobile Breakpoint: 480px
- Adjusted Spacing and Font Sizes for Mobile
- Preserved Readability and Touch Targets

### Template Variables

- Use EJS syntax: <%- variableName %>
- Common Variables:
  - userName
  - bookingReference
  - locationName
  - roomName
  - startTime
  - endTime
  - totalPrice (confirmation only)
  - locationAddress
  - locationPhoneNumber
  - bookingDetailsLink
  - currentYear

## Brand Assets

### Logo

The primary logo for Queen Entertainment is stored in the following locations:

- Primary Path: `/apps/web/public/images/brand/logo.png`
- Usage in components: `'/images/brand/logo.png'`

Logo Guidelines:

- Maintain aspect ratio when scaling
- Minimum height: 32px
- Preferred color:rgb(194, 171, 96) (Queen Gold)
- Use PNG format for transparency
- For dark backgrounds: use the primary gold version
- For light backgrounds: use the primary gold version with 90-100% opacity

Logo Variations:

1. Primary (Full): logo.png
2. Symbol Only (Crown): logo-symbol.png
3. Wordmark Only: logo-wordmark.png

Usage Contexts:

- Header/Navigation: Full logo or symbol depending on space
- Footer: Full logo
- Favicon: Symbol only
- Mobile: Adaptable between full and symbol based on viewport
- Documents/Print: Full logo with minimum 300dpi resolution
