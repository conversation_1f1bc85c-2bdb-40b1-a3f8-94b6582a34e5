import type {
  CreateAvailabilityOverrideDto,
  UpdateAvailabilityOverrideDto,
  AvailabilityOverrideQueryDto,
  CheckRoomAvailabilityQueryDto,
  RoomAvailabilityResponseDto,
  ApiResponse,
  PaginatedResponse,
} from 'shared-types';
import type { AvailabilityOverride } from 'shared-types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export interface TimeSlot {
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
  isAvailable: boolean;
  reason?:
    | 'booked'
    | 'override_unavailable'
    | 'outside_operating_hours'
    | 'unknown';
  bookingId?: string;
  overrideId?: string;
  overrideReason?: string;
  price?: number;
}

export interface DayAvailability {
  date: string; // YYYY-MM-DD format
  timeSlots: TimeSlot[];
}

class AvailabilityService {
  private async fetchWithAuth(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<Response> {
    const token = localStorage.getItem('auth-token');

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `HTTP error! status: ${response.status}`
      );
    }

    return response;
  }

  // Check room availability for a specific time range
  async checkRoomAvailability(
    roomId: string,
    query: CheckRoomAvailabilityQueryDto
  ): Promise<RoomAvailabilityResponseDto> {
    try {
      const params = new URLSearchParams();
      params.append('startTime', query.startTime);
      params.append('endTime', query.endTime);
      if ((query as any).excludeBookingId) {
        params.append('excludeBookingId', (query as any).excludeBookingId);
      }

      const response = await this.fetchWithAuth(
        `/availability/rooms/${roomId}/check?${params.toString()}`
      );
      return await response.json();
    } catch (error) {
      console.error('Error checking room availability:', error);
      throw error;
    }
  }

  // Find alternative time slots when the requested slot is unavailable
  async findAlternativeTimeSlots(
    roomId: string,
    query: any // Using any temporarily until TypeScript resolves the export
  ): Promise<any> {
    try {
      const params = new URLSearchParams();
      params.append('startTime', query.startTime);
      params.append('endTime', query.endTime);
      if (query.searchRangeHours) {
        params.append('searchRangeHours', query.searchRangeHours.toString());
      }
      if (query.maxSuggestions) {
        params.append('maxSuggestions', query.maxSuggestions.toString());
      }
      if (query.preferredDuration !== undefined) {
        params.append('preferredDuration', query.preferredDuration.toString());
      }

      const response = await this.fetchWithAuth(
        `/availability/rooms/${roomId}/alternatives?${params.toString()}`
      );
      return await response.json();
    } catch (error) {
      console.error('Error finding alternative time slots:', error);
      throw error;
    }
  }

  // Get room availability for a specific date (returns time slots for the whole day)
  async getRoomDayAvailability(
    roomId: string,
    date: string, // YYYY-MM-DD format
    operatingHours?: { [key: string]: string }
  ): Promise<DayAvailability> {
    try {
      // Generate time slots based on operating hours or default (6 AM to 11:30 PM)
      const timeSlots = this.generateTimeSlots(date, operatingHours);

      // Check availability for each slot by querying existing bookings and overrides
      const availabilityPromises = timeSlots.map(async slot => {
        try {
          const startDateTime = `${date}T${slot.startTime}:00`;
          const endDateTime = `${date}T${slot.endTime}:00`;

          const availability = await this.checkRoomAvailability(roomId, {
            startTime: startDateTime,
            endTime: endDateTime,
          });

          return {
            ...slot,
            isAvailable: availability.isGenerallyAvailable,
            reason: availability.reason as TimeSlot['reason'],
          };
        } catch (error) {
          // If check fails, assume unavailable for safety
          return {
            ...slot,
            isAvailable: false,
            reason: 'unknown' as const,
          };
        }
      });

      const resolvedSlots = await Promise.all(availabilityPromises);

      return {
        date,
        timeSlots: resolvedSlots,
      };
    } catch (error) {
      console.error('Error getting room day availability:', error);
      throw error;
    }
  }

  // Generate time slots for a given date and operating hours
  private generateTimeSlots(
    date: string,
    operatingHours?: { [key: string]: string }
  ): Omit<TimeSlot, 'isAvailable'>[] {
    const slots: Omit<TimeSlot, 'isAvailable'>[] = [];

    // Default operating hours if not provided
    let startHour = 6;
    let startMinute = 0;
    let endHour = 23;
    let endMinute = 30;

    if (operatingHours) {
      try {
        const dayOfWeek = new Date(date).getDay();
        const dayNames = [
          'sunday',
          'monday',
          'tuesday',
          'wednesday',
          'thursday',
          'friday',
          'saturday',
        ];
        const dayName = dayNames[dayOfWeek];
        const hoursForDay = operatingHours[dayName];

        if (hoursForDay && hoursForDay.includes('-')) {
          const [openTime, closeTime] = hoursForDay.split('-');
          const [openHour, openMin] = openTime.split(':').map(Number);
          const [closeHour, closeMin] = closeTime.split(':').map(Number);

          startHour = openHour;
          startMinute = openMin;
          endHour = closeHour;
          endMinute = closeMin;
        }
      } catch (error) {
        console.warn('Error parsing operating hours, using defaults:', error);
      }
    }

    // Generate 30-minute time slots
    let currentHour = startHour;
    let currentMinute = startMinute;

    while (true) {
      const slotStartTime = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;

      // Calculate end time (30 minutes later)
      let slotEndHour = currentHour;
      let slotEndMinute = currentMinute + 30;

      if (slotEndMinute >= 60) {
        slotEndMinute = 0;
        slotEndHour++;
      }

      const slotEndTime = `${slotEndHour.toString().padStart(2, '0')}:${slotEndMinute.toString().padStart(2, '0')}`;

      slots.push({
        startTime: slotStartTime,
        endTime: slotEndTime,
      });

      // Check if we've reached the end time
      const currentTotalMinutes = currentHour * 60 + currentMinute;
      const endTotalMinutes = endHour * 60 + endMinute;

      if (currentTotalMinutes >= endTotalMinutes) {
        break;
      }

      // Move to next slot
      currentMinute += 30;
      if (currentMinute >= 60) {
        currentMinute = 0;
        currentHour++;
      }

      // Safety check to prevent infinite loops
      if (slots.length > 48) {
        break;
      }
    }

    return slots;
  }

  // Availability Override Operations
  async createOverride(
    data: CreateAvailabilityOverrideDto
  ): Promise<AvailabilityOverride> {
    try {
      const response = await this.fetchWithAuth('/availability/overrides', {
        method: 'POST',
        body: JSON.stringify(data),
      });
      return await response.json();
    } catch (error) {
      console.error('Error creating availability override:', error);
      throw error;
    }
  }

  async getOverrides(
    query: AvailabilityOverrideQueryDto = {}
  ): Promise<AvailabilityOverride[]> {
    try {
      const params = new URLSearchParams();

      if (query.roomId) params.append('roomId', query.roomId);
      if (query.isAvailable !== undefined)
        params.append('isAvailable', query.isAvailable.toString());
      if (query.dateRange?.startDate)
        params.append('startDate', query.dateRange.startDate);
      if (query.dateRange?.endDate)
        params.append('endDate', query.dateRange.endDate);
      if (query.page) params.append('page', query.page.toString());
      if (query.limit) params.append('limit', query.limit.toString());

      const response = await this.fetchWithAuth(
        `/availability/overrides?${params.toString()}`
      );
      return await response.json();
    } catch (error) {
      console.error('Error fetching availability overrides:', error);
      throw error;
    }
  }

  async updateOverride(
    id: string,
    data: UpdateAvailabilityOverrideDto
  ): Promise<AvailabilityOverride> {
    try {
      const response = await this.fetchWithAuth(
        `/availability/overrides/${id}`,
        {
          method: 'PATCH',
          body: JSON.stringify(data),
        }
      );
      return await response.json();
    } catch (error) {
      console.error('Error updating availability override:', error);
      throw error;
    }
  }

  async deleteOverride(id: string): Promise<AvailabilityOverride> {
    try {
      const response = await this.fetchWithAuth(
        `/availability/overrides/${id}`,
        {
          method: 'DELETE',
        }
      );
      return await response.json();
    } catch (error) {
      console.error('Error deleting availability override:', error);
      throw error;
    }
  }

  // Quick block/unblock methods for easy admin actions
  async quickBlockTimeSlot(
    roomId: string,
    date: string,
    startTime: string,
    endTime: string,
    reason?: string
  ): Promise<AvailabilityOverride> {
    const startDateTime = `${date}T${startTime}:00`;
    const endDateTime = `${date}T${endTime}:00`;

    return this.createOverride({
      roomId,
      date,
      startTime: startDateTime,
      endTime: endDateTime,
      isAvailable: false,
      reason: reason || 'Blocked by admin',
    });
  }

  async quickUnblockTimeSlot(
    overrideId: string
  ): Promise<AvailabilityOverride> {
    return this.deleteOverride(overrideId);
  }

  // Utility methods
  formatTimeSlot(startTime: string, endTime: string): string {
    return `${startTime} - ${endTime}`;
  }

  getSlotStatusColor(slot: TimeSlot): string {
    if (!slot.isAvailable) {
      if (slot.reason === 'booked') {
        return 'bg-red-100 border-red-300 text-red-800';
      } else if (slot.reason === 'override_unavailable') {
        return 'bg-orange-100 border-orange-300 text-orange-800';
      } else {
        return 'bg-gray-100 border-gray-300 text-gray-600';
      }
    }
    return 'bg-green-100 border-green-300 text-green-800';
  }

  getSlotStatusLabel(slot: TimeSlot): string {
    if (!slot.isAvailable) {
      if (slot.reason === 'booked') {
        return 'Đã đặt';
      } else if (slot.reason === 'override_unavailable') {
        return 'Bị chặn';
      } else {
        return 'Không khả dụng';
      }
    }
    return 'Có sẵn';
  }

  // Get overrides for a specific room and date range
  async getOverridesForRoomAndDate(
    roomId: string,
    startDate: string,
    endDate?: string
  ): Promise<AvailabilityOverride[]> {
    try {
      const query: AvailabilityOverrideQueryDto = {
        roomId,
        dateRange: {
          startDate,
          endDate: endDate || startDate,
        },
      };

      return await this.getOverrides(query);
    } catch (error) {
      console.error('Error getting overrides for room and date:', error);
      throw error;
    }
  }

  // Enhanced method to get room availability with override details
  async getRoomDayAvailabilityWithOverrides(
    roomId: string,
    date: string, // YYYY-MM-DD format
    operatingHours?: { [key: string]: string }
  ): Promise<DayAvailability> {
    try {
      // Generate time slots based on operating hours or default (6 AM to 11:30 PM)
      const timeSlots = this.generateTimeSlots(date, operatingHours);

      // Get overrides for this room and date
      const overrides = await this.getOverridesForRoomAndDate(roomId, date);

      // Check availability for each slot and match with override data
      const availabilityPromises = timeSlots.map(async slot => {
        try {
          const startDateTime = `${date}T${slot.startTime}:00`;
          const endDateTime = `${date}T${slot.endTime}:00`;

          const availability = await this.checkRoomAvailability(roomId, {
            startTime: startDateTime,
            endTime: endDateTime,
          });

          // Find matching override for this time slot
          const matchingOverride = overrides.find(override => {
            const overrideStart = new Date(override.startTime);
            const overrideEnd = new Date(override.endTime);
            const slotStart = new Date(startDateTime);
            const slotEnd = new Date(endDateTime);

            // Check if slot overlaps with override
            return (
              (slotStart >= overrideStart && slotStart < overrideEnd) ||
              (slotEnd > overrideStart && slotEnd <= overrideEnd) ||
              (slotStart <= overrideStart && slotEnd >= overrideEnd)
            );
          });

          // Determine the correct reason based on availability and overrides
          let slotReason: TimeSlot['reason'] =
            availability.reason as TimeSlot['reason'];

          // If there's a matching override that blocks this slot, set reason to 'override_unavailable'
          if (matchingOverride && !matchingOverride.isAvailable) {
            slotReason = 'override_unavailable';
          }
          // If slot is unavailable and reason contains "override" or "blocking", it's likely an override
          else if (
            !availability.isGenerallyAvailable &&
            availability.reason &&
            (availability.reason.toLowerCase().includes('override') ||
              availability.reason.toLowerCase().includes('unavailable due to'))
          ) {
            slotReason = 'override_unavailable';
          }
          // If slot is unavailable and reason mentions booking, it's booked
          else if (
            !availability.isGenerallyAvailable &&
            availability.reason &&
            availability.reason.toLowerCase().includes('booked')
          ) {
            slotReason = 'booked';
          }
          // If slot is unavailable and reason mentions operating hours, it's outside hours
          else if (
            !availability.isGenerallyAvailable &&
            availability.reason &&
            (availability.reason.toLowerCase().includes('operating hours') ||
              availability.reason.toLowerCase().includes('not open'))
          ) {
            slotReason = 'outside_operating_hours';
          }

          return {
            ...slot,
            isAvailable: availability.isGenerallyAvailable,
            reason: slotReason,
            overrideId: matchingOverride?.id,
            overrideReason: matchingOverride?.reason,
          };
        } catch (error) {
          // If check fails, assume unavailable for safety
          return {
            ...slot,
            isAvailable: false,
            reason: 'unknown' as const,
          };
        }
      });

      const resolvedSlots = await Promise.all(availabilityPromises);

      return {
        date,
        timeSlots: resolvedSlots,
      };
    } catch (error) {
      console.error(
        'Error getting room day availability with overrides:',
        error
      );
      throw error;
    }
  }
}

export const availabilityService = new AvailabilityService();
