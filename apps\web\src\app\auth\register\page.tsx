'use client';

import React, { useState, useCallback } from 'react';
// import { useRouter } from 'next/navigation'; // No longer used
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../components/ui/card';
import { useAuthStore } from '../../../stores/auth-store';
import { authService } from '../../../services/auth-service';
import { Mail, Lock, User, Crown, RefreshCw } from 'lucide-react';
// import { UserRole } from 'shared-types'; // No longer used

// Animation variants following the guidelines
const containerVariants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08, // 80ms stagger between child elements
      duration: 0.3, // 300ms for container fade in
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  show: {
    opacity: 1,
    y: 0,
    transition: {
      type: 'spring',
      stiffness: 200, // Medium spring stiffness
      damping: 20, // Medium damping
      duration: 0.4, // 400ms for individual elements
    },
  },
};

const formControlVariants = {
  hidden: { opacity: 0, x: -20 },
  show: {
    opacity: 1,
    x: 0,
    transition: {
      type: 'spring',
      stiffness: 150, // Light spring stiffness
      damping: 15, // Light damping
      duration: 0.3, // 300ms for form controls
    },
  },
};

const successVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  show: {
    opacity: 1,
    scale: 1,
    transition: {
      type: 'spring',
      stiffness: 200,
      damping: 20,
    },
  },
};

// Define ApiError interface locally if not centrally available
interface ApiError extends Error {
  response?: {
    data?: {
      message?: string;
    };
  };
}

export default function RegisterPage() {
  // const router = useRouter(); // No longer used
  const { setLoading, setError, isLoading, error } = useAuthStore(); // setAuth removed

  // Move all state hooks to the top level
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
  });

  const [formErrors, setFormErrors] = useState<{
    name?: string;
    email?: string;
    password?: string;
    confirmPassword?: string;
  }>({});

  const [registrationSuccess, setRegistrationSuccess] =
    useState<boolean>(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [resendStatus, setResendStatus] = useState<
    'idle' | 'loading' | 'success' | 'error'
  >('idle');
  const [resendError, setResendError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors(prev => ({ ...prev, [name]: undefined }));
    }

    if (error) {
      setError(null);
    }
  };

  const validateForm = () => {
    const errors: typeof formErrors = {};
    if (!formData.name) errors.name = 'Vui lòng nhập tên.';
    else if (formData.name.length < 2)
      errors.name = 'Tên phải có ít nhất 2 ký tự.';

    if (!formData.email) errors.email = 'Vui lòng nhập email.';
    else if (!/\S+@\S+\.\S+/.test(formData.email))
      errors.email = 'Vui lòng nhập địa chỉ email hợp lệ.';

    if (!formData.password) errors.password = 'Vui lòng nhập mật khẩu.';
    else if (formData.password.length < 8)
      errors.password = 'Mật khẩu phải có ít nhất 8 ký tự.';

    if (!formData.confirmPassword)
      errors.confirmPassword = 'Vui lòng xác nhận mật khẩu.';
    else if (formData.password !== formData.confirmPassword)
      errors.confirmPassword = 'Mật khẩu không khớp.';

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setLoading(true);
    setError(null);
    setRegistrationSuccess(false);

    try {
      await authService.register({
        name: formData.name,
        email: formData.email,
        password: formData.password,
      });
      setRegistrationSuccess(true);
    } catch (err: unknown) {
      const apiError = err as ApiError;
      const errorMessage =
        apiError.response?.data?.message ||
        apiError.message ||
        'Đăng ký thất bại. Vui lòng thử lại.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleResendEmail = useCallback(async () => {
    if (resendCooldown > 0) return;

    setResendStatus('loading');
    setResendError(null);

    try {
      await authService.resendVerificationEmail(formData.email);
      setResendStatus('success');
      setResendCooldown(60); // 60 seconds cooldown

      // Start countdown
      const interval = setInterval(() => {
        setResendCooldown(prev => {
          if (prev <= 1) {
            clearInterval(interval);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (err) {
      const apiError = err as ApiError;
      setResendError(
        apiError.response?.data?.message ||
          apiError.message ||
          'Không thể gửi lại email. Vui lòng thử lại sau.'
      );
      setResendStatus('error');
    }
  }, [formData.email, resendCooldown]);

  if (registrationSuccess) {
    return (
      <motion.div
        className="min-h-screen bg-gradient-to-br from-gold-50 via-white to-gold-50 flex items-center justify-center p-4"
        initial="hidden"
        animate="show"
        variants={containerVariants}
      >
        <Card className="w-full max-w-md shadow-xl border-gold-200">
          <CardHeader className="text-center space-y-2 pb-2">
            <motion.div
              className="flex justify-center mb-6"
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Mail className="w-8 h-8 text-white" />
              </div>
            </motion.div>
            <CardTitle className="text-3xl font-bold bg-gradient-to-r from-green-700 to-green-600 bg-clip-text text-transparent">
              Đăng ký Thành Công!
            </CardTitle>
          </CardHeader>

          <CardContent className="space-y-6">
            <motion.div
              className="text-center space-y-4"
              variants={itemVariants}
            >
              <motion.p className="text-gray-700" variants={itemVariants}>
                Cảm ơn bạn đã đăng ký. Một email xác thực đã được gửi đến{' '}
                <strong className="text-gold-600">{formData.email}</strong>.
              </motion.p>
              <motion.p className="text-gray-600" variants={itemVariants}>
                Vui lòng kiểm tra hộp thư đến (và thư mục spam) của bạn và nhấp
                vào liên kết xác thực để kích hoạt tài khoản.
              </motion.p>

              <motion.div className="pt-4" variants={itemVariants}>
                <Button
                  onClick={handleResendEmail}
                  disabled={resendCooldown > 0 || resendStatus === 'loading'}
                  className={`w-full bg-gradient-to-r mb-4 ${
                    resendStatus === 'error'
                      ? 'from-red-600 to-red-500 hover:from-red-600/90 hover:to-red-500/90'
                      : 'from-gold-600 to-gold-500 hover:from-gold-600/90 hover:to-gold-500/90'
                  } text-white font-semibold py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 relative ${
                    resendCooldown > 0 || resendStatus === 'loading'
                      ? 'opacity-80 cursor-not-allowed'
                      : ''
                  }`}
                >
                  {resendStatus === 'loading' ? (
                    <motion.div
                      className="flex items-center justify-center"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                    >
                      <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
                      <span>Đang gửi...</span>
                    </motion.div>
                  ) : resendCooldown > 0 ? (
                    `Gửi lại sau ${resendCooldown}s`
                  ) : (
                    'Gửi lại email xác thực'
                  )}
                </Button>

                {resendStatus === 'success' && (
                  <motion.p
                    className="text-green-600 text-sm"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    Email xác thực đã được gửi lại thành công!
                  </motion.p>
                )}

                {resendError && (
                  <motion.p
                    className="text-red-600 text-sm"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    {resendError}
                  </motion.p>
                )}

                <Button
                  asChild
                  variant="outline"
                  className="w-full mt-4 border-gold-200 hover:bg-gold-50 text-gold-700"
                >
                  <Link href="/auth/login">Đến trang Đăng nhập</Link>
                </Button>
              </motion.div>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <div className="min-h-screen flex">
      {/* Left Panel - Cover Image */}
      <motion.div
        className="hidden lg:block lg:w-1/2 relative overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{
          duration: 0.6,
          delay: 0.2,
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/50 to-black/60 z-10" />
        <motion.div
          className="absolute inset-0 bg-cover bg-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 0.3 }}
          style={{
            backgroundImage: "url('/images/auth/register-cover.jpg')",
          }}
        />
        <motion.div
          className="absolute inset-0 flex items-center justify-center z-20"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="text-center p-8 max-w-2xl mx-auto">
            <motion.div
              className="space-y-6"
              variants={containerVariants}
              initial="hidden"
              animate="show"
            >
              <motion.h1
                className="text-5xl font-bold mb-4 text-white drop-shadow-[0_2px_2px_rgba(0,0,0,0.8)] font-serif"
                variants={itemVariants}
              >
                Queen Karaoke
              </motion.h1>
              <motion.div
                className="w-24 h-1 bg-gradient-to-r from-gold-400 to-gold-600 mx-auto rounded-full shadow-lg"
                variants={itemVariants}
              />
              <motion.p
                className="text-2xl text-gold-100 drop-shadow-[0_2px_2px_rgba(0,0,0,0.8)] font-light tracking-wide"
                variants={itemVariants}
              >
                Tham gia cùng chúng tôi để trải nghiệm dịch vụ hoàng gia
              </motion.p>
            </motion.div>
          </div>
        </motion.div>
      </motion.div>

      {/* Right Panel - Registration Form */}
      <motion.div
        className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-gradient-to-br from-gold-50 via-white to-gold-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          className="w-full max-w-md space-y-8"
          variants={containerVariants}
          initial="hidden"
          animate="show"
        >
          <motion.div className="text-center" variants={itemVariants}>
            <motion.div
              className="flex justify-center mb-6"
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <div className="w-16 h-16 bg-gradient-to-br from-gold-500 to-gold-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Crown className="w-8 h-8 text-white" />
              </div>
            </motion.div>
            <motion.h2
              className="text-3xl font-bold bg-gradient-to-r from-gold-700 to-gold-600 bg-clip-text text-transparent"
              variants={itemVariants}
            >
              Tham gia Queen Karaoke
            </motion.h2>
            <motion.p className="mt-2 text-gray-600" variants={itemVariants}>
              Tạo tài khoản để bắt đầu đặt phòng karaoke sang trọng
            </motion.p>
          </motion.div>

          <motion.form
            onSubmit={handleSubmit}
            className="space-y-6"
            variants={containerVariants}
          >
            {error && (
              <motion.div
                className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-600 text-sm"
                variants={itemVariants}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
              >
                {error}
              </motion.div>
            )}

            <motion.div className="space-y-2" variants={formControlVariants}>
              <Label htmlFor="name" className="flex items-center gap-2">
                <User className="w-4 h-4 text-gold-600" />
                Họ và Tên
              </Label>
              <Input
                id="name"
                name="name"
                type="text"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Nhập họ và tên của bạn"
                className={
                  formErrors.name
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                    : ''
                }
                disabled={isLoading}
              />
              {formErrors.name && (
                <motion.p
                  className="text-red-600 text-sm mt-1"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  {formErrors.name}
                </motion.p>
              )}
            </motion.div>

            <motion.div className="space-y-2" variants={formControlVariants}>
              <Label htmlFor="email" className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-gold-600" />
                Địa chỉ Email
              </Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Nhập email của bạn"
                className={
                  formErrors.email
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                    : ''
                }
                disabled={isLoading}
              />
              {formErrors.email && (
                <motion.p
                  className="text-red-600 text-sm mt-1"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  {formErrors.email}
                </motion.p>
              )}
            </motion.div>

            <motion.div className="space-y-2" variants={formControlVariants}>
              <Label htmlFor="password" className="flex items-center gap-2">
                <Lock className="w-4 h-4 text-gold-600" />
                Mật khẩu
              </Label>
              <Input
                id="password"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="Nhập mật khẩu của bạn"
                className={
                  formErrors.password
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                    : ''
                }
                disabled={isLoading}
              />
              {formErrors.password && (
                <motion.p
                  className="text-red-600 text-sm mt-1"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  {formErrors.password}
                </motion.p>
              )}
            </motion.div>

            <motion.div className="space-y-2" variants={formControlVariants}>
              <Label
                htmlFor="confirmPassword"
                className="flex items-center gap-2"
              >
                <Lock className="w-4 h-4 text-gold-600" />
                Xác nhận Mật khẩu
              </Label>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                placeholder="Nhập lại mật khẩu của bạn"
                className={
                  formErrors.confirmPassword
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                    : ''
                }
                disabled={isLoading}
              />
              {formErrors.confirmPassword && (
                <motion.p
                  className="text-red-600 text-sm mt-1"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  {formErrors.confirmPassword}
                </motion.p>
              )}
            </motion.div>

            <motion.div variants={itemVariants}>
              <Button
                type="submit"
                className={`w-full bg-gradient-to-r from-queens-gold to-gold-600 hover:from-queens-gold/90 hover:to-gold-600/90 text-white font-semibold py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 relative ${
                  isLoading ? 'cursor-not-allowed opacity-80' : ''
                }`}
                disabled={isLoading}
              >
                {isLoading ? (
                  <motion.div
                    className="flex items-center justify-center"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      />
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      />
                    </svg>
                    <span>Đang đăng ký...</span>
                  </motion.div>
                ) : (
                  <span>Đăng ký</span>
                )}
              </Button>
            </motion.div>

            <motion.div
              className="text-center text-sm text-gray-600"
              variants={itemVariants}
            >
              Đã có tài khoản?{' '}
              <Link
                href="/auth/login"
                className="text-gold-600 hover:text-gold-700 font-semibold"
              >
                Đăng nhập ngay
              </Link>
            </motion.div>
          </motion.form>
        </motion.div>
      </motion.div>
    </div>
  );
}
