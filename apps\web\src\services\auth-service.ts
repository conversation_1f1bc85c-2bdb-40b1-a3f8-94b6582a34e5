'use client';

import { LoginDto, RegisterDto, AuthResponseDto, User } from 'shared-types';

class AuthService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
  }

  private getAuthToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('auth-token');
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(url, config);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `HTTP ${response.status}: ${response.statusText}`
      );
    }

    return response.json();
  }

  private async authenticatedRequest<T>(
    endpoint: string,
    token?: string,
    options: RequestInit = {}
  ): Promise<T> {
    const authToken = token || this.getAuthToken();
    return this.request<T>(endpoint, {
      ...options,
      headers: {
        ...(authToken && { Authorization: `Bearer ${authToken}` }),
        ...options.headers,
      },
    });
  }

  async login(credentials: LoginDto): Promise<AuthResponseDto> {
    return this.request<AuthResponseDto>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async register(userData: RegisterDto): Promise<AuthResponseDto> {
    return this.request<AuthResponseDto>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async adminLogin(credentials: LoginDto): Promise<AuthResponseDto> {
    return this.request<AuthResponseDto>('/auth/admin/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async getCurrentUser(token?: string): Promise<User> {
    return this.authenticatedRequest<User>('/auth/me', token, {
      method: 'GET',
    });
  }

  async refreshToken(token?: string): Promise<{ accessToken: string }> {
    return this.authenticatedRequest('/auth/refresh', token, {
      method: 'POST',
    });
  }

  async logout(): Promise<void> {
    // If you have a logout endpoint on the server
    try {
      await this.authenticatedRequest('/auth/logout', undefined, {
        method: 'POST',
      });
    } catch (error) {
      // Log the error but don't throw it since logout should always succeed locally
      console.warn('Server logout failed:', error);
    }
  }

  async verifyEmail(token: string): Promise<{ message: string }> {
    return this.request<{ message: string }>(
      `/auth/verify-email?token=${token}`,
      { method: 'GET' }
    );
  }

  async resendVerificationEmail(email: string): Promise<{ message: string }> {
    return this.request<{ message: string }>(
      '/auth/public/resend-verification-email',
      {
        method: 'POST',
        body: JSON.stringify({ email }),
      }
    );
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
