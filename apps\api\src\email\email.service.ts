import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import * as fs from 'fs/promises';
import * as path from 'path';

interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

@Injectable()
export class EmailService implements OnModuleInit {
  private transporter: nodemailer.Transporter | undefined;
  private readonly logger = new Logger(EmailService.name);
  private devEmailRecipient: string | undefined;
  private isInitialized = false;

  constructor(private configService: ConfigService) {
    this.devEmailRecipient = this.configService.get<string>(
      'DEV_EMAIL_RECIPIENT',
    );
  }

  async onModuleInit() {
    await this.initializeTransporter();
  }

  private async initializeTransporter() {
    const emailHost = this.configService.get<string>('EMAIL_HOST');
    let emailPort = this.configService.get<number>('EMAIL_PORT');
    let emailSecure = this.configService.get<boolean>('EMAIL_SECURE');
    let emailUser = this.configService.get<string>('EMAIL_USER');
    let emailPass = this.configService.get<string>('EMAIL_PASS');

    let tlsConfig: Record<string, any> = {
      rejectUnauthorized: process.env.NODE_ENV === 'production',
    };

    if (emailHost === 'smtp.ethereal.email') {
      this.logger.log(
        'Ethereal Email detected. Applying specific configuration.',
      );
      emailPort = 587;
      emailSecure = false;
      tlsConfig = { rejectUnauthorized: false };

      if (
        !emailUser ||
        !emailPass ||
        emailUser.includes('your_ethereal_user')
      ) {
        try {
          this.logger.log('Attempting to create new Ethereal test account...');
          const testAccount = await nodemailer.createTestAccount();
          emailUser = testAccount.user;
          emailPass = testAccount.pass;
          this.logger.log(
            `Ethereal (auto-created): User - ${emailUser}, Pass - (see logs for password if needed)`,
          );
          this.logger.debug(
            `Ethereal Full Credentials: User - ${emailUser}, Pass - ${emailPass}`,
          );
          this.logger.log(
            'Ethereal preview URL: https://ethereal.email/messages',
          );
        } catch (error) {
          this.logger.error('Failed to create Ethereal test account:', error);
          return;
        }
      } else {
        this.logger.log(
          `Using provided Ethereal credentials for User: ${emailUser}`,
        );
      }
    }

    if (!emailHost || !emailPort || !emailUser || !emailPass) {
      this.logger.error('Email config incomplete. Sending disabled.');
      return;
    }

    try {
      const transportOptions = {
        host: emailHost,
        port: emailPort,
        secure: emailSecure,
        auth: {
          user: emailUser,
          pass: emailPass,
        },
        tls: tlsConfig,
        requireTLS: !emailSecure,
      };
      this.transporter = nodemailer.createTransport(transportOptions as any);

      if (emailHost !== 'smtp.ethereal.email') {
        await this.transporter.verify();
        this.logger.log('Email server connection verified.');
      }
      this.isInitialized = true;
    } catch (error) {
      this.logger.error('Email transporter init failed:', error);
      this.transporter = undefined;
    }
  }

  async sendMail(options: EmailOptions): Promise<void> {
    if (!this.isInitialized || !this.transporter) {
      this.logger.error('Email service not ready. Skipping send.');
      return;
    }

    const mailFrom = `"${this.configService.get<string>('EMAIL_FROM_NAME')}" <${this.configService.get<string>('EMAIL_FROM_ADDRESS')}>\``;
    const recipient = this.devEmailRecipient || options.to;

    const mailOptions = {
      from: mailFrom,
      to: recipient,
      subject: options.subject,
      html: options.html,
      text:
        options.text || 'Please view this email in an HTML-compatible client.',
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      this.logger.log(`Message sent: ${info.messageId}`);
      if (
        this.configService.get<string>('EMAIL_HOST') === 'smtp.ethereal.email'
      ) {
        const previewUrl = nodemailer.getTestMessageUrl(info);
        this.logger.log(`Ethereal preview URL: ${previewUrl}`);
      } else if (this.devEmailRecipient) {
        this.logger.log(
          `Email sent to DEV_EMAIL_RECIPIENT: ${this.devEmailRecipient}`,
        );
      }
    } catch (error) {
      this.logger.error(`Error sending email to ${recipient}:`, error);
      throw error; // Rethrow to allow calling service to handle it
    }
  }

  async renderTemplate(
    templateName: string, // e.g., 'booking-confirmation'
    language: 'vi' | 'en',
    data: Record<string, any>,
  ): Promise<string> {
    const templateFileName = `${templateName}.${language}.html`;
    // Based on error logs, templates are in project/apps/api/dist/templates
    // __dirname for this service is project/apps/api/dist/email
    // So, go up one level from 'email' to 'dist', then into 'templates'
    const templatePath = path.join(
      __dirname,
      'email/templates',
      templateFileName,
    );

    try {
      let htmlContent = await fs.readFile(templatePath, 'utf-8');
      for (const key in data) {
        const regex = new RegExp(`{{${key}}}`, 'g');
        htmlContent = htmlContent.replace(regex, String(data[key])); // Ensure data is string
      }
      return htmlContent;
    } catch (error) {
      this.logger.error(
        `Error rendering email template ${templatePath}:`,
        error,
      );
      throw new Error(`Could not render email template: ${templateFileName}`);
    }
  }
}
