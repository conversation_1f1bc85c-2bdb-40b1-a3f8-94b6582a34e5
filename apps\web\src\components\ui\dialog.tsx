'use client';

import React from 'react';
import {
  AlertTriangle,
  CheckCircle,
  Info,
  XCircle,
  HelpCircle,
} from 'lucide-react';
import { <PERSON><PERSON>, <PERSON>dalHeader, ModalBody, ModalFooter } from './modal';
import { Button } from './button';

export type AlertType = 'success' | 'error' | 'warning' | 'info';

interface AlertDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string;
  type?: AlertType;
  confirmText?: string;
}

interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'danger';
}

const getAlertIcon = (type: AlertType) => {
  const iconClasses = 'h-6 w-6 mr-3 flex-shrink-0';

  switch (type) {
    case 'success':
      return <CheckCircle className={`${iconClasses} text-green-600`} />;
    case 'error':
      return <XCircle className={`${iconClasses} text-red-600`} />;
    case 'warning':
      return <AlertTriangle className={`${iconClasses} text-yellow-600`} />;
    case 'info':
    default:
      return <Info className={`${iconClasses} text-blue-600`} />;
  }
};

const getAlertColors = (type: AlertType) => {
  switch (type) {
    case 'success':
      return {
        bg: 'bg-green-50',
        border: 'border-green-200',
        title: 'text-green-800',
        message: 'text-green-700',
      };
    case 'error':
      return {
        bg: 'bg-red-50',
        border: 'border-red-200',
        title: 'text-red-800',
        message: 'text-red-700',
      };
    case 'warning':
      return {
        bg: 'bg-yellow-50',
        border: 'border-yellow-200',
        title: 'text-yellow-800',
        message: 'text-yellow-700',
      };
    case 'info':
    default:
      return {
        bg: 'bg-blue-50',
        border: 'border-blue-200',
        title: 'text-blue-800',
        message: 'text-blue-700',
      };
  }
};

export const AlertDialog: React.FC<AlertDialogProps> = ({
  isOpen,
  onClose,
  title,
  message,
  type = 'info',
  confirmText = 'OK',
}) => {
  const colors = getAlertColors(type);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="sm"
      closeOnOverlayClick={false}
    >
      <ModalHeader showCloseButton={false}>
        <div className="flex items-center">
          {getAlertIcon(type)}
          <h2
            id="modal-title"
            className={`text-h5 font-semibold ${colors.title}`}
          >
            {title}
          </h2>
        </div>
      </ModalHeader>

      <ModalBody
        className={`${colors.bg} ${colors.border} border mx-6 rounded-lg`}
      >
        <p className={`text-base ${colors.message} whitespace-pre-line`}>
          {message}
        </p>
      </ModalBody>

      <ModalFooter>
        <Button
          onClick={onClose}
          className="bg-queens-gold hover:bg-queens-gold/90 text-white font-medium shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200 px-6"
        >
          {confirmText}
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Xác nhận',
  cancelText = 'Hủy bỏ',
  variant = 'default',
}) => {
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  const isDanger = variant === 'danger';

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="sm"
      closeOnOverlayClick={false}
    >
      <ModalHeader showCloseButton={false}>
        <div className="flex items-center">
          <HelpCircle
            className={`h-6 w-6 mr-3 flex-shrink-0 ${isDanger ? 'text-red-600' : 'text-blue-600'}`}
          />
          <h2
            id="modal-title"
            className={`text-h5 font-semibold ${isDanger ? 'text-red-800' : 'text-gray-900'}`}
          >
            {title}
          </h2>
        </div>
      </ModalHeader>

      <ModalBody
        className={`${isDanger ? 'bg-red-50 border-red-200' : 'bg-blue-50 border-blue-200'} border mx-6 rounded-lg`}
      >
        <p
          className={`text-base ${isDanger ? 'text-red-700' : 'text-blue-700'} whitespace-pre-line`}
        >
          {message}
        </p>
      </ModalBody>

      <ModalFooter>
        <Button onClick={onClose} variant="outline" className="px-6">
          {cancelText}
        </Button>
        <Button
          onClick={handleConfirm}
          variant={isDanger ? 'delete' : 'default'}
          className={`px-6 ${!isDanger ? 'bg-queens-gold hover:bg-queens-gold/90 text-white font-medium shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200' : ''}`}
        >
          {confirmText}
        </Button>
      </ModalFooter>
    </Modal>
  );
};
