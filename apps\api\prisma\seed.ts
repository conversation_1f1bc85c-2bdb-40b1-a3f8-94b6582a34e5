import { PrismaClient } from '../generated/prisma';
import * as bcrypt from 'bcrypt';
import { UserRole } from 'shared-types';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create Super Admin
  const superAdminPassword = await bcrypt.hash('SuperAdmin123!', 10);
  const superAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: superAdminPassword,
      name: 'Super Administrator',
      phoneNumber: '0123456789',
      role: UserRole.SUPER_ADMIN,
      isActive: true,
      emailVerified: true,
    },
  });

  // Create Admin
  const adminPassword = await bcrypt.hash('Admin123!', 10);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: adminPassword,
      name: 'Administrator',
      phoneNumber: '0987654321',
      role: UserRole.ADMIN,
      isActive: true,
      emailVerified: true,
    },
  });

  // Create Test Customer
  const customerPassword = await bcrypt.hash('Customer123!', 10);
  const customer = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: customerPassword,
      name: 'Test Customer',
      phoneNumber: '0111222333',
      role: UserRole.CUSTOMER,
      isActive: true,
      emailVerified: true,
    },
  });

  // Create Sample Locations
  const location1 = await prisma.location.create({
    data: {
      name: {
        vi: 'Queen Karaoke Quảng Phú',
        en: 'Queen Karaoke Quang Phu',
      },
      address: '123 Đường Quảng Phú, Thành phố Pleiku, Gia Lai',
      description: {
        vi: 'Chi nhánh chính tại Quảng Phú với không gian sang trọng và hiện đại',
        en: 'Main branch in Quang Phu with luxurious and modern space',
      },
      phoneNumber: '0269 123 4567',
      email: '<EMAIL>',
      operatingHours: {
        monday: '18:00-02:00',
        tuesday: '18:00-02:00',
        wednesday: '18:00-02:00',
        thursday: '18:00-02:00',
        friday: '18:00-03:00',
        saturday: '18:00-03:00',
        sunday: '18:00-02:00',
      },
      isActive: true,
    },
  });

  const location2 = await prisma.location.create({
    data: {
      name: {
        vi: 'Queen Karaoke Buôn Ma Thuột',
        en: 'Queen Karaoke Buon Ma Thuot',
      },
      address: '456 Đường Y Jút, Thành phố Buôn Ma Thuột, Đắk Lắk',
      description: {
        vi: 'Chi nhánh tại Buôn Ma Thuột với phong cách độc đáo',
        en: 'Branch in Buon Ma Thuot with unique style',
      },
      phoneNumber: '0262 987 6543',
      email: '<EMAIL>',
      operatingHours: {
        monday: '19:00-01:00',
        tuesday: '19:00-01:00',
        wednesday: '19:00-01:00',
        thursday: '19:00-01:00',
        friday: '19:00-02:00',
        saturday: '19:00-02:00',
        sunday: '19:00-01:00',
      },
      isActive: true,
    },
  });

  // Create Sample Rooms for Location 1
  const room1 = await prisma.room.create({
    data: {
      locationId: location1.id,
      name: {
        vi: 'Phòng VIP Hoàng Gia',
        en: 'Royal VIP Room',
      },
      theme: {
        vi: 'Phong cách hoàng gia cổ điển',
        en: 'Classic royal style',
      },
      description: {
        vi: 'Phòng VIP cao cấp với thiết kế hoàng gia, âm thanh chất lượng cao',
        en: 'Premium VIP room with royal design and high-quality sound system',
      },
      capacity: 15,
      pricePerHour: 500000,
      amenities: [
        { vi: 'Hệ thống âm thanh cao cấp', en: 'Premium sound system' },
        { vi: 'Màn hình LED 75 inch', en: '75-inch LED screen' },
        { vi: 'Sofa da thật', en: 'Genuine leather sofa' },
        { vi: 'Minibar', en: 'Minibar' },
        { vi: 'Điều hòa riêng', en: 'Private air conditioning' },
      ],
      images: [
        'https://example.com/room1-1.jpg',
        'https://example.com/room1-2.jpg',
      ],
      isActive: true,
    },
  });

  const room2 = await prisma.room.create({
    data: {
      locationId: location1.id,
      name: {
        vi: 'Phòng Standard Hiện Đại',
        en: 'Modern Standard Room',
      },
      theme: {
        vi: 'Phong cách hiện đại trẻ trung',
        en: 'Modern youthful style',
      },
      description: {
        vi: 'Phòng tiêu chuẩn với thiết kế hiện đại, phù hợp cho nhóm bạn',
        en: 'Standard room with modern design, suitable for groups of friends',
      },
      capacity: 8,
      pricePerHour: 200000,
      amenities: [
        { vi: 'Hệ thống âm thanh chuẩn', en: 'Standard sound system' },
        { vi: 'Màn hình LED 55 inch', en: '55-inch LED screen' },
        { vi: 'Ghế sofa thoải mái', en: 'Comfortable sofa' },
        { vi: 'Đồ uống miễn phí', en: 'Free beverages' },
      ],
      images: [
        'https://example.com/room2-1.jpg',
        'https://example.com/room2-2.jpg',
      ],
      isActive: true,
    },
  });

  // Create Sample Rooms for Location 2
  const room3 = await prisma.room.create({
    data: {
      locationId: location2.id,
      name: {
        vi: 'Phòng Deluxe Cà Phê',
        en: 'Coffee Deluxe Room',
      },
      theme: {
        vi: 'Phong cách cà phê Tây Nguyên',
        en: 'Central Highlands coffee style',
      },
      description: {
        vi: 'Phòng deluxe với chủ đề cà phê đặc trưng Tây Nguyên',
        en: 'Deluxe room with Central Highlands coffee theme',
      },
      capacity: 12,
      pricePerHour: 350000,
      amenities: [
        { vi: 'Hệ thống âm thanh chất lượng', en: 'Quality sound system' },
        { vi: 'Màn hình LED 65 inch', en: '65-inch LED screen' },
        { vi: 'Không gian cà phê', en: 'Coffee space' },
        { vi: 'Cà phê đặc sản miễn phí', en: 'Free specialty coffee' },
      ],
      images: [
        'https://example.com/room3-1.jpg',
        'https://example.com/room3-2.jpg',
      ],
      isActive: true,
    },
  });

  console.log('✅ Database seeding completed successfully!');
  console.log('\n📋 Created accounts:');
  console.log(`🔑 Super Admin: ${superAdmin.email} / SuperAdmin123!`);
  console.log(`🔑 Admin: ${admin.email} / Admin123!`);
  console.log(`🔑 Customer: ${customer.email} / Customer123!`);
  console.log('\n🏢 Created locations:');
  console.log(`📍 ${(location1.name as any).vi} (${location1.id})`);
  console.log(`📍 ${(location2.name as any).vi} (${location2.id})`);
  console.log('\n🏠 Created rooms:');
  console.log(
    `🎤 ${(room1.name as any).vi} - ${room1.pricePerHour.toLocaleString()}đ/hour`,
  );
  console.log(
    `🎤 ${(room2.name as any).vi} - ${room2.pricePerHour.toLocaleString()}đ/hour`,
  );
  console.log(
    `🎤 ${(room3.name as any).vi} - ${room3.pricePerHour.toLocaleString()}đ/hour`,
  );
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
