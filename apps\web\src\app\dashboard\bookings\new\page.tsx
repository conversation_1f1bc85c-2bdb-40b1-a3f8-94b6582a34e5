'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Save,
  X,
  Users,
  Clock,
  MapPin,
  CreditCard,
} from 'lucide-react';
import {
  Location,
  Room,
  CreateBookingDto,
  User,
  UserResponseDto,
} from 'shared-types';
import { locationsService } from '../../../../services/locations.service';
import { roomsService } from '../../../../services/rooms.service';
import { bookingsService } from '../../../../services/bookings.service';
import { usersService } from '../../../../services/users.service';
import BookingFormLocation from '../../../../components/dashboard/BookingFormLocation';
import BookingFormCustomer from '../../../../components/dashboard/BookingFormCustomer';
import BookingFormDateTime from '../../../../components/dashboard/BookingFormDateTime';
import BookingFormSummary from '../../../../components/dashboard/BookingFormSummary';
import { AlertDialog } from '../../../../components/ui/dialog';
import type { AvailabilityState } from '../../../../hooks/useAvailabilityCheck';

interface BookingFormData {
  // Location & Room
  locationId: string;
  roomId: string;

  // Date & Time
  startTime: string; // ISO string
  endTime: string; // ISO string
  numberOfGuests: number;

  // Customer
  isGuestBooking: boolean;
  userId?: string;
  guestName?: string;
  guestEmail?: string;
  guestPhone?: string;

  // Additional
  notes?: string;
}

const CreateBookingPage = () => {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [isSuccessAlertOpen, setIsSuccessAlertOpen] = useState(false);
  const [newBookingId, setNewBookingId] = useState<string | null>(null);
  const [newBookingReference, setNewBookingReference] = useState<string | null>(
    null
  );

  // Form data
  const [formData, setFormData] = useState<BookingFormData>({
    locationId: '',
    roomId: '',
    startTime: '',
    endTime: '',
    numberOfGuests: 1,
    isGuestBooking: true,
  });

  // Data for form options
  const [locations, setLocations] = useState<Location[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(
    null
  );
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [selectedUser, setSelectedUser] = useState<UserResponseDto | null>(
    null
  );

  // Availability state for validation
  const [availabilityState, setAvailabilityState] = useState<AvailabilityState>(
    {
      status: 'idle',
      data: null,
      error: null,
      isChecking: false,
      alternatives: [],
      suggestionsLoading: false,
      isTransitioning: false,
      lastCheckedParams: null,
    }
  );

  // Validation
  const [errors, setErrors] = useState<
    Partial<Record<keyof BookingFormData, string>>
  >({});

  // Load locations on mount
  useEffect(() => {
    loadLocations();
  }, []);

  // Load rooms when location changes
  useEffect(() => {
    if (formData.locationId) {
      loadRooms(formData.locationId);
    } else {
      setRooms([]);
      setSelectedRoom(null);
      updateFormData({ roomId: '' });
    }
  }, [formData.locationId]);

  // Update selected location object
  useEffect(() => {
    const location = locations.find(l => l.id === formData.locationId);
    setSelectedLocation(location || null);
  }, [formData.locationId, locations]);

  // Update selected room object
  useEffect(() => {
    const room = rooms.find(r => r.id === formData.roomId);
    setSelectedRoom(room || null);
  }, [formData.roomId, rooms]);

  // Fetch user details when userId changes
  useEffect(() => {
    if (formData.userId && !formData.isGuestBooking) {
      loadUserDetails(formData.userId);
    } else {
      setSelectedUser(null);
    }
  }, [formData.userId, formData.isGuestBooking]);

  const loadLocations = async () => {
    try {
      setLoading(true);
      const response = await locationsService.getLocations({
        page: 1,
        limit: 100,
        isActive: true,
      });
      setLocations(response.items);
    } catch (error) {
      console.error('Error loading locations:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadRooms = async (locationId: string) => {
    try {
      setLoading(true);
      const roomsList = await roomsService.getRooms(locationId, {
        isActive: true,
      });
      setRooms(roomsList);
    } catch (error) {
      console.error('Error loading rooms:', error);
      setRooms([]);
    } finally {
      setLoading(false);
    }
  };

  const loadUserDetails = async (userId: string) => {
    try {
      const user = await usersService.getUserById(userId);
      setSelectedUser(user);
    } catch (error) {
      console.error('Error loading user details:', error);
      setSelectedUser(null);
    }
  };

  const updateFormData = (updates: Partial<BookingFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));

    // Clear related errors
    const newErrors = { ...errors };
    Object.keys(updates).forEach(key => {
      delete newErrors[key as keyof BookingFormData];
    });
    setErrors(newErrors);
  };

  const handleAvailabilityChange = useCallback(
    (availability: AvailabilityState) => {
      setAvailabilityState(availability);
    },
    []
  );

  const validateStep = (stepNumber: number): boolean => {
    const newErrors: Partial<Record<keyof BookingFormData, string>> = {};

    switch (stepNumber) {
      case 1: // Location & Room
        if (!formData.locationId) {
          newErrors.locationId = 'Vui lòng chọn chi nhánh';
        }
        if (!formData.roomId) {
          newErrors.roomId = 'Vui lòng chọn phòng';
        }
        break;

      case 2: // Customer
        if (formData.isGuestBooking) {
          if (!formData.guestName) {
            newErrors.guestName = 'Vui lòng nhập tên khách hàng';
          }
          if (!formData.guestEmail) {
            newErrors.guestEmail = 'Vui lòng nhập email';
          } else if (!/\S+@\S+\.\S+/.test(formData.guestEmail)) {
            newErrors.guestEmail = 'Email không hợp lệ';
          }
          if (!formData.guestPhone) {
            newErrors.guestPhone = 'Vui lòng nhập số điện thoại';
          }
        } else if (!formData.userId) {
          newErrors.userId = 'Vui lòng chọn khách hàng';
        }
        break;

      case 3: // Date & Time
        if (!formData.startTime) {
          newErrors.startTime = 'Vui lòng chọn thời gian bắt đầu';
        }
        if (!formData.endTime) {
          newErrors.endTime = 'Vui lòng chọn thời gian kết thúc';
        }

        if (formData.startTime && formData.endTime) {
          const start = new Date(formData.startTime);
          const end = new Date(formData.endTime);

          // Check if end time is after start time
          if (end <= start) {
            newErrors.endTime = 'Thời gian kết thúc phải sau thời gian bắt đầu';
          } else {
            const durationMs = end.getTime() - start.getTime();
            const durationHours = durationMs / (1000 * 60 * 60);

            if (durationHours > 8) {
              newErrors.endTime =
                'Thời gian đặt phòng không được vượt quá 8 giờ';
            }
          }

          // Check if times are within operating hours
          if (selectedLocation?.operatingHours) {
            try {
              const operatingHours =
                typeof selectedLocation.operatingHours === 'string'
                  ? JSON.parse(selectedLocation.operatingHours)
                  : selectedLocation.operatingHours;

              const date = new Date(formData.startTime);
              const dayNames = [
                'sunday',
                'monday',
                'tuesday',
                'wednesday',
                'thursday',
                'friday',
                'saturday',
              ];
              const dayName = dayNames[date.getDay()];

              const hoursForDay = operatingHours[dayName];
              if (!hoursForDay || !hoursForDay.includes('-')) {
                newErrors.startTime = 'Chi nhánh không hoạt động vào ngày này';
              } else {
                const [openTimeStr, closeTimeStr] = hoursForDay.split('-');
                const [openHour, openMinute] = openTimeStr
                  .split(':')
                  .map(Number);
                const [closeHour, closeMinute] = closeTimeStr
                  .split(':')
                  .map(Number);

                const startTimeHM = formData.startTime
                  .split('T')[1]
                  ?.slice(0, 5);
                const endTimeHM = formData.endTime.split('T')[1]?.slice(0, 5);

                if (startTimeHM && endTimeHM) {
                  const [startHour, startMinute] = startTimeHM
                    .split(':')
                    .map(Number);
                  const [endHour, endMinute] = endTimeHM.split(':').map(Number);

                  // Convert times to minutes for easier comparison
                  const openMinutes = openHour * 60 + openMinute;
                  const closeMinutes = closeHour * 60 + closeMinute;
                  const startMinutes = startHour * 60 + startMinute;
                  const endMinutes = endHour * 60 + endMinute;

                  // Handle midnight crossover
                  const crossesMidnight = closeMinutes <= openMinutes;

                  if (crossesMidnight) {
                    // For 24-hour operations or operations crossing midnight
                    if (
                      startMinutes < openMinutes &&
                      startMinutes > closeMinutes
                    ) {
                      newErrors.startTime = `Thời gian bắt đầu phải trong giờ hoạt động (${openTimeStr} - ${closeTimeStr})`;
                    }
                    if (endMinutes < openMinutes && endMinutes > closeMinutes) {
                      newErrors.endTime = `Thời gian kết thúc phải trong giờ hoạt động (${openTimeStr} - ${closeTimeStr})`;
                    }
                  } else {
                    // Normal operating hours within the same day
                    if (
                      startMinutes < openMinutes ||
                      startMinutes > closeMinutes
                    ) {
                      newErrors.startTime = `Thời gian bắt đầu phải trong giờ hoạt động (${openTimeStr} - ${closeTimeStr})`;
                    }
                    if (endMinutes < openMinutes || endMinutes > closeMinutes) {
                      newErrors.endTime = `Thời gian kết thúc phải trong giờ hoạt động (${openTimeStr} - ${closeTimeStr})`;
                    }
                  }
                }
              }
            } catch (error) {
              console.error('Error validating operating hours:', error);
            }
          }
        }

        if (formData.numberOfGuests < 1) {
          newErrors.numberOfGuests = 'Số khách phải lớn hơn 0';
        }
        if (selectedRoom && formData.numberOfGuests > selectedRoom.capacity) {
          newErrors.numberOfGuests = `Số khách không được vượt quá ${selectedRoom.capacity} người`;
        }

        // Check availability status
        if (availabilityState.status === 'checking') {
          newErrors.startTime =
            'Đang kiểm tra tình trạng phòng. Vui lòng đợi...';
        } else if (availabilityState.status === 'unavailable') {
          newErrors.startTime =
            'Khoảng thời gian này không có sẵn. Vui lòng chọn thời gian khác.';
        } else if (availabilityState.status === 'error') {
          newErrors.startTime =
            availabilityState.error ||
            'Có lỗi xảy ra khi kiểm tra tình trạng phòng.';
        } else if (
          availabilityState.status === 'idle' &&
          formData.startTime &&
          formData.endTime &&
          formData.roomId
        ) {
          newErrors.startTime =
            'Chưa kiểm tra tình trạng phòng. Vui lòng đợi...';
        }

        break;
    }

    setErrors(newErrors);

    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(step)) {
      setStep(prev => Math.min(prev + 1, 4));
    }
  };

  const handlePrevious = () => {
    setStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    const isStep3Valid = validateStep(3);
    if (!isStep3Valid) return;

    const isStep2Valid = validateStep(2);
    if (!isStep2Valid) {
      setStep(2);
      return;
    }

    try {
      setSaving(true);

      // Prepare booking data strictly according to CreateBookingDto
      const bookingData: CreateBookingDto = {
        roomId: formData.roomId,
        startTime: formData.startTime,
        endTime: formData.endTime,
        numberOfGuests: formData.numberOfGuests,
        notes: formData.notes,
      };

      let response;

      if (formData.isGuestBooking) {
        // For guest bookings, add required guest details
        if (
          !formData.guestName ||
          !formData.guestEmail ||
          !formData.guestPhone
        ) {
          setErrors(prev => ({
            ...prev,
            form: 'Thông tin khách hàng không đầy đủ. Vui lòng kiểm tra lại.',
          }));
          setSaving(false);
          setStep(2); // Go back to customer step
          return;
        }

        bookingData.guestName = formData.guestName;
        bookingData.guestEmail = formData.guestEmail;
        bookingData.guestPhone = formData.guestPhone;

        response = await bookingsService.createGuestBookingByAdmin(bookingData);
      } else {
        // For user bookings, add userId
        if (!formData.userId) {
          setErrors(prev => ({
            ...prev,
            form: 'Chưa chọn khách hàng. Vui lòng chọn khách hàng từ danh sách.',
          }));
          setSaving(false);
          setStep(2); // Go back to customer step
          return;
        }

        // Create extended booking data with userId for user bookings
        const userBookingData = {
          ...bookingData,
          userId: formData.userId,
        };

        response =
          await bookingsService.createUserBookingByAdmin(userBookingData);
      }

      // ApiResponse<Booking> is expected, where Booking is in response.data
      if (response && response.data && response.data.id) {
        setNewBookingId(response.data.id);
        setNewBookingReference(response.data.bookingReference);
        setIsSuccessAlertOpen(true);
      } else {
        console.error('Booking creation response missing ID or data', response);
        setErrors(prev => ({
          ...prev,
          form: 'Không thể tạo đặt phòng hoặc phản hồi không hợp lệ.',
        }));
      }
    } catch (error: any) {
      console.error('Error creating booking:', error);
      let errorMessage = 'Đã có lỗi xảy ra trong quá trình tạo đặt phòng.';
      if (
        error.response &&
        error.response.data &&
        error.response.data.message
      ) {
        if (Array.isArray(error.response.data.message)) {
          errorMessage = error.response.data.message.join('\n');
        } else {
          errorMessage = error.response.data.message;
        }
      }
      setErrors(prev => ({ ...prev, form: errorMessage }));
    } finally {
      setSaving(false);
    }
  };

  const handleSuccessAlertClose = () => {
    setIsSuccessAlertOpen(false);
    if (newBookingId) {
      router.push(`/dashboard/bookings/${newBookingId}`);
      setNewBookingId(null); // Reset for next time
      setNewBookingReference(null); // Reset for next time
    } else {
      router.push('/dashboard/bookings'); // Fallback
    }
  };

  const getStepTitle = (stepNumber: number): string => {
    switch (stepNumber) {
      case 1:
        return 'Chọn chi nhánh & phòng';
      case 2:
        return 'Thông tin khách hàng';
      case 3:
        return 'Chọn thời gian';
      case 4:
        return 'Xác nhận đặt phòng';
      default:
        return '';
    }
  };

  const getStepIcon = (stepNumber: number) => {
    switch (stepNumber) {
      case 1:
        return <MapPin className="w-5 h-5" />;
      case 2:
        return <Users className="w-5 h-5" />;
      case 3:
        return <Clock className="w-5 h-5" />;
      case 4:
        return <CreditCard className="w-5 h-5" />;
      default:
        return null;
    }
  };

  const isStepCompleted = (stepNumber: number): boolean => {
    switch (stepNumber) {
      case 1:
        return !!(formData.locationId && formData.roomId);
      case 2:
        return formData.isGuestBooking
          ? !!(formData.guestName && formData.guestEmail && formData.guestPhone)
          : !!formData.userId;
      case 3:
        return !!(
          formData.startTime &&
          formData.endTime &&
          formData.numberOfGuests > 0 &&
          availabilityState.status === 'available'
        );
      default:
        return false;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => router.back()}
          className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>
        <div>
          <h1 className="text-h1 font-semibold text-gray-900">
            Tạo đặt phòng mới
          </h1>
          <p className="text-base text-gray-600 mt-1">
            Tạo đặt phòng cho khách hàng từ giao diện quản trị
          </p>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          {[1, 2, 3, 4].map(stepNumber => (
            <div key={stepNumber} className="flex items-center">
              <div
                className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors ${
                  step === stepNumber
                    ? 'border-gold-600 bg-gold-600 text-white'
                    : isStepCompleted(stepNumber)
                      ? 'border-green-600 bg-green-600 text-white'
                      : 'border-gray-300 bg-white text-gray-400'
                }`}
              >
                {isStepCompleted(stepNumber) ? (
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  getStepIcon(stepNumber)
                )}
              </div>
              <div className={`ml-3 ${stepNumber < 4 ? 'mr-8' : ''}`}>
                <div
                  className={`text-sm font-medium ${
                    step === stepNumber ? 'text-gold-600' : 'text-gray-900'
                  }`}
                >
                  Bước {stepNumber}
                </div>
                <div className="text-xs text-gray-500">
                  {getStepTitle(stepNumber)}
                </div>
              </div>
              {stepNumber < 4 && (
                <div
                  className={`flex-1 h-px mx-4 ${
                    isStepCompleted(stepNumber) ? 'bg-green-600' : 'bg-gray-300'
                  }`}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Form Content */}
      <div className="bg-white rounded-lg border border-gray-200">
        {step === 1 && (
          <BookingFormLocation
            formData={formData}
            locations={locations}
            rooms={rooms}
            selectedLocation={selectedLocation}
            selectedRoom={selectedRoom}
            errors={errors}
            loading={loading}
            onUpdate={updateFormData}
          />
        )}

        {step === 2 && (
          <BookingFormCustomer
            formData={formData}
            errors={errors}
            onUpdate={updateFormData}
          />
        )}

        {step === 3 && (
          <BookingFormDateTime
            formData={formData}
            selectedRoom={selectedRoom}
            selectedLocation={selectedLocation}
            errors={errors}
            onUpdate={updateFormData}
            onAvailabilityChange={handleAvailabilityChange}
          />
        )}

        {step === 4 && (
          <BookingFormSummary
            formData={formData}
            selectedLocation={selectedLocation}
            selectedRoom={selectedRoom}
            selectedUser={selectedUser}
            onUpdate={updateFormData}
          />
        )}
      </div>

      {isSuccessAlertOpen && (
        <AlertDialog
          isOpen={isSuccessAlertOpen}
          onClose={handleSuccessAlertClose}
          title="Đặt phòng thành công!"
          message={`Phòng đã được đặt thành công.${newBookingReference ? `\nMã đặt phòng của bạn là: ${newBookingReference}.` : ''}`}
          type="success"
          confirmText="Tuyệt vời!"
        />
      )}

      {/* Navigation */}
      <div className="flex justify-between">
        <button
          onClick={handlePrevious}
          disabled={step === 1}
          className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Quay lại
        </button>

        <div className="flex gap-3">
          {step < 4 ? (
            (() => {
              const isStep3Unavailable = Boolean(
                step === 3 &&
                  (availabilityState.status === 'checking' ||
                    availabilityState.status === 'unavailable' ||
                    availabilityState.status === 'error' ||
                    (availabilityState.status === 'idle' &&
                      formData.startTime &&
                      formData.endTime &&
                      formData.roomId))
              );

              return (
                <button
                  onClick={handleNext}
                  disabled={isStep3Unavailable}
                  className={`px-6 py-3 rounded-lg flex items-center gap-2 transition-colors ${
                    isStep3Unavailable
                      ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                      : 'bg-gold-600 hover:bg-gold-700 text-white'
                  }`}
                >
                  {step === 3 && availabilityState.status === 'checking' ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
                      Kiểm tra tình trạng...
                    </>
                  ) : step === 3 &&
                    (availabilityState.status === 'unavailable' ||
                      availabilityState.status === 'error') ? (
                    <>Không thể tiếp tục</>
                  ) : (
                    <>
                      Tiếp tục
                      <ArrowLeft className="w-4 h-4 rotate-180" />
                    </>
                  )}
                </button>
              );
            })()
          ) : (
            <button
              onClick={handleSubmit}
              disabled={saving}
              className="bg-gold-600 hover:bg-gold-700 text-white px-6 py-3 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Đang tạo...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  Tạo đặt phòng
                </>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreateBookingPage;
