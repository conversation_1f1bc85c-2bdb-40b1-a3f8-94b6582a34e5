'use client';

import React, { useEffect, useState } from 'react';
import { useAuthStore } from '../../../stores/auth-store';
import { UserRole, User } from 'shared-types';
import type { UserListResponseDto, UserStatsDto } from 'shared-types';
import { usersService } from '../../../services/users.service';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from '../../../components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';
import {
  Users,
  UserPlus,
  Search,
  Download,
  MoreHorizontal,
  Edit,
  Trash2,
  UserCheck,
  UserX,
  RefreshCw,
  Eye,
  Shield,
  ActivitySquare,
  X,
} from 'lucide-react';
import Link from 'next/link';
import { useDialogs } from '../../../components/ui/modal-provider';
import { useRouter } from 'next/navigation';

interface UserListItem extends User {
  bookingCount?: number;
  totalSpent?: number;
}

const UsersPage = () => {
  const { user: currentUser } = useAuthStore();
  const { alert, confirm } = useDialogs();
  const [users, setUsers] = useState<UserListItem[]>([]);
  const [stats, setStats] = useState<UserStatsDto | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // Fetch users from API
  const fetchUsers = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      setError(null);

      const params = {
        limit: 100, // Get more users for now
        ...(searchTerm && { search: searchTerm }),
        ...(roleFilter !== 'all' && { role: roleFilter as UserRole }),
        ...(statusFilter === 'active' && { isActive: true }),
        ...(statusFilter === 'inactive' && { isActive: false }),
        ...(statusFilter === 'verified' && { emailVerified: true }),
        ...(statusFilter === 'unverified' && { emailVerified: false }),
      };

      const [usersResponse, statsResponse] = await Promise.all([
        usersService.getUsers(params),
        usersService.getUserStats().catch(() => null), // Don't fail if stats unavailable
      ]);

      setUsers(usersResponse.users || []);
      setStats(statsResponse);
    } catch (err) {
      console.error('Failed to fetch users:', err);
      setError(
        err instanceof Error
          ? err.message
          : 'Có lỗi xảy ra khi tải danh sách người dùng'
      );
      alert(
        'Không thể tải danh sách người dùng. Vui lòng thử lại.',
        'Lỗi',
        'error'
      );
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchUsers();
  }, []);

  // Refresh when filters change (debounced)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (!loading) {
        fetchUsers(false);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, roleFilter, statusFilter]);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchUsers(false);
  };

  const handleDeleteUser = async (user: UserListItem) => {
    if (!usersService.canDeleteUser(user, currentUser?.id || '')) {
      alert(
        'Không thể xóa người dùng này. Người dùng có thể đã có đặt phòng hoặc bạn đang cố xóa chính mình.',
        'Không thể xóa',
        'warning'
      );
      return;
    }

    const confirmed = await confirm(
      `Bạn có chắc chắn muốn xóa người dùng "${user.name || user.email}"?`,
      'Xác nhận xóa người dùng'
    );

    if (confirmed) {
      try {
        await usersService.deleteUser(user.id);
        alert('Xóa người dùng thành công!', 'Thành công', 'success');
        fetchUsers(false);
      } catch (err) {
        console.error('Failed to delete user:', err);
        alert(
          err instanceof Error
            ? err.message
            : 'Có lỗi xảy ra khi xóa người dùng',
          'Lỗi',
          'error'
        );
      }
    }
  };

  const getRoleLabel = (role: UserRole): string => {
    return usersService.getRoleLabel(role);
  };

  const getRoleColor = (role: UserRole): string => {
    switch (role) {
      case UserRole.CUSTOMER:
        return 'bg-blue-100 text-blue-800';
      case UserRole.ADMIN:
        return 'bg-green-100 text-green-800';
      case UserRole.SUPER_ADMIN:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusDisplay = (isActive: boolean, emailVerified: boolean) => {
    if (!isActive) {
      return {
        label: 'Bị vô hiệu hóa',
        color: 'bg-red-100 text-red-800',
      };
    }
    if (!emailVerified) {
      return {
        label: 'Chưa xác thực',
        color: 'bg-yellow-100 text-yellow-800',
      };
    }
    return {
      label: 'Hoạt động',
      color: 'bg-green-100 text-green-800',
    };
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch =
      user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    const matchesStatus =
      statusFilter === 'all' ||
      (statusFilter === 'active' && user.isActive) ||
      (statusFilter === 'inactive' && !user.isActive) ||
      (statusFilter === 'verified' && user.emailVerified) ||
      (statusFilter === 'unverified' && !user.emailVerified);

    return matchesSearch && matchesRole && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gold-600"></div>
        <span className="ml-2 text-gray-900">
          Đang tải danh sách người dùng...
        </span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-h1 font-bold text-gray-900">
            Quản Lý Người Dùng
          </h1>
          <p className="text-lg text-gray-600">
            Quản lý tất cả người dùng trên hệ thống
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => handleRefresh()}
            variant="outline"
            className="gap-2"
            disabled={refreshing}
          >
            <RefreshCw
              className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`}
            />
            Làm mới
          </Button>
          <Button
            onClick={() => {
              // TODO: Implement export functionality
            }}
            variant="outline"
            className="gap-2"
          >
            <Download className="h-4 w-4" />
            Xuất Excel
          </Button>
          <Button
            onClick={() => router.push('/dashboard/users/new')}
            className="gap-2 bg-queens-gold hover:bg-queens-gold/90 text-white font-medium shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
          >
            <UserPlus className="h-4 w-4" />
            Thêm Người Dùng
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-900">
              Tổng người dùng
            </CardTitle>
            <Users className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-black">
              {stats?.totalUsers || users.length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-900">
              Đang hoạt động
            </CardTitle>
            <UserCheck className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {stats?.activeUsers || users.filter(u => u.isActive).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-900">
              Bị vô hiệu hóa
            </CardTitle>
            <UserX className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {stats?.inactiveUsers || users.filter(u => !u.isActive).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-900">
              Chưa xác thực
            </CardTitle>
            <Users className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {stats?.unverifiedUsers ||
                users.filter(u => !u.emailVerified).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Error Message */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <div className="text-red-600 text-sm font-medium">{error}</div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchUsers()}
                className="ml-auto"
              >
                Thử lại
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-6 space-y-4">
          {/* Search Row */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
            <Input
              placeholder="Tìm kiếm theo tên, email..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="pl-10 text-gray-900 w-full"
            />
          </div>

          {/* Filters Row */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Role Filter */}
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="text-black">
                <Shield className="w-4 h-4 mr-2 text-gray-500" />
                <span className="text-black">
                  {roleFilter === 'all'
                    ? 'Tất cả vai trò'
                    : getRoleLabel(roleFilter as UserRole)}
                </span>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  <span className="text-black">Tất cả vai trò</span>
                </SelectItem>
                {Object.values(UserRole).map(role => (
                  <SelectItem key={role} value={role}>
                    <span className="text-black">{getRoleLabel(role)}</span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Status Filter */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="text-black">
                <ActivitySquare className="w-4 h-4 mr-2 text-gray-500" />
                <span className="text-black">
                  {statusFilter === 'all'
                    ? 'Tất cả trạng thái'
                    : statusFilter === 'active'
                      ? 'Đang hoạt động'
                      : statusFilter === 'inactive'
                        ? 'Bị vô hiệu hóa'
                        : statusFilter === 'verified'
                          ? 'Đã xác thực'
                          : 'Chưa xác thực'}
                </span>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  <span className="text-black">Tất cả trạng thái</span>
                </SelectItem>
                <SelectItem value="active">
                  <span className="text-black">Đang hoạt động</span>
                </SelectItem>
                <SelectItem value="inactive">
                  <span className="text-black">Bị vô hiệu hóa</span>
                </SelectItem>
                <SelectItem value="verified">
                  <span className="text-black">Đã xác thực</span>
                </SelectItem>
                <SelectItem value="unverified">
                  <span className="text-black">Chưa xác thực</span>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Active Filters */}
          {(roleFilter !== 'all' || statusFilter !== 'all' || searchTerm) && (
            <div className="flex items-center justify-end gap-2">
              <span className="text-sm text-gray-600">
                {[
                  roleFilter !== 'all' && 'Vai trò',
                  statusFilter !== 'all' && 'Trạng thái',
                  searchTerm && 'Tìm kiếm',
                ].filter(Boolean).length + ' bộ lọc đang áp dụng'}
              </span>
              <button
                onClick={() => {
                  setRoleFilter('all');
                  setStatusFilter('all');
                  setSearchTerm('');
                }}
                className="flex items-center gap-1.5 text-sm text-gray-700 hover:text-gray-900"
              >
                <X className="w-4 h-4" />
                Xóa bộ lọc
              </button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Users Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredUsers.map(user => {
          const status = getStatusDisplay(user.isActive, user.emailVerified);
          return (
            <Card key={user.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3 flex-1">
                    <div className="w-12 h-12 bg-gold-100 rounded-full flex items-center justify-center">
                      <span className="text-lg font-medium text-gold-700">
                        {user.name?.charAt(0).toUpperCase() ||
                          user.email.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-gray-900 truncate">
                        {user.name || 'Chưa có tên'}
                      </h3>
                      <p className="text-sm text-gray-900 truncate">
                        {user.email}
                      </p>
                      {user.phoneNumber && (
                        <p className="text-sm text-gray-900">
                          {user.phoneNumber}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="mt-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-900">Vai trò:</span>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}
                    >
                      {getRoleLabel(user.role)}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-900">Trạng thái:</span>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${status.color}`}
                    >
                      {status.label}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-900">Số đặt phòng:</span>
                    <span className="text-sm font-medium text-black">
                      {user.bookingCount || 0}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-900">
                      Tổng chi tiêu:
                    </span>
                    <span className="text-sm font-medium text-black">
                      {user.totalSpent
                        ? formatCurrency(user.totalSpent)
                        : '0 VND'}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-900">Tham gia:</span>
                    <span className="text-sm font-medium text-black">
                      {formatDate(user.createdAt)}
                    </span>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex gap-2">
                    <Link
                      href={`/dashboard/users/${user.id}/edit`}
                      className="flex-1"
                    >
                      <Button variant="outline" size="sm" className="w-full">
                        <Edit className="h-4 w-4 mr-1" />
                        Chỉnh sửa
                      </Button>
                    </Link>
                    <Link
                      href={`/dashboard/bookings?email=${encodeURIComponent(user.email)}`}
                      className="flex-1"
                    >
                      <Button variant="outline" size="sm" className="w-full">
                        <Eye className="h-4 w-4 mr-1" />
                        Xem
                      </Button>
                    </Link>
                    {usersService.canDeleteUser(
                      user,
                      currentUser?.id || ''
                    ) && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                        onClick={() => handleDeleteUser(user)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredUsers.length === 0 && !loading && (
        <Card>
          <CardContent className="pt-8 pb-8">
            <div className="text-center">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Không tìm thấy người dùng
              </h3>
              <p className="text-gray-900 mb-4">
                Không có người dùng nào phù hợp với bộ lọc hiện tại.
              </p>
              <Button
                onClick={() => {
                  setSearchTerm('');
                  setRoleFilter('all');
                  setStatusFilter('all');
                }}
              >
                Xóa bộ lọc
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default UsersPage;
