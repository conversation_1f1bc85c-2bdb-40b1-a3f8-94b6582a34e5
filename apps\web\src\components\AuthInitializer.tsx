'use client';

import { useEffect } from 'react';
import { useAuthStore } from '../stores/auth-store';

export function AuthInitializer() {
  const { initializeAuth, isInitialized } = useAuthStore();

  useEffect(() => {
    if (!isInitialized) {
      // Initialize auth state from persisted storage
      initializeAuth();
    }
  }, [initializeAuth, isInitialized]);

  // This component doesn't render anything
  return null;
}
