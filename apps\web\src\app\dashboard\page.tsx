'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '../../stores/auth-store';
import type { DashboardAnalyticsDto, Booking, User } from 'shared-types';
import { BookingStatus } from 'shared-types';
import StatCard from '../../components/dashboard/StatCard';
import RecentActivityFeed from '../../components/dashboard/RecentActivityFeed';
import QuickActions from '../../components/dashboard/QuickActions';
import { analyticsService } from '../../services/analytics.service';
import { bookingsService } from '../../services/bookings.service';
import { usersService } from '../../services/users.service';
import { Button } from '../../components/ui/button';
import { useDialogs } from '../../components/ui/modal-provider';
import {
  Building2,
  Users,
  Calendar,
  DollarSign,
  TrendingUp,
  <PERSON>,
  <PERSON>fresh<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Wifi,
  WifiOff,
} from 'lucide-react';

interface ActivityItem {
  id: string;
  type:
    | 'booking'
    | 'user_registration'
    | 'booking_cancellation'
    | 'system_update';
  title: string;
  description: string;
  timestamp: string;
  metadata?: {
    amount?: number;
    roomType?: string;
    userRole?: string;
  };
}

// Loading skeleton components
const DashboardSkeleton = () => {
  return (
    <div className="space-y-6 animate-in fade-in duration-300">
      {/* Header Skeleton */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-2">
          <div className="h-8 w-80 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-5 w-96 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="h-10 w-24 bg-gray-200 rounded animate-pulse"></div>
      </div>

      {/* Stats Grid Skeleton */}
      <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="animate-in slide-in-from-bottom duration-500 opacity-0"
            style={{
              animationDelay: `${i * 100}ms`,
              animationFillMode: 'forwards',
            }}
          >
            <StatCard
              title=""
              value=""
              subtitle=""
              icon={Clock}
              iconColor=""
              valueColor=""
              loading={true}
            />
          </div>
        ))}
      </div>

      {/* Content Grid Skeleton */}
      <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
        <div className="lg:col-span-2 animate-in slide-in-from-left duration-700 delay-700">
          <RecentActivityFeed activities={[]} loading={true} />
        </div>
        <div className="lg:col-span-1 animate-in slide-in-from-right duration-700 delay-800">
          <div className="bg-white rounded-lg border shadow-sm">
            <div className="p-6">
              <div className="h-6 w-32 bg-gray-200 rounded animate-pulse mb-4"></div>
              <div className="grid grid-cols-2 gap-3">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="h-20 bg-gray-200 rounded animate-pulse"
                  ></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function AdminDashboard() {
  const { user } = useAuthStore();
  const router = useRouter();
  const { alert } = useDialogs();

  // State management
  const [dashboardStats, setDashboardStats] =
    useState<DashboardAnalyticsDto | null>(null);
  const [recentActivities, setRecentActivities] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [statsLoaded, setStatsLoaded] = useState(false);
  const [activitiesLoaded, setActivitiesLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [isOnline, setIsOnline] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Utility functions
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      notation: 'compact',
      maximumFractionDigits: 1,
    }).format(amount);
  };

  const calculateGrowth = (
    current: number,
    previous: number
  ): { value: number; trend: 'up' | 'down' | 'stable' } => {
    if (previous === 0)
      return {
        value: current > 0 ? 100 : 0,
        trend: current > 0 ? 'up' : 'stable',
      };
    const growth = ((current - previous) / previous) * 100;
    return {
      value: Math.abs(growth),
      trend: growth > 5 ? 'up' : growth < -5 ? 'down' : 'stable',
    };
  };

  // Fetch recent activities from multiple sources
  const fetchRecentActivities = useCallback(async (): Promise<
    ActivityItem[]
  > => {
    try {
      const activities: ActivityItem[] = [];

      // Fetch recent bookings
      const recentBookings = await bookingsService.getAllBookings({
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      });

      recentBookings.items?.forEach((booking: Booking) => {
        if (booking.status === BookingStatus.CONFIRMED) {
          activities.push({
            id: `booking-${booking.id}`,
            type: 'booking',
            title: 'Đặt phòng mới',
            description: `${booking.guestName} đặt phòng ${booking.room?.name?.vi || 'Unknown'} - ${booking.bookingReference}`,
            timestamp: booking.createdAt,
            metadata: {
              amount: booking.totalPrice,
              roomType: booking.room?.roomType,
            },
          });
        } else if (
          booking.status === BookingStatus.CANCELLED_BY_ADMIN ||
          booking.status === BookingStatus.CANCELLED_BY_USER
        ) {
          activities.push({
            id: `booking-cancel-${booking.id}`,
            type: 'booking_cancellation',
            title: 'Hủy đặt phòng',
            description: `Đặt phòng ${booking.bookingReference} đã bị hủy`,
            timestamp: booking.updatedAt,
            metadata: {
              amount: booking.totalPrice,
            },
          });
        }
      });

      // Fetch recent user registrations
      const recentUsers = await usersService.getUsers({
        page: 1,
        limit: 5,
      });

      recentUsers.users?.forEach((user: User) => {
        activities.push({
          id: `user-${user.id}`,
          type: 'user_registration',
          title: 'Đăng ký mới',
          description: `${user.name || user.email} đã tạo tài khoản`,
          timestamp: user.createdAt,
          metadata: {
            userRole: user.role,
          },
        });
      });

      // Sort by timestamp descending and limit to 10 most recent
      return activities
        .sort(
          (a, b) =>
            new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        )
        .slice(0, 10);
    } catch (error) {
      console.error('Error fetching recent activities:', error);
      return [];
    }
  }, []);

  // Main data fetching function with staggered loading
  const fetchDashboardData = useCallback(
    async (isRefresh = false) => {
      try {
        setError(null);
        if (isRefresh) {
          setRefreshing(true);
        } else {
          setLoading(true);
          setStatsLoaded(false);
          setActivitiesLoaded(false);
        }

        // Check authentication
        if (!analyticsService.isAuthenticated()) {
          setError(
            'Bạn cần đăng nhập để xem dashboard. Vui lòng đăng nhập lại.'
          );
          return;
        }

        // Fetch stats first (higher priority)
        const statsPromise = analyticsService
          .getDashboardStats()
          .then(stats => {
            setDashboardStats(stats);
            setStatsLoaded(true);
            return stats;
          });

        // Fetch activities with a slight delay for staggered effect
        const activitiesPromise = new Promise<ActivityItem[]>(resolve => {
          setTimeout(
            async () => {
              const activities = await fetchRecentActivities();
              setRecentActivities(activities);
              setActivitiesLoaded(true);
              resolve(activities);
            },
            isRefresh ? 0 : 300
          ); // No delay on refresh, 300ms delay on initial load
        });

        await Promise.all([statsPromise, activitiesPromise]);
        setLastUpdated(new Date());
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'Có lỗi xảy ra khi tải dữ liệu dashboard';
        setError(errorMessage);

        // Show error dialog for critical errors
        if (errorMessage.includes('Authentication')) {
          await alert(
            'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.',
            'Lỗi xác thực',
            'error'
          );
          router.push('/login');
        }
      } finally {
        // Smooth transition out of loading state
        setTimeout(
          () => {
            setLoading(false);
            setRefreshing(false);
          },
          isRefresh ? 0 : 100
        );
      }
    },
    [fetchRecentActivities, alert, router]
  );

  // Auto-refresh functionality
  useEffect(() => {
    fetchDashboardData();

    // Set up auto-refresh every 5 minutes
    const interval = setInterval(() => fetchDashboardData(true), 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [fetchDashboardData]);

  // Network status monitoring
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Manual refresh handler
  const handleRefresh = () => {
    fetchDashboardData(true);
  };

  // Navigation handlers
  const handleNavigateToLocations = () => router.push('/dashboard/locations');
  const handleNavigateToRooms = () => router.push('/dashboard/rooms');
  const handleNavigateToBookings = () => router.push('/dashboard/bookings');
  const handleNavigateToUsers = () => router.push('/dashboard/users');
  const handleNavigateToAnalytics = () => router.push('/dashboard/analytics');

  // Show loading skeleton
  if (loading && !dashboardStats) {
    return <DashboardSkeleton />;
  }

  // Error state
  if (error && !dashboardStats) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4 animate-in fade-in duration-500">
        <AlertCircle className="w-16 h-16 text-red-500" />
        <h2 className="text-xl font-semibold text-gray-800">
          Không thể tải dữ liệu
        </h2>
        <p className="text-gray-600 text-center max-w-md">{error}</p>
        <Button onClick={handleRefresh} className="flex items-center gap-2">
          <RefreshCw className="w-4 h-4" />
          Thử lại
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 animate-in fade-in slide-in-from-top duration-500">
        <div>
          <h1 className="text-h1 text-gray-800 font-semibold">
            Chào mừng trở lại, {user?.name}
          </h1>
          <div className="flex items-center gap-2 mt-1">
            <p className="text-lg text-gray-600">
              Tổng quan hoạt động kinh doanh Queen Karaoke
            </p>
            <div className="flex items-center gap-1">
              {isOnline ? (
                <Wifi className="w-4 h-4 text-green-500" />
              ) : (
                <WifiOff className="w-4 h-4 text-red-500" />
              )}
              {lastUpdated && (
                <span className="text-xs text-gray-500">
                  Cập nhật lúc {lastUpdated.toLocaleTimeString('vi-VN')}
                </span>
              )}
            </div>
          </div>
        </div>

        <Button
          onClick={handleRefresh}
          variant="outline"
          className="flex items-center gap-2 transition-all duration-200 hover:scale-105"
          disabled={refreshing}
        >
          <RefreshCw
            className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`}
          />
          Làm mới
        </Button>
      </div>

      {/* Statistics Grid */}
      <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        {[
          {
            title: 'Chi nhánh',
            value: dashboardStats?.totalLocations || 0,
            subtitle: 'Cơ sở hoạt động',
            icon: Building2,
            iconColor: 'text-gold-600',
            valueColor: 'text-gold-800',
            onClick: handleNavigateToLocations,
          },
          {
            title: 'Phòng hát',
            value: dashboardStats?.totalRooms || 0,
            subtitle: 'Phòng khả dụng',
            icon: Users,
            iconColor: 'text-sapphire-primary',
            valueColor: 'text-sapphire-primary',
            onClick: handleNavigateToRooms,
          },
          {
            title: 'Tổng đặt phòng',
            value: dashboardStats?.totalBookings || 0,
            subtitle: 'Tất cả thời gian',
            icon: Calendar,
            iconColor: 'text-ruby-primary',
            valueColor: 'text-ruby-primary',
            growth: dashboardStats
              ? calculateGrowth(
                  dashboardStats.thisMonthBookings,
                  dashboardStats.previousMonthBookings
                )
              : undefined,
            onClick: handleNavigateToBookings,
          },
          {
            title: 'Doanh thu',
            value: dashboardStats
              ? formatCurrency(dashboardStats.thisMonthRevenue)
              : '0',
            subtitle: 'Tháng này',
            icon: DollarSign,
            iconColor: 'text-queens-primary',
            valueColor: 'text-queens-primary',
            growth: dashboardStats
              ? calculateGrowth(
                  dashboardStats.thisMonthRevenue,
                  dashboardStats.previousMonthRevenue
                )
              : undefined,
            onClick: handleNavigateToAnalytics,
          },
          {
            title: 'Hôm nay',
            value: dashboardStats?.todayBookings || 0,
            subtitle: 'Đặt phòng hiện tại',
            icon: Clock,
            iconColor: 'text-opal-primary',
            valueColor: 'text-opal-primary',
            onClick: handleNavigateToBookings,
          },
          {
            title: 'Tỷ lệ lấp đầy',
            value: `${(dashboardStats?.occupancyRate || 0).toFixed(1)}%`,
            subtitle: 'Hiệu suất sử dụng',
            icon: TrendingUp,
            iconColor: 'text-pearl-primary',
            valueColor: 'text-pearl-primary',
            onClick: handleNavigateToAnalytics,
          },
        ].map((stat, index) => (
          <div
            key={stat.title}
            className={`animate-in slide-in-from-bottom duration-500 ${
              statsLoaded ? 'opacity-100' : 'opacity-0'
            }`}
            style={{
              animationDelay: statsLoaded ? `${index * 100}ms` : '0ms',
              transitionDelay: statsLoaded ? `${index * 100}ms` : '0ms',
            }}
          >
            <StatCard
              title={stat.title}
              value={stat.value}
              subtitle={stat.subtitle}
              icon={stat.icon}
              iconColor={stat.iconColor}
              valueColor={stat.valueColor}
              growth={stat.growth}
              onClick={stat.onClick}
              loading={!statsLoaded}
            />
          </div>
        ))}
      </div>

      {/* Content Grid */}
      <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
        {/* Recent Activity Feed */}
        <div
          className={`lg:col-span-2 animate-in slide-in-from-left duration-700 transition-opacity ${
            activitiesLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          style={{
            transitionDelay: activitiesLoaded ? '200ms' : '0ms',
          }}
        >
          <RecentActivityFeed
            activities={recentActivities}
            loading={!activitiesLoaded}
          />
        </div>

        {/* Quick Actions */}
        <div
          className={`lg:col-span-1 animate-in slide-in-from-right duration-700 transition-opacity ${
            statsLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          style={{
            transitionDelay: statsLoaded ? '400ms' : '0ms',
          }}
        >
          <QuickActions />
        </div>
      </div>

      {/* Error Banner */}
      {error && dashboardStats && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 animate-in slide-in-from-bottom duration-300">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-yellow-600" />
            <p className="text-sm text-yellow-800">
              Có lỗi khi cập nhật dữ liệu: {error}
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              className="ml-auto"
            >
              Thử lại
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
