import {
  Injectable,
  BadRequestException,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { AvailabilityService } from '../availability/availability.service';
import { RoomsService } from '../rooms/rooms.service';
import { BookingReferenceGenerator } from './booking-reference.generator';
import { CreateBookingDto } from './dto';
import { AdminUpdateBookingDto } from './dto/update-booking.dto';
import {
  User,
  Booking,
  Prisma,
  Room as PrismaRoom,
  Location as PrismaLocation,
} from '../../generated/prisma';
import {
  BookingStatus as SharedBookingStatus,
  PaginatedResponse,
  SortOrder,
  UserRole,
} from '@shared-types/common.types';
import { BookingQueryDto } from './dto/booking-query.dto';
import { EmailService } from '../email/email.service';
import { ConfigService } from '@nestjs/config';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';

const MIN_BOOKING_DURATION_MINUTES = 30;
const MAX_BOOKING_DURATION_HOURS = 8;

// Define a type for Bookings with included relations for the reminder service
type BookingWithRelationsForReminder = Booking & {
  user: Pick<User, 'name' | 'email'> | null;
  room: Pick<PrismaRoom, 'name'>;
  location: Pick<PrismaLocation, 'name' | 'address'>;
};

@Injectable()
export class BookingsService {
  private readonly logger = new Logger(BookingsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly availabilityService: AvailabilityService,
    private readonly roomsService: RoomsService,
    private readonly bookingReferenceGenerator: BookingReferenceGenerator,
    private readonly emailService: EmailService,
    private readonly configService: ConfigService,
  ) {}

  async createBooking(createBookingDto: CreateBookingDto): Promise<Booking> {
    const {
      roomId,
      userId,
      startTime,
      endTime,
      numberOfGuests,
      guestName,
      guestEmail,
      guestPhone,
      notes,
    } = createBookingDto;

    const parsedStartTime = new Date(startTime);
    let parsedEndTime = new Date(endTime);

    if (isNaN(parsedStartTime.getTime()) || isNaN(parsedEndTime.getTime())) {
      throw new BadRequestException('Invalid start or end time format.');
    }
    if (parsedStartTime < new Date()) {
      throw new BadRequestException(
        'Booking start time must be in the future.',
      );
    }

    // Handle midnight crossover: if end time is earlier than start time,
    // assume end time is on the next day (common for karaoke venues)
    if (parsedEndTime <= parsedStartTime) {
      parsedEndTime = new Date(parsedEndTime.getTime() + 24 * 60 * 60 * 1000); // Add 24 hours
    }

    // After handling midnight crossover, validate that end is actually after start
    if (parsedEndTime <= parsedStartTime) {
      throw new BadRequestException(
        'Booking end time must be after start time.',
      );
    }

    const durationMinutes =
      (parsedEndTime.getTime() - parsedStartTime.getTime()) / (1000 * 60);
    if (durationMinutes < MIN_BOOKING_DURATION_MINUTES) {
      throw new BadRequestException(
        `Booking duration must be at least ${MIN_BOOKING_DURATION_MINUTES} minutes.`,
      );
    }
    if (durationMinutes > MAX_BOOKING_DURATION_HOURS * 60) {
      throw new BadRequestException(
        `Booking duration cannot exceed ${MAX_BOOKING_DURATION_HOURS} hours.`,
      );
    }

    const fetchedRoom:
      | (PrismaRoom & {
          location: {
            id: string;
            name: any;
            address: any;
            phoneNumber: string | null;
            operatingHours: any;
          };
        })
      | null = await this.prisma.room.findUnique({
      where: { id: roomId },
      include: {
        location: {
          select: {
            id: true,
            name: true,
            address: true,
            phoneNumber: true,
            operatingHours: true,
          },
        },
      },
    });

    if (!fetchedRoom) {
      throw new NotFoundException(`Room with ID "${roomId}" not found.`);
    }
    if (!fetchedRoom.isActive) {
      throw new BadRequestException(
        `Room with ID "${roomId}" is currently inactive.`,
      );
    }
    if (numberOfGuests > fetchedRoom.capacity) {
      throw new BadRequestException(
        `Number of guests (${numberOfGuests}) exceeds room capacity (${fetchedRoom.capacity}).`,
      );
    }
    const locationId = fetchedRoom.location.id;

    let user: User | null = null;
    if (userId) {
      user = await this.prisma.user.findUnique({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User with ID "${userId}" not found.`);
      }
    } else {
      if (!guestName || !guestEmail || !guestPhone) {
        throw new BadRequestException(
          'Guest name, email, and phone are required for guest bookings.',
        );
      }
    }

    const availabilityCheck =
      await this.availabilityService.checkRoomAvailability(roomId, {
        startTime: parsedStartTime.toISOString(),
        endTime: parsedEndTime.toISOString(),
      });

    if (!availabilityCheck.isGenerallyAvailable) {
      throw new ConflictException(
        availabilityCheck.reason || 'The selected time slot is not available.',
      );
    }

    // CRITICAL FIX: Create a new date object for bookingDate to avoid modifying parsedStartTime
    const bookingDateOnly = new Date(parsedStartTime);
    bookingDateOnly.setHours(0, 0, 0, 0);

    const pricePerHour =
      typeof fetchedRoom.pricePerHour === 'number'
        ? fetchedRoom.pricePerHour
        : parseFloat(fetchedRoom.pricePerHour.toString());
    if (isNaN(pricePerHour)) {
      throw new BadRequestException('Invalid price per hour for the room.');
    }
    const totalPrice = pricePerHour * (durationMinutes / 60);

    // Generate a professional booking reference
    const bookingReference =
      await this.bookingReferenceGenerator.generateElegantBookingReference();

    try {
      const newBooking = await this.prisma.booking.create({
        data: {
          bookingReference,
          roomId,
          locationId,
          userId: userId || undefined,
          startTime: parsedStartTime,
          endTime: parsedEndTime,
          bookingDate: bookingDateOnly,
          durationMinutes,
          numberOfGuests,
          totalPrice,
          currency: 'VND',
          status: SharedBookingStatus.CONFIRMED,
          guestName: userId ? undefined : guestName,
          guestEmail: userId ? undefined : guestEmail,
          guestPhone: userId ? undefined : guestPhone,
          notes: notes || undefined,
        },
        include: {
          room: { select: { name: true } },
          location: {
            select: { name: true, address: true, phoneNumber: true },
          },
          user: { select: { name: true, email: true, role: true } },
        },
      });

      // Send confirmation email
      try {
        const recipientName = userId
          ? newBooking.user?.name
          : newBooking.guestName;
        const recipientEmail = userId
          ? newBooking.user?.email
          : newBooking.guestEmail;
        const userLanguage = 'vi';

        if (recipientEmail && recipientName) {
          const frontendUrl = this.configService.get<string>('FRONTEND_URL');
          let bookingDetailsLink: string | undefined;
          if (newBooking.user?.role === UserRole.CUSTOMER && frontendUrl) {
            bookingDetailsLink = `${frontendUrl}/dashboard/bookings/${newBooking.id}`;
          } else {
            bookingDetailsLink = undefined;
          }

          const templateData = {
            userName: recipientName,
            bookingReference: newBooking.bookingReference,
            locationName:
              (newBooking.location.name as any)?.[userLanguage] ||
              newBooking.location.name,
            roomName:
              (newBooking.room.name as any)?.[userLanguage] ||
              newBooking.room.name,
            startTime: newBooking.startTime.toLocaleString(
              userLanguage === 'vi' ? 'vi-VN' : 'en-US',
              { dateStyle: 'medium', timeStyle: 'short' },
            ),
            endTime: newBooking.endTime.toLocaleString(
              userLanguage === 'vi' ? 'vi-VN' : 'en-US',
              { dateStyle: 'medium', timeStyle: 'short' },
            ),
            totalPrice: `${newBooking.totalPrice
              ?.toNumber()
              .toLocaleString(
                userLanguage === 'vi' ? 'vi-VN' : 'en-US',
              )} ${newBooking.currency}`,
            locationAddress:
              (newBooking.location.address as any)?.[userLanguage] ||
              newBooking.location.address,
            locationPhoneNumber: newBooking.location.phoneNumber,
            bookingDetailsLink: bookingDetailsLink,
            currentYear: new Date().getFullYear(),
          };

          const emailHtml = await this.emailService.renderTemplate(
            'booking-confirmation',
            userLanguage as 'vi' | 'en',
            templateData,
          );

          const emailSubject =
            userLanguage === 'vi'
              ? 'Xác nhận đặt phòng Queen Karaoke'
              : 'Queen Karaoke Booking Confirmation';

          await this.emailService.sendMail({
            to: recipientEmail,
            subject: emailSubject,
            html: emailHtml,
          });
        } else {
          console.warn(
            `Missing recipient name or email for booking ${newBooking.id}, skipping confirmation email.`,
          );
        }
      } catch (emailError) {
        console.error(
          `Failed to send confirmation email for booking ${newBooking.id}:`,
          emailError,
        );
        // Do not throw an error here to let booking succeed even if email fails
      }

      return newBooking;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException(
            'This time slot has just been booked. Please try another slot.',
          );
        }
      }
      console.error('Error creating booking:', error);
      throw new BadRequestException(
        'Could not create booking. Please try again.',
      );
    }
  }

  async findUserBookings(
    userId: string,
    query: BookingQueryDto,
  ): Promise<PaginatedResponse<Booking>> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = SortOrder.DESC,
      status,
      locationId,
      roomId,
      bookingDateFrom,
      bookingDateTo,
      bookingReference,
    } = query;

    const skip = (Number(page) - 1) * Number(limit);
    const take = Number(limit);

    const where: Prisma.BookingWhereInput = {
      userId,
    };

    if (status) {
      where.status = status;
    }
    if (locationId) {
      where.locationId = locationId;
    }
    if (roomId) {
      where.roomId = roomId;
    }
    if (bookingReference) {
      where.bookingReference = {
        contains: bookingReference,
        mode: 'insensitive', // Optional: for case-insensitive search
      };
    }
    if (bookingDateFrom || bookingDateTo) {
      where.bookingDate = {};
      if (bookingDateFrom) {
        where.bookingDate.gte = new Date(bookingDateFrom);
      }
      if (bookingDateTo) {
        // Adjust to include the whole day for 'to' date
        const toDate = new Date(bookingDateTo);
        toDate.setHours(23, 59, 59, 999);
        where.bookingDate.lte = toDate;
      }
    }

    const bookings = await this.prisma.booking.findMany({
      where,
      skip,
      take,
      orderBy: {
        [sortBy]: sortOrder,
      },
      // Optionally include related data like room name, location name
      include: {
        room: { select: { name: true, id: true } },
        location: { select: { name: true, id: true } },
      },
    });

    const total = await this.prisma.booking.count({ where });

    return {
      items: bookings,
      total,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(total / Number(limit)),
    };
  }

  async findOneBooking(
    bookingId: string,
    requestingUserId?: string,
  ): Promise<Booking | null> {
    const where: Prisma.BookingWhereUniqueInput = { id: bookingId };

    if (requestingUserId) {
      where.userId = requestingUserId;
    }

    const booking = await this.prisma.booking.findUnique({
      where,
      include: {
        room: {
          select: {
            id: true,
            name: true,
            theme: true,
            description: true,
            capacity: true,
            pricePerHour: true,
            images: true,
            amenities: true,
            roomType: true,
            isActive: true,
          },
        },
        location: {
          select: {
            id: true,
            name: true,
            address: true,
            description: true,
            imageUrl: true,
            phoneNumber: true,
            email: true,
            operatingHours: true,
            isActive: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phoneNumber: true,
            role: true,
            lastLogin: true,
          },
        },
        payment: {
          select: {
            id: true,
            amount: true,
            currency: true,
            paymentGateway: true,
            transactionId: true,
            paymentIntentId: true,
            status: true,
            paymentMethod: true,
            paidAt: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
    });

    if (!booking) {
      let message = `Booking with ID "${bookingId}" not found.`;
      if (requestingUserId) {
        message = `Booking with ID "${bookingId}" not found or does not belong to the user.`;
      }
      throw new NotFoundException(message);
    }

    return booking;
  }

  async updateBookingStatus(
    bookingId: string,
    newStatus: SharedBookingStatus,
    requestingUserId?: string,
    notes?: string,
  ): Promise<Booking> {
    const findConditions: Prisma.BookingWhereUniqueInput = { id: bookingId };
    if (requestingUserId) {
      findConditions.userId = requestingUserId;
    }

    const booking = await this.prisma.booking.findUnique({
      where: findConditions,
    });

    if (!booking) {
      throw new NotFoundException(
        `Booking with ID "${bookingId}" not found or user does not have permission.`,
      );
    }

    const currentStatus = booking.status as SharedBookingStatus;

    let allowUpdate = false;

    if (requestingUserId) {
      if (newStatus === SharedBookingStatus.CANCELLED_BY_USER) {
        if (
          [
            SharedBookingStatus.PENDING_PAYMENT,
            SharedBookingStatus.CONFIRMED,
          ].includes(currentStatus)
        ) {
          allowUpdate = true;
        }
      }
    } else {
      if (
        currentStatus === SharedBookingStatus.COMPLETED &&
        newStatus !== SharedBookingStatus.COMPLETED
      ) {
        allowUpdate = false;
      } else if (
        currentStatus === SharedBookingStatus.NO_SHOW &&
        newStatus !== SharedBookingStatus.NO_SHOW
      ) {
        allowUpdate = false;
      } else {
        allowUpdate = true;
      }
    }

    if (!allowUpdate) {
      throw new BadRequestException(
        `Cannot change booking status from ${currentStatus} to ${newStatus}.`,
      );
    }

    const dataToUpdate: Prisma.BookingUpdateInput = {
      status: newStatus as any,
    };
    if (notes) {
      if (requestingUserId) {
        dataToUpdate.notes = notes;
      } else {
        dataToUpdate.adminNotes = notes;
      }
    }

    return this.prisma.booking.update({
      where: { id: bookingId },
      data: dataToUpdate,
    });
  }

  async findAllBookings(
    query: BookingQueryDto,
  ): Promise<PaginatedResponse<Booking>> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = SortOrder.DESC,
      status,
      locationId,
      roomId,
      userId, // Admins can filter by userId
      bookingDateFrom,
      bookingDateTo,
      bookingReference,
      search,
    } = query;

    const skip = (Number(page) - 1) * Number(limit);
    const take = Number(limit);

    const where: Prisma.BookingWhereInput = {};

    if (userId) {
      where.userId = userId;
    }
    if (status) {
      where.status = status;
    }
    if (locationId) {
      where.locationId = locationId;
    }
    if (roomId) {
      where.roomId = roomId;
    }

    if (search) {
      const searchCondition = {
        contains: search,
        mode: 'insensitive' as Prisma.QueryMode,
      };
      where.OR = [
        { bookingReference: searchCondition },
        { guestName: searchCondition },
        { guestEmail: searchCondition },
        { user: { name: searchCondition } },
        { user: { email: searchCondition } },
      ];
    }

    if (bookingReference) {
      const specificBookingRefCondition = {
        equals: bookingReference,
        mode: 'insensitive' as Prisma.QueryMode,
      };

      if (where.OR) {
        // General search exists, so AND with specific bookingReference
        where.AND = [
          { OR: where.OR },
          { bookingReference: specificBookingRefCondition },
        ];
        delete where.OR; // OR conditions are now part of the AND block
      } else {
        // No general search, so just apply specific bookingReference filter
        where.bookingReference = specificBookingRefCondition;
      }
    }

    if (bookingDateFrom || bookingDateTo) {
      where.bookingDate = {};
      if (bookingDateFrom) {
        where.bookingDate.gte = new Date(bookingDateFrom);
      }
      if (bookingDateTo) {
        const toDate = new Date(bookingDateTo);
        toDate.setHours(23, 59, 59, 999);
        where.bookingDate.lte = toDate;
      }
    }

    const bookings = await this.prisma.booking.findMany({
      where,
      skip,
      take,
      orderBy: {
        [sortBy]: sortOrder,
      },
      include: {
        room: {
          select: {
            id: true,
            name: true,
            theme: true,
            description: true,
            capacity: true,
            pricePerHour: true,
            images: true,
            amenities: true,
            roomType: true,
            isActive: true,
          },
        },
        location: {
          select: {
            id: true,
            name: true,
            address: true,
            description: true,
            imageUrl: true,
            phoneNumber: true,
            email: true,
            operatingHours: true,
            isActive: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phoneNumber: true,
            role: true,
            lastLogin: true,
          },
        },
        payment: {
          select: {
            id: true,
            amount: true,
            currency: true,
            paymentGateway: true,
            transactionId: true,
            paymentIntentId: true,
            status: true,
            paymentMethod: true,
            paidAt: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
    });

    const total = await this.prisma.booking.count({ where });

    return {
      items: bookings,
      total,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(total / Number(limit)),
    };
  }

  async comprehensivelyUpdateBooking(
    bookingId: string,
    updateData: AdminUpdateBookingDto,
    requestingUserId?: string,
  ): Promise<Booking> {
    const findConditions: Prisma.BookingWhereUniqueInput = { id: bookingId };
    if (requestingUserId) {
      findConditions.userId = requestingUserId;
    }

    const existingBooking = await this.prisma.booking.findUnique({
      where: findConditions,
      include: {
        room: { select: { id: true, capacity: true, pricePerHour: true } },
      },
    });

    if (!existingBooking) {
      throw new NotFoundException(
        `Booking with ID "${bookingId}" not found or user does not have permission.`,
      );
    }

    // Prepare the update data
    const dataToUpdate: Prisma.BookingUpdateInput = {};

    // Handle time updates
    if (updateData.startTime || updateData.endTime) {
      const parsedStartTime = updateData.startTime
        ? new Date(updateData.startTime)
        : new Date(existingBooking.startTime);
      let parsedEndTime = updateData.endTime
        ? new Date(updateData.endTime)
        : new Date(existingBooking.endTime);

      // Validate time formats
      if (isNaN(parsedStartTime.getTime()) || isNaN(parsedEndTime.getTime())) {
        throw new BadRequestException('Invalid start or end time format.');
      }

      // Handle midnight crossover: if end time is earlier than start time,
      // assume end time is on the next day (common for karaoke venues)
      if (parsedEndTime <= parsedStartTime) {
        parsedEndTime = new Date(parsedEndTime.getTime() + 24 * 60 * 60 * 1000); // Add 24 hours
      }

      // After handling midnight crossover, validate time logic
      if (parsedEndTime <= parsedStartTime) {
        throw new BadRequestException(
          'Booking end time must be after start time.',
        );
      }

      const durationMinutes =
        (parsedEndTime.getTime() - parsedStartTime.getTime()) / (1000 * 60);

      if (durationMinutes < MIN_BOOKING_DURATION_MINUTES) {
        throw new BadRequestException(
          `Booking duration must be at least ${MIN_BOOKING_DURATION_MINUTES} minutes.`,
        );
      }

      if (durationMinutes > MAX_BOOKING_DURATION_HOURS * 60) {
        throw new BadRequestException(
          `Booking duration cannot exceed ${MAX_BOOKING_DURATION_HOURS} hours.`,
        );
      }

      // Check availability for new time slot (excluding current booking)
      const availabilityCheck =
        await this.availabilityService.checkRoomAvailability(
          existingBooking.roomId,
          {
            startTime: parsedStartTime.toISOString(),
            endTime: parsedEndTime.toISOString(),
            excludeBookingId: bookingId,
          },
        );

      if (!availabilityCheck.isGenerallyAvailable) {
        throw new ConflictException(
          availabilityCheck.reason ||
            'The selected time slot is not available.',
        );
      }

      // Update time-related fields
      dataToUpdate.startTime = parsedStartTime;
      dataToUpdate.endTime = parsedEndTime;
      dataToUpdate.durationMinutes = durationMinutes;

      // CRITICAL FIX: Create a new date object for bookingDate to avoid modifying parsedStartTime
      const bookingDateOnly = new Date(parsedStartTime);
      bookingDateOnly.setHours(0, 0, 0, 0);
      dataToUpdate.bookingDate = bookingDateOnly;

      // Recalculate total price if time changed
      const pricePerHour =
        typeof existingBooking.room.pricePerHour === 'number'
          ? existingBooking.room.pricePerHour
          : parseFloat(existingBooking.room.pricePerHour.toString());

      if (!isNaN(pricePerHour)) {
        dataToUpdate.totalPrice = pricePerHour * (durationMinutes / 60);
      }
    }

    // Handle guest number updates
    if (updateData.numberOfGuests !== undefined) {
      if (updateData.numberOfGuests > existingBooking.room.capacity) {
        throw new BadRequestException(
          `Number of guests (${updateData.numberOfGuests}) exceeds room capacity (${existingBooking.room.capacity}).`,
        );
      }
      dataToUpdate.numberOfGuests = updateData.numberOfGuests;
    }

    // Handle status updates
    if (updateData.status) {
      dataToUpdate.status = updateData.status as any;
    }

    // Handle notes updates
    if (updateData.notes !== undefined) {
      dataToUpdate.notes = updateData.notes;
    }

    if (updateData.adminNotes !== undefined) {
      dataToUpdate.adminNotes = updateData.adminNotes;
    }

    try {
      return await this.prisma.booking.update({
        where: { id: bookingId },
        data: dataToUpdate,
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException(
            'This time slot has just been booked. Please try another slot.',
          );
        }
      }
      console.error('Error updating booking:', error);
      throw new BadRequestException(
        'Could not update booking. Please try again.',
      );
    }
  }

  @Cron(CronExpression.EVERY_HOUR)
  async handleBookingReminders() {
    this.logger.log('Running booking reminder cron job');

    const now = new Date();
    const reminderWindowStart = new Date(now);
    reminderWindowStart.setHours(now.getHours() + 24);

    const bookingsToRemind =
      (await this.prisma.booking.findMany<Prisma.BookingFindManyArgs>({
        where: {
          startTime: {
            gte: now,
            lte: reminderWindowStart,
          },
          status: SharedBookingStatus.CONFIRMED,
          isReminderSent: false,
        },
        include: {
          user: { select: { name: true, email: true } }, // Removed preferredLanguage
          // guestEmail and guestName are direct fields, no need to include if not relations
          room: { select: { name: true } },
          location: { select: { name: true, address: true } },
        },
      })) as BookingWithRelationsForReminder[]; // Assert the type here

    if (bookingsToRemind.length === 0) {
      this.logger.log('No bookings found requiring a reminder at this time.');
      return;
    }

    this.logger.log(`Found ${bookingsToRemind.length} bookings to remind.`);

    for (const booking of bookingsToRemind) {
      const recipientName = booking.user
        ? booking.user.name
        : booking.guestName;
      const recipientEmail = booking.user
        ? booking.user.email
        : booking.guestEmail;
      const userLanguage = 'vi'; // Defaulting to Vietnamese for now

      if (!recipientEmail || !recipientName) {
        this.logger.warn(
          `Booking ${booking.id} missing recipient name or email, skipping reminder.`,
        );
        continue;
      }

      try {
        // Ensure that booking.location and booking.room are not null/undefined before accessing name
        const locationName = booking.location
          ? (booking.location.name as any)?.[userLanguage] ||
            booking.location.name
          : 'N/A';
        const roomName = booking.room
          ? (booking.room.name as any)?.[userLanguage] || booking.room.name
          : 'N/A';
        const locationAddress = booking.location
          ? (booking.location.address as any)?.[userLanguage] ||
            booking.location.address
          : 'N/A';

        const templateData = {
          userName: recipientName,
          bookingReference: booking.bookingReference,
          locationName: locationName,
          roomName: roomName,
          startTime: booking.startTime.toLocaleString(
            userLanguage === 'vi' ? 'vi-VN' : 'en-US',
            { dateStyle: 'medium', timeStyle: 'short' },
          ),
          locationAddress: locationAddress,
          currentYear: new Date().getFullYear(),
        };

        const emailHtml = await this.emailService.renderTemplate(
          'booking-reminder',
          userLanguage,
          templateData,
        );

        const emailSubject =
          userLanguage === 'vi'
            ? 'Nhắc nhở lịch hẹn Queen Karaoke của bạn'
            : 'Your Queen Karaoke Booking Reminder';

        await this.emailService.sendMail({
          to: recipientEmail,
          subject: emailSubject,
          html: emailHtml,
        });

        // Mark reminder as sent
        await this.prisma.booking.update({
          where: { id: booking.id },
          data: { isReminderSent: true },
        });

        this.logger.log(
          `Reminder sent for booking ${booking.id} to ${recipientEmail}`,
        );
      } catch (error) {
        this.logger.error(
          `Failed to send reminder for booking ${booking.id}:`,
          error,
        );
        // Continue to next booking even if one fails
      }
    }
    this.logger.log('Booking reminder cron job finished.');
  }
}
