'use client';

import {
  ChevronDown,
  MapPin,
  Users,
  Eye,
  Clock,
  DollarSign,
} from 'lucide-react';
import { Location, Room, LocalizedString } from 'shared-types';
import { roomsService } from '../../services/rooms.service';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';

interface BookingFormData {
  locationId: string;
  roomId: string;
  startTime: string;
  endTime: string;
  numberOfGuests: number;
  isGuestBooking: boolean;
  userId?: string;
  guestName?: string;
  guestEmail?: string;
  guestPhone?: string;
  notes?: string;
}

interface BookingFormLocationProps {
  formData: BookingFormData;
  locations: Location[];
  rooms: Room[];
  selectedLocation: Location | null;
  selectedRoom: Room | null;
  errors: Partial<Record<keyof BookingFormData, string>>;
  loading: boolean;
  onUpdate: (updates: Partial<BookingFormData>) => void;
}

// Helper function to get localized text
const getLocalizedText = (
  localizedString: LocalizedString | undefined | null
): string => {
  if (!localizedString) return '';
  return localizedString.vi || localizedString.en || '';
};

const BookingFormLocation: React.FC<BookingFormLocationProps> = ({
  formData,
  locations,
  rooms,
  selectedLocation,
  selectedRoom,
  errors,
  loading,
  onUpdate,
}) => {
  const handleLocationChange = (locationId: string) => {
    onUpdate({ locationId, roomId: '' }); // Reset room when location changes
  };

  const handleRoomChange = (roomId: string) => {
    onUpdate({ roomId });
  };

  const formatPrice = (price: number | string): string => {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(numPrice);
  };

  const formatOperatingHours = (operatingHours: any): string => {
    if (!operatingHours) return 'Không xác định';

    try {
      const hours =
        typeof operatingHours === 'string'
          ? JSON.parse(operatingHours)
          : operatingHours;

      // Check if it's the new format: { "monday": "18:00-02:00", "tuesday": "18:00-02:00", ... }
      if (hours && typeof hours === 'object') {
        const dayNames = [
          'monday',
          'tuesday',
          'wednesday',
          'thursday',
          'friday',
          'saturday',
          'sunday',
        ];
        const viDayNames = [
          'Thứ Hai',
          'Thứ Ba',
          'Thứ Tư',
          'Thứ Năm',
          'Thứ Sáu',
          'Thứ Bảy',
          'Chủ Nhật',
        ];

        // Find the most common time range to show as general hours
        const timeRanges = Object.values(hours).filter(Boolean) as string[];
        if (timeRanges.length > 0) {
          // Show the most common range
          const rangeCounts = timeRanges.reduce(
            (acc: Record<string, number>, range) => {
              acc[range] = (acc[range] || 0) + 1;
              return acc;
            },
            {}
          );

          const mostCommonRange = Object.keys(rangeCounts).reduce((a, b) =>
            rangeCounts[a] > rangeCounts[b] ? a : b
          );

          return `${mostCommonRange} (xem chi tiết)`;
        }
      }

      // Legacy format support
      if (hours.openTime && hours.closeTime) {
        return `${hours.openTime} - ${hours.closeTime}`;
      }

      return 'Không xác định';
    } catch (error) {
      return 'Không xác định';
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-h3 font-semibold text-gray-900 mb-2">
          Chọn chi nhánh và phòng
        </h2>
        <p className="text-base text-gray-600">
          Chọn chi nhánh và phòng muốn đặt cho khách hàng
        </p>
      </div>

      {/* Location Selection */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Chi nhánh <span className="text-red-500">*</span>
          </label>
          <Select
            value={formData.locationId}
            onValueChange={handleLocationChange}
          >
            <SelectTrigger
              className={`w-full ${
                errors.locationId ? 'border-red-500' : 'border-gray-300'
              } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
              disabled={loading}
            >
              <div className="flex items-center gap-2">
                <MapPin className="w-5 h-5 text-gray-400" />
                {formData.locationId && selectedLocation ? (
                  <span className="text-black">
                    {getLocalizedText(selectedLocation.name)}
                  </span>
                ) : (
                  <span className="text-gray-500">
                    {loading ? 'Đang tải chi nhánh...' : 'Chọn chi nhánh...'}
                  </span>
                )}
              </div>
            </SelectTrigger>
            <SelectContent>
              {loading ? (
                <div className="flex items-center justify-center py-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gold-600"></div>
                  <span className="ml-2 text-sm text-gray-500">
                    Đang tải...
                  </span>
                </div>
              ) : locations.length === 0 ? (
                <div className="py-2 px-4 text-sm text-gray-500">
                  Không có chi nhánh nào
                </div>
              ) : (
                locations.map(location => (
                  <SelectItem key={location.id} value={location.id}>
                    {getLocalizedText(location.name)}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
          {errors.locationId && (
            <p className="mt-1 text-sm text-red-600">{errors.locationId}</p>
          )}
        </div>

        {/* Location Details */}
        {selectedLocation && (
          <div className="bg-gray-50 rounded-lg p-4 border">
            <div className="flex items-start gap-4">
              {selectedLocation.imageUrl && (
                <img
                  src={selectedLocation.imageUrl}
                  alt={getLocalizedText(selectedLocation.name)}
                  className="w-20 h-20 rounded-lg object-cover flex-shrink-0"
                />
              )}
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">
                  {getLocalizedText(selectedLocation.name)}
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  {selectedLocation.address}
                </p>
                <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                  <span className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    {formatOperatingHours(selectedLocation.operatingHours)}
                  </span>
                  {selectedLocation.phoneNumber && (
                    <span>📞 {selectedLocation.phoneNumber}</span>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Room Selection */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Phòng <span className="text-red-500">*</span>
          </label>
          {!formData.locationId ? (
            <div className="border border-gray-300 rounded-lg p-4 text-center text-gray-500">
              Vui lòng chọn chi nhánh trước
            </div>
          ) : loading ? (
            <div className="border border-gray-300 rounded-lg p-4 text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gold-600 mx-auto"></div>
              <span className="text-gray-500 text-sm mt-2">
                Đang tải phòng...
              </span>
            </div>
          ) : rooms.length === 0 ? (
            <div className="border border-gray-300 rounded-lg p-4 text-center text-gray-500">
              Không có phòng nào khả dụng tại chi nhánh này
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {rooms.map(room => (
                <div
                  key={room.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                    formData.roomId === room.id
                      ? 'border-gold-500 bg-gold-50 ring-2 ring-gold-200'
                      : 'border-gray-300 bg-white hover:border-gray-400 hover:shadow-md'
                  }`}
                  onClick={() => handleRoomChange(room.id)}
                >
                  <div className="flex items-start gap-3">
                    {room.images && room.images.length > 0 && (
                      <img
                        src={room.images[0]}
                        alt={getLocalizedText(room.name)}
                        className="w-16 h-16 rounded-lg object-cover flex-shrink-0"
                      />
                    )}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <h3
                          className={`font-medium text-sm ${
                            formData.roomId === room.id
                              ? 'text-gold-700'
                              : 'text-gray-900'
                          }`}
                        >
                          {getLocalizedText(room.name)}
                        </h3>
                        <span
                          className={`px-2 py-1 text-xs rounded-full ${
                            roomsService.getRoomTypeColor(room.roomType) ===
                            'queens'
                              ? 'bg-purple-100 text-purple-800'
                              : roomsService.getRoomTypeColor(room.roomType) ===
                                  'ruby'
                                ? 'bg-red-100 text-red-800'
                                : roomsService.getRoomTypeColor(
                                      room.roomType
                                    ) === 'sapphire'
                                  ? 'bg-blue-100 text-blue-800'
                                  : roomsService.getRoomTypeColor(
                                        room.roomType
                                      ) === 'opal'
                                    ? 'bg-indigo-100 text-indigo-800'
                                    : roomsService.getRoomTypeColor(
                                          room.roomType
                                        ) === 'pearl'
                                      ? 'bg-gray-100 text-gray-800'
                                      : roomsService.getRoomTypeColor(
                                            room.roomType
                                          ) === 'hall'
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          {roomsService.getRoomTypeDisplayName(room.roomType)}
                        </span>
                      </div>

                      <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <Users className="w-3 h-3" />
                          {room.capacity} người
                        </span>
                        <span className="flex items-center gap-1">
                          <DollarSign className="w-3 h-3" />
                          {formatPrice(room.pricePerHour)}/giờ
                        </span>
                      </div>

                      {room.description && (
                        <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                          {getLocalizedText(room.description)}
                        </p>
                      )}
                    </div>
                  </div>

                  {formData.roomId === room.id && (
                    <div className="mt-3 pt-3 border-t border-gold-200">
                      <div className="flex items-center justify-center">
                        <div className="w-6 h-6 bg-gold-600 rounded-full flex items-center justify-center">
                          <svg
                            className="w-4 h-4 text-white"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
          {errors.roomId && (
            <p className="mt-1 text-sm text-red-600">{errors.roomId}</p>
          )}
        </div>

        {/* Room Details */}
        {selectedRoom && (
          <div className="bg-gray-50 rounded-lg p-4 border">
            <h4 className="font-medium text-gray-900 mb-3">
              Chi tiết phòng đã chọn
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Loại phòng:</span>
                <div className="font-medium text-gray-900">
                  {roomsService.getRoomTypeDisplayName(selectedRoom.roomType)}
                </div>
              </div>
              <div>
                <span className="text-gray-500">Sức chứa:</span>
                <div className="font-medium text-gray-900">
                  {selectedRoom.capacity} người
                </div>
              </div>
              <div>
                <span className="text-gray-500">Giá mỗi giờ:</span>
                <div className="font-medium text-gray-900">
                  {formatPrice(selectedRoom.pricePerHour)}
                </div>
              </div>
              <div>
                <span className="text-gray-500">Trạng thái:</span>
                <div
                  className={`font-medium ${
                    selectedRoom.isActive ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {selectedRoom.isActive ? 'Hoạt động' : 'Không hoạt động'}
                </div>
              </div>
            </div>

            {selectedRoom.description && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <span className="text-gray-500 text-sm">Mô tả:</span>
                <p className="text-gray-900 text-sm mt-1">
                  {getLocalizedText(selectedRoom.description)}
                </p>
              </div>
            )}

            {selectedRoom.images && selectedRoom.images.length > 1 && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <span className="text-gray-500 text-sm mb-2 block">
                  Hình ảnh phòng:
                </span>
                <div className="flex gap-2 overflow-x-auto">
                  {selectedRoom.images.slice(0, 4).map((image, index) => (
                    <img
                      key={index}
                      src={image}
                      alt={`${getLocalizedText(selectedRoom.name)} - ${index + 1}`}
                      className="w-20 h-20 rounded-lg object-cover flex-shrink-0"
                    />
                  ))}
                  {selectedRoom.images.length > 4 && (
                    <div className="w-20 h-20 rounded-lg bg-gray-200 flex items-center justify-center text-gray-500 text-xs flex-shrink-0">
                      +{selectedRoom.images.length - 4}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default BookingFormLocation;
