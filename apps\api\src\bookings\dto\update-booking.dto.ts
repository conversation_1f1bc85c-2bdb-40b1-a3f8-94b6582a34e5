import {
  <PERSON><PERSON>ption<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IsNotEmpty,
  IsDateString,
  IsInt,
  Min,
} from 'class-validator';
import { BookingStatus } from '@shared-types/common.types'; // Assuming BookingStatus enum is in common.types

export class UpdateBookingDto {
  @IsOptional()
  @IsDateString(
    {},
    { message: 'Start time must be a valid ISO 8601 date string' },
  )
  startTime?: string;

  @IsOptional()
  @IsDateString(
    {},
    { message: 'End time must be a valid ISO 8601 date string' },
  )
  endTime?: string;

  @IsOptional()
  @IsInt({ message: 'Number of guests must be an integer' })
  @Min(1, { message: 'Number of guests must be at least 1' })
  numberOfGuests?: number;

  @IsOptional()
  @IsEnum(BookingStatus, { message: 'Status must be a valid BookingStatus' })
  status?: BookingStatus;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string; // For user to add/update notes

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  adminNotes?: string; // For admin to add/update internal notes
}

// Specific DTO for user cancelling their booking
export class CancelBookingDto {
  @IsNotEmpty({ message: 'Status is required for cancellation' })
  @IsEnum([BookingStatus.CANCELLED_BY_USER], {
    message: 'Status must be CANCELLED_BY_USER for user cancellation',
  })
  status: BookingStatus.CANCELLED_BY_USER;

  @IsOptional()
  @IsString()
  @MaxLength(500)
  cancellationReason?: string; // Optional reason from user
}

// Specific DTO for admin updating booking status
export class AdminUpdateBookingStatusDto {
  @IsNotEmpty({ message: 'Status is required' })
  @IsEnum(BookingStatus, { message: 'Status must be a valid BookingStatus' })
  status: BookingStatus;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  adminNotes?: string;
}

// Comprehensive DTO for admin updating any booking field
export class AdminUpdateBookingDto {
  @IsOptional()
  @IsDateString(
    {},
    { message: 'Start time must be a valid ISO 8601 date string' },
  )
  startTime?: string;

  @IsOptional()
  @IsDateString(
    {},
    { message: 'End time must be a valid ISO 8601 date string' },
  )
  endTime?: string;

  @IsOptional()
  @IsInt({ message: 'Number of guests must be an integer' })
  @Min(1, { message: 'Number of guests must be at least 1' })
  numberOfGuests?: number;

  @IsOptional()
  @IsEnum(BookingStatus, { message: 'Status must be a valid BookingStatus' })
  status?: BookingStatus;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  adminNotes?: string;
}
