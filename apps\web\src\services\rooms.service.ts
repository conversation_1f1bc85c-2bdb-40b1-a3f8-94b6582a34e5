import {
  Room,
  CreateRoomDto,
  UpdateRoomDto,
  RoomQueryDto,
  PaginatedResponse,
  RoomTypeEnum,
  DecorStyle,
} from 'shared-types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

class RoomsService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('auth-token');
    return {
      'Content-Type': 'application/json',
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    };
  }

  private getAuthHeadersForFormData(): HeadersInit {
    const token = localStorage.getItem('auth-token');
    return {
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    };
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorText = await response.text();
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

      try {
        const errorData = JSON.parse(errorText);
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        // Keep default message if JSON parsing fails
      }

      throw new Error(errorMessage);
    }

    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();
      return data.data || data; // Handle both wrapped and direct responses
    }

    return response.text() as any;
  }

  // Create a new room
  async createRoom(locationId: string, roomData: CreateRoomDto): Promise<Room> {
    const response = await fetch(
      `${API_BASE_URL}/locations/${locationId}/rooms`,
      {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(roomData),
      }
    );

    return this.handleResponse<Room>(response);
  }

  // Get all rooms for a location with optional filters
  async getRooms(locationId: string, query?: RoomQueryDto): Promise<Room[]> {
    const params = new URLSearchParams();

    if (query) {
      Object.entries(query).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const url = `${API_BASE_URL}/locations/${locationId}/rooms${params.toString() ? `?${params.toString()}` : ''}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<Room[]>(response);
  }

  // Get all rooms across all locations with optional filters (admin view)
  async getAllRooms(
    query?: RoomQueryDto & { locationId?: string }
  ): Promise<
    PaginatedResponse<Room & { location?: { id: string; name: any } }>
  > {
    const params = new URLSearchParams();

    if (query) {
      Object.entries(query).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    // We'll need to create a custom endpoint for this or iterate through locations
    // For now, let's create a method that fetches rooms from all locations
    throw new Error(
      'Global room listing not yet implemented - use getRooms with specific locationId'
    );
  }

  // Get a specific room
  async getRoom(locationId: string, roomId: string): Promise<Room> {
    const response = await fetch(
      `${API_BASE_URL}/locations/${locationId}/rooms/${roomId}`,
      {
        method: 'GET',
        headers: this.getAuthHeaders(),
      }
    );

    return this.handleResponse<Room>(response);
  }

  // Update a room
  async updateRoom(
    locationId: string,
    roomId: string,
    roomData: UpdateRoomDto
  ): Promise<Room> {
    const response = await fetch(
      `${API_BASE_URL}/locations/${locationId}/rooms/${roomId}`,
      {
        method: 'PATCH',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(roomData),
      }
    );

    return this.handleResponse<Room>(response);
  }

  // Soft delete a room (Super Admin only)
  async deleteRoom(locationId: string, roomId: string): Promise<Room> {
    const response = await fetch(
      `${API_BASE_URL}/locations/${locationId}/rooms/${roomId}`,
      {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
      }
    );

    return this.handleResponse<Room>(response);
  }

  // Upload room images
  async uploadRoomImages(
    locationId: string,
    roomId: string,
    images: File[]
  ): Promise<Room> {
    const formData = new FormData();

    images.forEach((image, index) => {
      formData.append('images', image);
    });

    const response = await fetch(
      `${API_BASE_URL}/locations/${locationId}/rooms/${roomId}/images`,
      {
        method: 'POST',
        headers: this.getAuthHeadersForFormData(),
        body: formData,
      }
    );

    return this.handleResponse<Room>(response);
  }

  // Helper methods for room type styling
  getRoomTypeColor(roomType?: RoomTypeEnum): string {
    switch (roomType) {
      case RoomTypeEnum.QUEENS_EYES:
        return 'queens';
      case RoomTypeEnum.RUBY:
        return 'ruby';
      case RoomTypeEnum.SAPPHIRE:
        return 'sapphire';
      case RoomTypeEnum.OPAL:
        return 'opal';
      case RoomTypeEnum.PEARL:
        return 'pearl';
      case RoomTypeEnum.HALL:
        return 'hall';
      case RoomTypeEnum.STANDARD:
      default:
        return 'standard';
    }
  }

  getRoomTypeDisplayName(roomType?: RoomTypeEnum): string {
    switch (roomType) {
      case RoomTypeEnum.QUEENS_EYES:
        return 'Queens Eyes';
      case RoomTypeEnum.RUBY:
        return 'Ruby';
      case RoomTypeEnum.SAPPHIRE:
        return 'Sapphire';
      case RoomTypeEnum.OPAL:
        return 'Opal';
      case RoomTypeEnum.PEARL:
        return 'Pearl';
      case RoomTypeEnum.HALL:
        return 'Hall';
      case RoomTypeEnum.STANDARD:
      default:
        return 'Standard';
    }
  }

  // Format room price for display
  formatPrice(price: number | string): string {
    const numericPrice = typeof price === 'string' ? parseFloat(price) : price;
    if (isNaN(numericPrice)) {
      console.error('Invalid price value for formatting:', price);
      return 'N/A VND';
    }
    return numericPrice.toLocaleString('vi-VN') + ' VND';
  }

  // Get room display name from localized content
  getRoomDisplayName(room: Room): string {
    return room.name?.vi || room.name?.en || 'Không có tên';
  }

  // Get room display description from localized content
  getRoomDisplayDescription(room: Room): string {
    return room.description?.vi || room.description?.en || '';
  }

  getDecorStyleDisplayName(style: DecorStyle): string {
    switch (style) {
      case DecorStyle.MODERN:
        return 'Hiện Đại';
      case DecorStyle.CLASSIC:
        return 'Cổ Điển';
      default:
        return 'Không xác định';
    }
  }
}

export const roomsService = new RoomsService();
