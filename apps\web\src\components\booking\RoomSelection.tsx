'use client';

import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  Users,
  Star,
  Tv,
  Music,
  Wifi,
  ChevronRight,
  X,
  ChevronLeft,
  ChevronUp,
  ChevronDown,
  Clock,
  Sparkles,
  Expand,
} from 'lucide-react';
import { RoomTypeEnum, DecorStyle } from 'shared-types';
import type { Room, Location, LocalizedString } from 'shared-types';
import { Button } from '../ui/button';
import { useState, useEffect } from 'react';
import { roomsService } from '../../services/rooms.service';
import { Modal, ModalBody } from '../ui/modal';
import { ImageGallery } from '../ui/image-gallery';

interface RoomSelectionProps {
  selectedLocation: Location;
  onRoomSelect: (room: Room) => void;
  onBack: () => void;
}

type CapacityRange = '< 10' | '10-15' | '15-20' | '> 20';
type SelectionStep = 'capacity' | 'style' | 'rooms';

interface RoomFilter {
  capacity: CapacityRange | null;
  style: DecorStyle | null;
}

// Animation variants
const pageTransition = {
  initial: {
    opacity: 0,
    x: 20,
  },
  animate: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.3,
      ease: [0.25, 0.46, 0.45, 0.94],
      when: 'beforeChildren',
      staggerChildren: 0.05,
    },
  },
  exit: {
    opacity: 0,
    x: -20,
    transition: {
      duration: 0.2,
      ease: [0.25, 0.46, 0.45, 0.94],
      when: 'afterChildren',
      staggerChildren: 0.03,
      staggerDirection: -1,
    },
  },
};

const staggerContainer = {
  hidden: {
    opacity: 0,
    transition: {
      when: 'afterChildren',
    },
  },
  visible: {
    opacity: 1,
    transition: {
      when: 'beforeChildren',
      staggerChildren: 0.05,
      delayChildren: 0.1,
      duration: 0.3,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
  exit: {
    opacity: 0,
    transition: {
      when: 'afterChildren',
      staggerChildren: 0.03,
      staggerDirection: -1,
      duration: 0.2,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

const chatBubbleVariants = {
  hidden: {
    opacity: 0,
    y: 20,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.15,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
  exit: {
    opacity: 0,
    y: -20,
    scale: 0.95,
    transition: {
      duration: 0.12,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

const optionVariants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.12,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
  hover: {
    scale: 1.02,
    y: -5,
    transition: {
      duration: 0.1,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
  tap: {
    scale: 0.98,
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: {
      duration: 0.08,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

const shimmerAnimation = {
  initial: {
    backgroundPosition: '200% 0',
    opacity: 0.4,
  },
  animate: {
    backgroundPosition: ['-200% 0', '200% 0'],
    opacity: [0.4, 0.8, 0.4],
    transition: {
      duration: 4,
      ease: 'linear',
      repeat: Infinity,
      opacity: {
        duration: 2,
        ease: 'easeInOut',
        repeat: Infinity,
      },
    },
  },
};

const roomCardVariants = {
  initial: {
    opacity: 0,
    y: 10,
    scale: 0.98,
  },
  animate: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.4,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
  hover: {
    y: -5,
    scale: 1.02,
    transition: {
      duration: 0.3,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
  tap: {
    scale: 0.98,
    transition: {
      duration: 0.2,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
  exit: {
    opacity: 0,
    y: -10,
    scale: 0.98,
    transition: {
      duration: 0.3,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

const roomsContainerVariants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      when: 'beforeChildren',
      staggerChildren: 0.08,
      delayChildren: 0.1,
      duration: 0.4,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
  exit: {
    opacity: 0,
    transition: {
      when: 'afterChildren',
      staggerChildren: 0.05,
      staggerDirection: -1,
      duration: 0.3,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

const getRoomTypeColor = (roomType: RoomTypeEnum) => {
  switch (roomType) {
    case RoomTypeEnum.QUEENS_EYES:
      return 'from-[#9A6C2E] via-[#E6B325] to-[#9A6C2E]';
    case RoomTypeEnum.RUBY:
      return 'from-red-600 via-red-500 to-red-600';
    case RoomTypeEnum.SAPPHIRE:
      return 'from-blue-600 via-blue-500 to-blue-600';
    case RoomTypeEnum.OPAL:
      return 'from-teal-600 via-teal-500 to-teal-600';
    case RoomTypeEnum.PEARL:
      return 'from-purple-600 via-purple-500 to-purple-600';
    default:
      return 'from-gray-600 via-gray-500 to-gray-600';
  }
};

const capacityRanges: CapacityRange[] = ['< 10', '10-15', '15-20', '> 20'];
const decorStyles = [DecorStyle.MODERN, DecorStyle.CLASSIC];

// Add this constant at the top with other animation variants
const layoutTransition = {
  layout: {
    duration: 0.3,
    ease: [0.25, 0.46, 0.45, 0.94],
  },
};

const ITEMS_PER_PAGE = 6;

export function RoomSelection({
  selectedLocation,
  onRoomSelect,
  onBack,
}: RoomSelectionProps) {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [filteredRooms, setFilteredRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [currentStep, setCurrentStep] = useState<SelectionStep>('capacity');
  const [filter, setFilter] = useState<RoomFilter>({
    capacity: null,
    style: null,
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [showModal, setShowModal] = useState(false);
  const [showGallery, setShowGallery] = useState(false);
  const [galleryInitialIndex, setGalleryInitialIndex] = useState(0);

  // Calculate pagination
  const totalPages = Math.ceil(filteredRooms.length / ITEMS_PER_PAGE);
  const paginatedRooms = filteredRooms.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  useEffect(() => {
    const fetchRooms = async () => {
      try {
        const locationRooms = await roomsService.getRooms(selectedLocation.id);
        setRooms(locationRooms);
      } catch (error) {
        console.error('Error fetching rooms:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRooms();
  }, [selectedLocation.id]);

  useEffect(() => {
    if (!rooms.length) return;

    let filtered = [...rooms];

    if (filter.capacity) {
      filtered = filtered.filter(room => {
        const capacity = room.capacity;
        switch (filter.capacity) {
          case '< 10':
            return capacity < 10;
          case '10-15':
            return capacity >= 10 && capacity <= 15;
          case '15-20':
            return capacity > 15 && capacity <= 20;
          case '> 20':
            return capacity > 20;
          default:
            return true;
        }
      });
    }

    if (filter.style) {
      filtered = filtered.filter(room => room.decorStyle === filter.style);
    }

    setFilteredRooms(filtered);
  }, [rooms, filter]);

  const handleCapacitySelect = (capacity: CapacityRange) => {
    setFilter(prev => ({ ...prev, capacity }));
    setCurrentStep('style');
  };

  const handleStyleSelect = (style: DecorStyle) => {
    setFilter(prev => ({ ...prev, style }));
    setCurrentStep('rooms');
  };

  const handleRoomSelect = (room: Room) => {
    setSelectedRoom(room);
    setShowModal(true);
  };

  const handleConfirmSelection = () => {
    if (selectedRoom) {
      setShowModal(false);
      onRoomSelect(selectedRoom);
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll to top of room section smoothly
    document.getElementById('rooms-section')?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleBack = () => {
    if (currentStep === 'style') {
      setCurrentStep('capacity');
      setFilter(prev => ({ ...prev, style: null }));
    } else if (currentStep === 'rooms') {
      setCurrentStep('style');
      setSelectedRoom(null);
    } else {
      onBack();
    }
  };

  return (
    <motion.div
      className="max-w-7xl mx-auto px-4 flex flex-col"
      variants={pageTransition}
      initial="initial"
      animate="animate"
      exit="exit"
      layout="position"
      transition={{
        layout: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] },
      }}
    >
      {/* Header */}
      <motion.div
        className={`flex items-center gap-4 mb-6`}
        variants={chatBubbleVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        layout="position"
        transition={{
          layout: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] },
        }}
      >
        <motion.div
          layout="position"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{
            duration: 0.3,
            ease: [0.25, 0.46, 0.45, 0.94],
          }}
        >
          <Button
            variant="ghost"
            size="icon"
            onClick={handleBack}
            className="text-gold-600 hover:text-gold-700 hover:bg-gold-50 transition-colors duration-200"
          >
            <ArrowLeft className="h-6 w-6" />
          </Button>
        </motion.div>
        <motion.h2
          className="text-2xl md:text-3xl font-semibold text-gold-900"
          layout="position"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{
            duration: 0.3,
            ease: [0.25, 0.46, 0.45, 0.94],
          }}
        >
          Chọn phòng tại {(selectedLocation.name as LocalizedString).vi}
        </motion.h2>
      </motion.div>

      {/* Main Content Container */}
      <motion.div
        className={`flex-1 ${currentStep !== 'rooms' ? 'flex items-start' : ''}`}
        layout="position"
        transition={{
          layout: { duration: 0.12, ease: [0.25, 0.46, 0.45, 0.94] },
        }}
      >
        <AnimatePresence mode="wait">
          {/* Capacity Selection */}
          {currentStep === 'capacity' && (
            <motion.div
              key="capacity"
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={staggerContainer}
              className="relative w-full max-w-4xl mx-auto"
            >
              <motion.div
                className="bg-white/80 backdrop-blur-sm rounded-xl p-8 ring-1 ring-gold-200/30 shadow-[0_0_15px_-3px_rgba(234,179,8,0.2)]"
                layout="preserve-aspect"
                transition={{
                  layout: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] },
                }}
              >
                <motion.h3
                  variants={chatBubbleVariants}
                  className="text-xl font-semibold text-gold-900 mb-4"
                  layout="position"
                >
                  Bạn đi bao nhiêu người?
                </motion.h3>
                <motion.div
                  className="grid grid-cols-1 md:grid-cols-2 gap-4"
                  layout="position"
                >
                  {capacityRanges.map((range, index) => (
                    <motion.div
                      key={range}
                      variants={optionVariants}
                      whileHover="hover"
                      whileTap="tap"
                      onClick={() => handleCapacitySelect(range)}
                      className="relative p-6 rounded-xl bg-white shadow-lg cursor-pointer border-2 border-transparent hover:border-gold-500 transition-colors duration-200"
                    >
                      <div className="flex items-center gap-3 mb-2">
                        <Users className="w-5 h-5 text-gold-600" />
                        <h3 className="text-xl font-semibold text-gold-900">
                          {range} người
                        </h3>
                      </div>
                      <p className="text-gray-600">
                        {range === '< 10'
                          ? 'Phù hợp cho nhóm nhỏ'
                          : range === '10-15'
                          ? 'Lý tưởng cho nhóm vừa'
                          : range === '15-20'
                          ? 'Thích hợp cho nhóm lớn'
                          : 'Dành cho sự kiện đặc biệt'}
                      </p>
                    </motion.div>
                  ))}
                </motion.div>
              </motion.div>
            </motion.div>
          )}

          {/* Style Selection */}
          {currentStep === 'style' && (
            <motion.div
              key="style"
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={staggerContainer}
              className="relative w-full max-w-4xl mx-auto"
            >
              <motion.div
                className="bg-white/80 backdrop-blur-sm rounded-xl p-8 ring-1 ring-gold-200/30 shadow-[0_0_15px_-3px_rgba(234,179,8,0.2)]"
                layout="preserve-aspect"
                transition={{
                  layout: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] },
                }}
              >
                <motion.h3
                  variants={chatBubbleVariants}
                  className="text-xl font-semibold text-gold-900 mb-4"
                  layout="position"
                >
                  Bạn thích phong cách nào?
                </motion.h3>
                <motion.div
                  className="grid grid-cols-1 md:grid-cols-2 gap-4"
                  layout="position"
                >
                  {decorStyles.map(style => (
                    <motion.div
                      key={style}
                      variants={optionVariants}
                      whileHover="hover"
                      whileTap="tap"
                      onClick={() => handleStyleSelect(style)}
                      className="relative p-6 rounded-xl bg-white shadow-lg cursor-pointer border-2 border-transparent hover:border-gold-500 transition-colors duration-200"
                    >
                      <h3 className="text-xl font-semibold text-gold-900 mb-2">
                        {roomsService.getDecorStyleDisplayName(style)}
                      </h3>
                      <p className="text-gray-600">
                        {style === DecorStyle.MODERN
                          ? 'Thiết kế hiện đại, sang trọng'
                          : 'Thiết kế cổ điển, quý phái'}
                      </p>
                    </motion.div>
                  ))}
                </motion.div>
              </motion.div>
            </motion.div>
          )}

          {/* Rooms Display */}
          {currentStep === 'rooms' && (
            <motion.div
              key="rooms"
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={staggerContainer}
              className="relative w-full"
              id="rooms-section"
              layout
              transition={{
                layout: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] },
              }}
            >
              {/* Filter Summary Bar */}
              <motion.div
                variants={chatBubbleVariants}
                className="bg-white/80 backdrop-blur-sm rounded-xl p-6 ring-1 ring-gold-200/30 shadow-[0_0_15px_-3px_rgba(234,179,8,0.2)] mb-6 sticky top-4 z-10"
                layout
                transition={{
                  layout: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] },
                }}
              >
                <div className="flex flex-wrap items-center gap-4 text-sm text-gold-600">
                  <motion.div
                    variants={optionVariants}
                    className="flex items-center gap-2 bg-gold-50/80 px-3 py-1.5 rounded-full"
                  >
                    <Users className="w-4 h-4" />
                    <span>{filter.capacity} người</span>
                  </motion.div>
                  <motion.div
                    variants={optionVariants}
                    className="flex items-center gap-2 bg-gold-50/80 px-3 py-1.5 rounded-full"
                  >
                    <Star className="w-4 h-4" />
                    <span>{roomsService.getDecorStyleDisplayName(filter.style!)}</span>
                  </motion.div>
                </div>
              </motion.div>

              {loading ? (
                <motion.div
                  className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 auto-rows-fr"
                  layout
                  transition={{
                    layout: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] },
                  }}
                >
                  {Array.from({ length: 6 }).map((_, i) => (
                    <motion.div
                      key={`skeleton-${i}`}
                      variants={optionVariants}
                      custom={i}
                      className="rounded-xl relative overflow-hidden min-h-[500px]"
                      layout
                      transition={{
                        layout: {
                          duration: 0.3,
                          ease: [0.25, 0.46, 0.45, 0.94],
                        },
                      }}
                    >
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-gold-50 via-gold-100/50 to-gold-50"
                        variants={shimmerAnimation}
                        animate="animate"
                        initial="initial"
                      />
                    </motion.div>
                  ))}
                </motion.div>
              ) : (
                <>
                  {filteredRooms.length === 0 ? (
                    <motion.div
                      variants={chatBubbleVariants}
                      className="bg-white/80 backdrop-blur-sm rounded-xl p-8 ring-1 ring-gold-200/30 shadow-[0_0_15px_-3px_rgba(234,179,8,0.2)] text-center"
                      layout
                      transition={{
                        layout: {
                          duration: 0.3,
                          ease: [0.25, 0.46, 0.45, 0.94],
                        },
                      }}
                    >
                      <motion.div
                        variants={optionVariants}
                        className="max-w-md mx-auto"
                      >
                        <Music className="w-12 h-12 text-gold-400 mx-auto mb-4" />
                        <p className="text-lg text-gray-600 mb-6">
                          Không tìm thấy phòng phù hợp với yêu cầu của bạn.
                        </p>
                        <Button
                          onClick={() => setCurrentStep('capacity')}
                          variant="outline"
                          className="bg-white hover:bg-gold-50"
                        >
                          Thử lại với tiêu chí khác
                        </Button>
                      </motion.div>
                    </motion.div>
                  ) : (
                    <>
                      <motion.div
                        className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 auto-rows-fr mb-8"
                        variants={roomsContainerVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        layout
                        transition={{
                          layout: {
                            duration: 0.3,
                            ease: [0.25, 0.46, 0.45, 0.94],
                          },
                        }}
                      >
                        {paginatedRooms.map((room, index) => (
                          <motion.div
                            key={room.id}
                            variants={roomCardVariants}
                            initial="initial"
                            animate="animate"
                            exit="exit"
                            whileHover="hover"
                            whileTap="tap"
                            onClick={() => handleRoomSelect(room)}
                            className="bg-white/80 backdrop-blur-sm rounded-xl overflow-hidden ring-1 ring-gold-200/30 shadow-[0_0_15px_-3px_rgba(234,179,8,0.2)] cursor-pointer transition-all duration-200 flex flex-col"
                            layout
                            transition={{
                              layout: {
                                duration: 0.3,
                                ease: [0.25, 0.46, 0.45, 0.94],
                              },
                            }}
                          >
                            {/* Room Image */}
                            <motion.div
                              className="relative h-56 overflow-hidden"
                              initial={{ scale: 1 }}
                              whileHover={{
                                scale: 1.05,
                                transition: { 
                                  duration: 0.2,
                                  ease: [0.25, 0.46, 0.45, 0.94],
                                },
                              }}
                            >
                              {room.images?.[0] ? (
                                <motion.img
                                  src={room.images[0]}
                                  alt={room.name.vi}
                                  className="w-full h-full object-cover"
                                  initial={{ scale: 1.2, opacity: 0 }}
                                  animate={{ 
                                    scale: 1,
                                    opacity: 1,
                                    transition: {
                                      duration: 0.3,
                                      ease: [0.25, 0.46, 0.45, 0.94],
                                    },
                                  }}
                                />
                              ) : (
                                <div className="w-full h-full bg-gradient-to-br from-gold-100 to-gold-50 flex items-center justify-center">
                                  <Music className="w-12 h-12 text-gold-400" />
                                </div>
                              )}
                            </motion.div>

                            {/* Room Content */}
                            <motion.div
                              className="flex flex-col flex-grow p-6"
                              initial={{ opacity: 0 }}
                              animate={{ 
                                opacity: 1,
                                transition: {
                                  delay: 0.1,
                                  duration: 0.3,
                                  ease: [0.25, 0.46, 0.45, 0.94],
                                },
                              }}
                            >
                              {/* Room Name and Description */}
                              <div className="text-center mb-4">
                                <h3 className="text-xl font-semibold text-gold-900 mb-2">
                                  {(room.name as LocalizedString).vi}
                                </h3>
                                <p className="text-gray-600 line-clamp-2">
                                  {(room.description as LocalizedString)?.vi || 'Không có mô tả'}
                                </p>
                              </div>

                              {/* Key Features */}
                              <div className="bg-gold-50/50 rounded-xl p-4 mb-4">
                                <div className="flex flex-col gap-3">
                                  {/* Room Type */}
                                  <div className="flex items-center gap-2">
                                    <Star className="w-5 h-5 text-gold-500" />
                                    <div>
                                      <div className="text-sm text-gold-600">Loại phòng</div>
                                      {room.roomType && (
                                        <div className={`
                                          px-2 py-1 rounded-full text-white text-sm font-medium
                                          bg-gradient-to-r ${getRoomTypeColor(room.roomType)}
                                          shadow-sm inline-block mt-1
                                        `}>
                                          {roomsService.getRoomTypeDisplayName(room.roomType)}
                                        </div>
                                      )}
                                    </div>
                                  </div>

                                  {/* Capacity */}
                                  <div className="flex items-center gap-2">
                                    <Users className="w-5 h-5 text-gold-500" />
                                    <div>
                                      <div className="text-sm text-gold-600">Sức chứa</div>
                                      <div className="text-gold-900 font-medium mt-1">
                                        {room.capacity} người
                                      </div>
                                    </div>
                                  </div>

                                  {/* Price */}
                                  <div className="flex items-center gap-2">
                                    <Clock className="w-5 h-5 text-gold-500" />
                                    <div>
                                      <div className="text-sm text-gold-600">Giá theo giờ</div>
                                      <div className="text-gold-900 font-medium mt-1">
                                        {roomsService.formatPrice(room.pricePerHour)}/h
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {/* Amenities Preview */}
                              <div className="flex flex-wrap gap-2">
                                {room.amenities?.slice(0, 3).map((amenity, index) => (
                                  <div
                                    key={index}
                                    className="px-2 py-1 bg-gold-50 text-gold-700 rounded-md text-sm flex items-center gap-1.5"
                                  >
                                    {index === 0 && <Tv className="w-3 h-3" />}
                                    {index === 1 && <Music className="w-3 h-3" />}
                                    {index === 2 && <Wifi className="w-3 h-3" />}
                                    <span className="truncate">{(amenity as LocalizedString).vi}</span>
                                  </div>
                                ))}
                                {(room.amenities?.length || 0) > 3 && (
                                  <div className="px-2 py-1 bg-gold-50 text-gold-700 rounded-md text-sm">
                                    +{(room.amenities?.length || 0) - 3}
                                  </div>
                                )}
                              </div>
                            </motion.div>
                          </motion.div>
                        ))}
                      </motion.div>

                      {/* Pagination */}
                      {totalPages > 1 && (
                        <motion.div
                          className="flex justify-center items-center gap-2 mb-8"
                          variants={chatBubbleVariants}
                          initial="hidden"
                          animate="visible"
                          exit="exit"
                        >
                          <motion.div
                            className="flex items-center gap-2"
                            variants={staggerContainer}
                            initial="hidden"
                            animate="visible"
                            exit="exit"
                          >
                            <motion.div variants={optionVariants}>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handlePageChange(currentPage - 1)}
                                disabled={currentPage === 1}
                                className="w-10 h-10"
                              >
                                <ChevronLeft className="w-4 h-4" />
                              </Button>
                            </motion.div>

                            {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                              (page) => (
                                <motion.div key={page} variants={optionVariants}>
                                  <Button
                                    variant={currentPage === page ? "default" : "outline"}
                                    onClick={() => handlePageChange(page)}
                                    className={`w-10 h-10 ${
                                      currentPage === page
                                        ? "bg-gold-600 text-white"
                                        : "text-gold-600"
                                    }`}
                                  >
                                    {page}
                                  </Button>
                                </motion.div>
                              )
                            )}

                            <motion.div variants={optionVariants}>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handlePageChange(currentPage + 1)}
                                disabled={currentPage === totalPages}
                                className="w-10 h-10"
                              >
                                <ChevronRight className="w-4 h-4" />
                              </Button>
                            </motion.div>
                          </motion.div>
                        </motion.div>
                      )}
                    </>
                  )}
                </>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Room Details Modal */}
      <Modal isOpen={showModal} onClose={() => setShowModal(false)} size="xl">
        <ModalBody className="p-0 rounded-t-2xl overflow-hidden">
          {selectedRoom && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              className="relative"
            >
              {/* Close Button - Moved to top right */}
              <button
                onClick={() => setShowModal(false)}
                className="absolute top-4 right-4 z-10 p-2 rounded-full bg-red-500 text-white hover:bg-red-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>

              {/* Room Image Section */}
              <div 
                className="relative h-72 sm:h-96 cursor-pointer group"
                onClick={() => {
                  if (selectedRoom.images?.length) {
                    setGalleryInitialIndex(0);
                    setShowGallery(true);
                  }
                }}
              >
                {selectedRoom.images?.[0] ? (
                  <>
                    <img
                      src={selectedRoom.images[0]}
                      alt={selectedRoom.name.vi}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                      <Expand className="w-8 h-8 text-white" />
                    </div>
                  </>
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-gold-100 to-gold-50 flex items-center justify-center">
                    <Music className="w-16 h-16 text-gold-400" />
                  </div>
                )}
              </div>

              {/* Additional Images */}
              {selectedRoom.images && selectedRoom.images.length > 1 && (
                <div className="px-4 -mt-12 mb-4 relative z-10">
                  <motion.div 
                    className="flex gap-2 overflow-x-auto pb-4 px-2 mask-fade-x"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    {selectedRoom.images.slice(1).map((image, index) => (
                      <motion.div
                        key={index + 1}
                        className="relative flex-shrink-0 w-20 h-20 sm:w-24 sm:h-24 rounded-lg overflow-hidden shadow-lg cursor-pointer group"
                        onClick={() => {
                          setGalleryInitialIndex(index + 1);
                          setShowGallery(true);
                        }}
                        whileHover={{ y: -2 }}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 * (index + 1) }}
                      >
                        <img
                          src={image}
                          alt={`${selectedRoom.name.vi} - Image ${index + 2}`}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                          <Expand className="w-5 h-5 text-white" />
                        </div>
                      </motion.div>
                    ))}
                  </motion.div>
                </div>
              )}

              {/* Content Section */}
              <div className="p-4 sm:p-8">
                <div className="max-w-3xl mx-auto">
                  {/* Header */}
                  <div className="mb-6 sm:mb-8 text-center">
                    <motion.h2 
                      className="text-2xl sm:text-3xl font-semibold text-gold-900 mb-2 sm:mb-3"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 }}
                    >
                      {(selectedRoom.name as LocalizedString).vi}
                    </motion.h2>
                    <motion.p 
                      className="text-gray-600 max-w-2xl mx-auto text-sm sm:text-base"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      {(selectedRoom.description as LocalizedString)?.vi || 'Không có mô tả'}
                    </motion.p>
                  </div>

                  {/* Room Type and Key Features */}
                  <motion.div 
                    className="bg-gold-50/50 rounded-xl p-4 sm:p-6 mb-6 sm:mb-8"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6 justify-between">
                      <div className="flex items-center gap-3 w-full sm:w-auto">
                        <Star className="w-5 h-5 sm:w-6 sm:h-6 text-gold-500" />
                        <div>
                          <div className="text-sm text-gold-600 mb-1">Loại phòng</div>
                          {selectedRoom.roomType && (
                            <div className={`
                              px-3 py-1.5 rounded-full text-white text-sm font-medium
                              bg-gradient-to-r ${getRoomTypeColor(selectedRoom.roomType)}
                              shadow-sm
                            `}>
                              {roomsService.getRoomTypeDisplayName(selectedRoom.roomType)}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="hidden sm:block h-12 w-px bg-gold-200" />
                      <div className="flex items-center gap-3 w-full sm:w-auto">
                        <Users className="w-5 h-5 sm:w-6 sm:h-6 text-gold-500" />
                        <div>
                          <div className="text-sm text-gold-600 mb-1">Sức chứa</div>
                          <div className="text-gold-900 font-medium">
                            {selectedRoom.capacity} người
                          </div>
                        </div>
                      </div>
                      <div className="hidden sm:block h-12 w-px bg-gold-200" />
                      <div className="flex items-center gap-3 w-full sm:w-auto">
                        <Clock className="w-5 h-5 sm:w-6 sm:h-6 text-gold-500" />
                        <div>
                          <div className="text-sm text-gold-600 mb-1">Giá theo giờ</div>
                          <div className="text-gold-900 font-medium">
                            {roomsService.formatPrice(selectedRoom.pricePerHour)}/h
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>

                  {/* Amenities */}
                  <div className="mb-6 sm:mb-8">
                    <h3 className="text-lg font-semibold text-gold-900 mb-4">
                      Tiện nghi phòng
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                      {selectedRoom.amenities?.map((amenity, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-2 p-3 bg-gold-50 rounded-lg"
                        >
                          {index % 4 === 0 && <Tv className="w-4 h-4 text-gold-500" />}
                          {index % 4 === 1 && <Music className="w-4 h-4 text-gold-500" />}
                          {index % 4 === 2 && <Wifi className="w-4 h-4 text-gold-500" />}
                          {index % 4 === 3 && <Star className="w-4 h-4 text-gold-500" />}
                          <span className="text-gray-700 text-sm sm:text-base">
                            {(amenity as LocalizedString).vi}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Action Button */}
                  <div className="flex justify-end pt-4 sm:pt-6 border-t border-gray-100">
                    <Button
                      onClick={handleConfirmSelection}
                      className="w-full sm:w-auto bg-gold-600 hover:bg-gold-700 text-white px-8"
                    >
                      Tiếp tục
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </ModalBody>
      </Modal>

      {/* Image Gallery */}
      {selectedRoom && (
        <ImageGallery
          images={selectedRoom.images || []}
          isOpen={showGallery}
          onClose={() => setShowGallery(false)}
          initialIndex={galleryInitialIndex}
        />
      )}

      <style jsx global>{`
        .mask-fade-x {
          mask-image: linear-gradient(to right, transparent, black 8px, black calc(100% - 8px), transparent);
        }
      `}</style>
    </motion.div>
  );
}
