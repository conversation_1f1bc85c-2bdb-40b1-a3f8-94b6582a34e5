'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useParams } from 'next/navigation';
import { UserRole, type UserResponseDto } from 'shared-types';
import { usersService } from '../../../../../services/users.service';
import { useAuthStore } from '../../../../../stores/auth-store';
import { Button } from '../../../../../components/ui/button';
import { Input } from '../../../../../components/ui/input';
import { Label } from '../../../../../components/ui/label';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from '../../../../../components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../../components/ui/select';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>fresh<PERSON><PERSON>,
  Mail,
  Shield,
} from 'lucide-react';
import Link from 'next/link';
import { useDialogs } from '../../../../../components/ui/modal-provider';
import { motion } from 'framer-motion';

interface EditUserFormData {
  email: string;
  name: string;
  phoneNumber: string;
  role: UserRole;
  isActive: boolean;
}

interface PasswordChangeData {
  newPassword: string;
  generateRandom: boolean;
}

const EditUserPage = () => {
  const router = useRouter();
  const params = useParams();
  const userId = params.id as string;
  const { user: currentUser } = useAuthStore();
  const { alert } = useDialogs();

  const [user, setUser] = useState<UserResponseDto | null>(null);
  const [formData, setFormData] = useState<EditUserFormData>({
    email: '',
    name: '',
    phoneNumber: '',
    role: UserRole.CUSTOMER,
    isActive: true,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Password change state
  const [showPasswordSection, setShowPasswordSection] = useState(false);
  const [passwordData, setPasswordData] = useState<PasswordChangeData>({
    newPassword: '',
    generateRandom: false,
  });
  const [changingPassword, setChangingPassword] = useState(false);

  // Add state for email verification
  const [verifyingEmail, setVerifyingEmail] = useState(false);

  // Fetch user data
  useEffect(() => {
    if (userId) {
      fetchUser();
    }
  }, [userId]);

  const fetchUser = async () => {
    try {
      setLoading(true);
      const userData = await usersService.getUserById(userId);
      setUser(userData);
      setFormData({
        email: userData.email,
        name: userData.name || '',
        phoneNumber: userData.phoneNumber || '',
        role: userData.role,
        isActive: userData.isActive,
      });
    } catch (err) {
      console.error('Failed to fetch user:', err);
      alert(
        err instanceof Error
          ? err.message
          : 'Không thể tải thông tin người dùng',
        'Lỗi',
        'error'
      );
      router.push('/dashboard/users');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleRoleChange = (value: string) => {
    setFormData(prev => ({ ...prev, role: value as UserRole }));
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handlePasswordSubmit = async () => {
    if (!passwordData.generateRandom && !passwordData.newPassword.trim()) {
      alert(
        'Vui lòng nhập mật khẩu mới hoặc chọn tạo mật khẩu ngẫu nhiên',
        'Lỗi',
        'error'
      );
      return;
    }

    setChangingPassword(true);
    try {
      const result = await usersService.adminChangePassword(userId, {
        newPassword: passwordData.generateRandom
          ? undefined
          : passwordData.newPassword,
        generateRandom: passwordData.generateRandom,
      });

      if (result.newPassword) {
        alert(
          `${result.message}\n\nMật khẩu mới: ${result.newPassword}\n\nVui lòng lưu lại mật khẩu này!`,
          'Thành công',
          'success'
        );
      } else {
        alert(result.message, 'Thành công', 'success');
      }

      // Reset password form
      setPasswordData({ newPassword: '', generateRandom: false });
      setShowPasswordSection(false);
    } catch (error) {
      console.error('Error changing password:', error);
      alert(
        error instanceof Error
          ? error.message
          : 'Có lỗi xảy ra khi thay đổi mật khẩu',
        'Lỗi',
        'error'
      );
    } finally {
      setChangingPassword(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.email.trim()) {
      newErrors.email = 'Email là bắt buộc';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email không hợp lệ';
    }

    if (!formData.name.trim()) {
      newErrors.name = 'Tên là bắt buộc';
    }

    if (formData.phoneNumber && !/^[0-9+\-\s()]+$/.test(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Số điện thoại không hợp lệ';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setSaving(true);
    try {
      // Update basic user info (email is not included in AdminUpdateUserDto)
      await usersService.updateUser(userId, {
        name: formData.name,
        phoneNumber: formData.phoneNumber || undefined,
      });

      // Update role if changed and user has permission
      if (
        formData.role !== user?.role &&
        usersService.canChangeRole(
          user!,
          currentUser?.id || '',
          currentUser?.role!
        )
      ) {
        await usersService.updateUserRole(userId, {
          role: formData.role,
        });
      }

      // Update status if changed and user has permission
      if (
        formData.isActive !== user?.isActive &&
        usersService.canDeactivateUser(user!, currentUser?.id || '')
      ) {
        await usersService.updateUserStatus(userId, {
          isActive: formData.isActive,
        });
      }

      alert(
        'Cập nhật thông tin người dùng thành công!',
        'Thành công',
        'success'
      );
      router.push('/dashboard/users');
    } catch (error) {
      console.error('Error updating user:', error);
      alert(
        error instanceof Error
          ? error.message
          : 'Có lỗi xảy ra khi cập nhật người dùng',
        'Lỗi',
        'error'
      );
    } finally {
      setSaving(false);
    }
  };

  const getRoleLabel = (role: UserRole): string => {
    return usersService.getRoleLabel(role);
  };

  const canEditRole = user
    ? usersService.canChangeRole(
        user,
        currentUser?.id || '',
        currentUser?.role!
      )
    : false;
  const canEditStatus = user
    ? usersService.canDeactivateUser(user, currentUser?.id || '')
    : false;
  const canChangePassword =
    user &&
    currentUser?.role === UserRole.SUPER_ADMIN &&
    user.id !== currentUser?.id;

  // Add email verification handler
  const handleVerifyEmail = async () => {
    try {
      setVerifyingEmail(true);
      await usersService.verifyEmail(userId);
      // Update local user state
      setUser(prev => (prev ? { ...prev, emailVerified: true } : null));
      alert('Email đã được xác thực thành công!', 'Thành công', 'success');
    } catch (error) {
      console.error('Error verifying email:', error);
      alert(
        error instanceof Error
          ? error.message
          : 'Có lỗi xảy ra khi xác thực email',
        'Lỗi',
        'error'
      );
    } finally {
      setVerifyingEmail(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gold-600"></div>
        <span className="ml-2 text-gray-900">
          Đang tải thông tin người dùng...
        </span>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          Không tìm thấy người dùng
        </h1>
        <Link href="/dashboard/users">
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại danh sách
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/dashboard/users">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Chỉnh sửa người dùng
          </h1>
          <p className="text-gray-900 mt-1">
            Cập nhật thông tin và quyền hạn của người dùng
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Form Section */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-gray-900">
                <UserCheck className="h-5 w-5" />
                Thông tin người dùng
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {errors.general && (
                  <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
                    {errors.general}
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-gray-900">
                      Email <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      className={`text-black ${errors.email ? 'border-red-500' : ''}`}
                      disabled
                    />
                    <p className="text-sm text-gray-900">
                      Email không thể thay đổi sau khi tạo tài khoản
                    </p>
                    {errors.email && (
                      <p className="text-sm text-red-500">{errors.email}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-gray-900">
                      Tên đầy đủ <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="Nguyễn Văn A"
                      className={`text-black ${errors.name ? 'border-red-500' : ''}`}
                    />
                    {errors.name && (
                      <p className="text-sm text-red-500">{errors.name}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phoneNumber" className="text-gray-900">
                      Số điện thoại
                    </Label>
                    <Input
                      id="phoneNumber"
                      name="phoneNumber"
                      value={formData.phoneNumber}
                      onChange={handleInputChange}
                      placeholder="+84 123 456 789"
                      className={`text-black ${errors.phoneNumber ? 'border-red-500' : ''}`}
                    />
                    {errors.phoneNumber && (
                      <p className="text-sm text-red-500">
                        {errors.phoneNumber}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="role" className="text-gray-900">
                      Vai trò <span className="text-red-500">*</span>
                    </Label>
                    {canEditRole ? (
                      <Select
                        value={formData.role}
                        onValueChange={handleRoleChange}
                      >
                        <SelectTrigger className="text-black">
                          <span className="text-black">
                            {getRoleLabel(formData.role)}
                          </span>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={UserRole.CUSTOMER}>
                            <span className="text-black">
                              {getRoleLabel(UserRole.CUSTOMER)}
                            </span>
                          </SelectItem>
                          <SelectItem value={UserRole.ADMIN}>
                            <span className="text-black">
                              {getRoleLabel(UserRole.ADMIN)}
                            </span>
                          </SelectItem>
                          <SelectItem value={UserRole.SUPER_ADMIN}>
                            <span className="text-black">
                              {getRoleLabel(UserRole.SUPER_ADMIN)}
                            </span>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-black">
                        {getRoleLabel(formData.role)}
                      </div>
                    )}
                    {!canEditRole && (
                      <p className="text-sm text-gray-900">
                        Bạn không có quyền thay đổi vai trò của người dùng này
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <input
                      id="isActive"
                      name="isActive"
                      type="checkbox"
                      checked={formData.isActive}
                      onChange={handleInputChange}
                      disabled={!canEditStatus}
                      className="rounded border-gray-300 text-gold-600 focus:ring-gold-500"
                    />
                    <Label htmlFor="isActive" className="text-sm text-gray-900">
                      Tài khoản đang hoạt động
                    </Label>
                    {!canEditStatus && (
                      <span className="text-sm text-gray-900 ml-2">
                        (Không thể thay đổi)
                      </span>
                    )}
                  </div>
                  {canEditStatus && (
                    <p className="text-xs text-gray-600">
                      💡 Bỏ tích để vô hiệu hóa tài khoản. Người dùng sẽ không
                      thể đăng nhập khi tài khoản bị vô hiệu hóa.
                    </p>
                  )}
                </div>

                <div className="flex gap-3 pt-6 border-t">
                  <Button
                    type="submit"
                    disabled={saving}
                    className="bg-gold-600 hover:bg-gold-700"
                  >
                    {saving ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Đang lưu...
                      </>
                    ) : (
                      <>
                        <UserCheck className="h-4 w-4 mr-2" />
                        Cập nhật người dùng
                      </>
                    )}
                  </Button>
                  <Link href="/dashboard/users">
                    <Button variant="outline" type="button">
                      Hủy
                    </Button>
                  </Link>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar Section */}
        <div className="space-y-6">
          {/* User Info Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium text-gray-900">
                Thông tin bổ sung
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Ngày tạo:</span>
                  <span className="font-medium text-gray-900">
                    {new Date(user.createdAt).toLocaleDateString('vi-VN')}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Lần cập nhật cuối:</span>
                  <span className="font-medium text-gray-900">
                    {new Date(user.updatedAt).toLocaleDateString('vi-VN')}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Trạng thái email:</span>
                  <div className="flex items-center gap-2">
                    <span
                      className={`font-medium ${
                        user.emailVerified
                          ? 'text-green-600'
                          : 'text-yellow-600'
                      }`}
                    >
                      {user.emailVerified ? 'Đã xác thực' : 'Chưa xác thực'}
                    </span>
                    {!user.emailVerified &&
                      currentUser?.role === UserRole.SUPER_ADMIN && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleVerifyEmail}
                          disabled={verifyingEmail}
                          className="ml-2"
                        >
                          {verifyingEmail ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Mail className="h-4 w-4" />
                          )}
                          <span className="ml-1">Xác thực</span>
                        </Button>
                      )}
                  </div>
                </div>
                {user.bookingCount !== undefined && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Số lượng đặt phòng:</span>
                    <span className="font-medium text-gray-900">
                      {user.bookingCount}
                    </span>
                  </div>
                )}
                {user.totalSpent !== undefined && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Tổng chi tiêu:</span>
                    <span className="font-medium text-gray-900">
                      {new Intl.NumberFormat('vi-VN', {
                        style: 'currency',
                        currency: 'VND',
                      }).format(user.totalSpent)}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Password Management Card */}
          {canChangePassword && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium text-gray-900 flex items-center gap-2">
                  <Key className="h-4 w-4" />
                  Quản lý mật khẩu
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {!showPasswordSection ? (
                  <Button
                    onClick={() => setShowPasswordSection(true)}
                    variant="outline"
                    className="w-full"
                  >
                    Thay đổi mật khẩu
                  </Button>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <input
                        id="generateRandom"
                        name="generateRandom"
                        type="checkbox"
                        checked={passwordData.generateRandom}
                        onChange={handlePasswordChange}
                        className="rounded border-gray-300 text-gold-600 focus:ring-gold-500"
                      />
                      <Label
                        htmlFor="generateRandom"
                        className="text-sm text-gray-900"
                      >
                        Tạo mật khẩu ngẫu nhiên
                      </Label>
                    </div>

                    {!passwordData.generateRandom && (
                      <div className="space-y-2">
                        <Label htmlFor="newPassword" className="text-gray-900">
                          Mật khẩu mới
                        </Label>
                        <Input
                          id="newPassword"
                          name="newPassword"
                          type="password"
                          value={passwordData.newPassword}
                          onChange={handlePasswordChange}
                          placeholder="Nhập mật khẩu mới (tối thiểu 8 ký tự)"
                          className="text-black"
                          minLength={8}
                        />
                      </div>
                    )}

                    <div className="flex gap-2">
                      <Button
                        onClick={handlePasswordSubmit}
                        disabled={changingPassword}
                        className="bg-yellow-600 hover:bg-yellow-700 text-white"
                      >
                        {changingPassword ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Đang thay đổi...
                          </>
                        ) : (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            {passwordData.generateRandom
                              ? 'Tạo mật khẩu mới'
                              : 'Cập nhật mật khẩu'}
                          </>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setShowPasswordSection(false);
                          setPasswordData({
                            newPassword: '',
                            generateRandom: false,
                          });
                        }}
                        disabled={changingPassword}
                      >
                        Hủy
                      </Button>
                    </div>

                    <p className="text-xs text-yellow-700">
                      ⚠️ Lưu ý: Thay đổi mật khẩu sẽ buộc người dùng phải đăng
                      nhập lại với mật khẩu mới.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default EditUserPage;
