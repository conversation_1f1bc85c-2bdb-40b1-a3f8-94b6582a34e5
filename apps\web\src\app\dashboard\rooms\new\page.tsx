'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  ArrowLeft,
  Save,
  Hotel,
  PlusCircle,
  Building,
  ImageIcon,
  Tag,
} from 'lucide-react';
import { roomsService } from '../../../../services/rooms.service';
import { locationsService } from '../../../../services/locations.service';
import {
  CreateRoomDto,
  Location,
  RoomTypeEnum,
  LocalizedString,
  DecorStyle,
} from 'shared-types';
import { Button } from '../../../../components/ui/button';
import { Input } from '../../../../components/ui/input';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../../components/ui/card';
import { Label } from '../../../../components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../components/ui/select';
import ImageUpload from '../../../../components/ui/image-upload';
import AmenitiesManager from '../../../../components/ui/amenities-manager';

interface FormData {
  nameVi: string;
  nameEn: string;
  descriptionVi: string;
  descriptionEn: string;
  capacity: number;
  pricePerHour: number;
  locationId: string;
  roomType: RoomTypeEnum | '';
  decorStyle: DecorStyle | '';
  images: File[];
  amenities: LocalizedString[];
}

interface FormErrors {
  [key: string]: string;
}

export default function AddNewRoomPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [locations, setLocations] = useState<Location[]>([]);
  const [loadingLocations, setLoadingLocations] = useState(true);
  const [formData, setFormData] = useState<FormData>({
    nameVi: '',
    nameEn: '',
    descriptionVi: '',
    descriptionEn: '',
    capacity: 1,
    pricePerHour: 100000,
    locationId: '',
    roomType: '',
    decorStyle: DecorStyle.MODERN,
    images: [],
    amenities: [],
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadingImages, setUploadingImages] = useState(false);

  useEffect(() => {
    const fetchLocations = async () => {
      try {
        setLoadingLocations(true);
        const response = await locationsService.getLocations({
          page: 1,
          limit: 100,
        });
        setLocations(response.items);

        const queryLocationId = searchParams.get('locationId');

        if (
          queryLocationId &&
          response.items.some(loc => loc.id === queryLocationId)
        ) {
          setFormData(prev => ({ ...prev, locationId: queryLocationId }));
        } else if (response.items.length > 0) {
          setFormData(prev => ({ ...prev, locationId: response.items[0].id }));
        }
      } catch (error) {
        console.error('Error fetching locations:', error);
        setErrors(prev => ({
          ...prev,
          general: 'Không thể tải danh sách chi nhánh.',
        }));
      } finally {
        setLoadingLocations(false);
      }
    };
    fetchLocations();
  }, [searchParams]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    if (!formData.nameVi.trim())
      newErrors.nameVi = 'Tên tiếng Việt là bắt buộc';
    if (!formData.nameEn.trim()) newErrors.nameEn = 'Tên tiếng Anh là bắt buộc';
    if (!formData.locationId) newErrors.locationId = 'Vui lòng chọn chi nhánh';
    if (!formData.roomType) newErrors.roomType = 'Vui lòng chọn loại phòng';
    if (!formData.decorStyle)
      newErrors.decorStyle = 'Vui lòng chọn phong cách trang trí';
    if (formData.capacity < 1) newErrors.capacity = 'Sức chứa phải lớn hơn 0';
    if (formData.pricePerHour < 1000)
      newErrors.pricePerHour = 'Giá phòng ít nhất là 1,000 VND';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (
    field: keyof FormData,
    value: string | number | boolean | File[] | LocalizedString[]
  ) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      // First, create the room
      const roomData: CreateRoomDto = {
        name: { vi: formData.nameVi, en: formData.nameEn } as LocalizedString,
        description: {
          vi: formData.descriptionVi,
          en: formData.descriptionEn,
        } as LocalizedString,
        capacity: Number(formData.capacity),
        pricePerHour: Number(formData.pricePerHour),
        roomType: formData.roomType as RoomTypeEnum,
        decorStyle: formData.decorStyle as DecorStyle,
        locationId: formData.locationId,
        images: [], // Images will be uploaded separately
        amenities: formData.amenities,
      };

      const createdRoom = await roomsService.createRoom(
        formData.locationId,
        roomData
      );

      // Upload images if any are selected
      if (formData.images.length > 0) {
        setUploadingImages(true);
        try {
          await roomsService.uploadRoomImages(
            formData.locationId,
            createdRoom.id,
            formData.images
          );
        } catch (imageError) {
          console.error('Error uploading images:', imageError);
          // Room was created but images failed - show warning
          setErrors(prev => ({
            ...prev,
            general:
              'Phòng đã tạo thành công nhưng có lỗi khi tải hình ảnh. Bạn có thể thêm hình ảnh sau.',
          }));
        } finally {
          setUploadingImages(false);
        }
      }

      // Redirect to room detail page or rooms list
      router.push('/dashboard/rooms');
    } catch (error) {
      console.error('Error creating room:', error);
      setErrors(prev => ({
        ...prev,
        general: 'Có lỗi xảy ra khi tạo phòng. Vui lòng thử lại.',
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  const getLocationDisplayName = (locationName: any): string => {
    if (typeof locationName === 'string') return locationName;
    return (
      locationName?.vi || locationName?.en || 'Tên chi nhánh không xác định'
    );
  };

  // Helper function to get current location display name
  const getCurrentLocationLabel = (): string => {
    if (!formData.locationId) return 'Chọn chi nhánh';
    const location = locations.find(loc => loc.id === formData.locationId);
    return location ? getLocationDisplayName(location.name) : 'Chọn chi nhánh';
  };

  // Helper function to get current room type display name
  const getCurrentRoomTypeLabel = (): string => {
    if (!formData.roomType) return 'Chọn loại phòng';
    return roomsService.getRoomTypeDisplayName(
      formData.roomType as RoomTypeEnum
    );
  };

  // Helper function to get current decor style display name
  const getCurrentDecorStyleLabel = (): string => {
    if (!formData.decorStyle) return 'Chọn phong cách trang trí';
    return roomsService.getDecorStyleDisplayName(formData.decorStyle);
  };

  if (loadingLocations) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-queens-gold"></div>
      </div>
    );
  }

  if (locations.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-h1 font-bold text-gray-900">Thêm Phòng Mới</h1>
          </div>
        </div>
        <Card>
          <CardContent className="p-12 text-center">
            <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Chưa có chi nhánh nào
            </h3>
            <p className="text-gray-600 mb-6">
              Bạn cần tạo chi nhánh trước khi có thể thêm phòng.
            </p>
            <Button
              onClick={() => router.push('/dashboard/locations/new')}
              className="gap-2 bg-queens-gold hover:bg-queens-gold/90 text-white font-medium shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
            >
              <PlusCircle className="h-4 w-4" />
              Tạo Chi Nhánh Mới
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={() => router.back()} className="p-2">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-h1 font-bold text-gray-900">Thêm Phòng Mới</h1>
          <p className="text-lg text-gray-600">
            Tạo phòng karaoke mới cho hệ thống
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Hotel className="h-5 w-5 text-queens-gold" />
              Thông Tin Cơ Bản
            </CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="nameVi">Tên phòng (Tiếng Việt) *</Label>
              <Input
                id="nameVi"
                value={formData.nameVi}
                onChange={e => handleInputChange('nameVi', e.target.value)}
                placeholder="Ví dụ: Phòng VIP 1"
                className={errors.nameVi ? 'border-red-500' : ''}
              />
              {errors.nameVi && (
                <p className="text-sm text-red-600">{errors.nameVi}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="nameEn">Tên phòng (Tiếng Anh) *</Label>
              <Input
                id="nameEn"
                value={formData.nameEn}
                onChange={e => handleInputChange('nameEn', e.target.value)}
                placeholder="Example: VIP Room 1"
                className={errors.nameEn ? 'border-red-500' : ''}
              />
              {errors.nameEn && (
                <p className="text-sm text-red-600">{errors.nameEn}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="locationId">Chi nhánh *</Label>
              <Select
                value={formData.locationId}
                onValueChange={value => {
                  handleInputChange('locationId', value);
                }}
              >
                <SelectTrigger
                  className={
                    errors.locationId
                      ? 'border-red-500 text-black'
                      : 'text-black'
                  }
                >
                  <span className="text-black">
                    {getCurrentLocationLabel()}
                  </span>
                </SelectTrigger>
                <SelectContent>
                  {locations.map(loc => {
                    const displayName = getLocationDisplayName(loc.name);
                    return (
                      <SelectItem key={loc.id} value={loc.id}>
                        <span className="text-black">{displayName}</span>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
              {errors.locationId && (
                <p className="text-sm text-red-600">{errors.locationId}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="roomType">Loại phòng *</Label>
              <Select
                value={formData.roomType}
                onValueChange={value => handleInputChange('roomType', value)}
              >
                <SelectTrigger
                  className={
                    errors.roomType ? 'border-red-500 text-black' : 'text-black'
                  }
                >
                  <span className="text-black">
                    {getCurrentRoomTypeLabel()}
                  </span>
                </SelectTrigger>
                <SelectContent>
                  {Object.values(RoomTypeEnum).map(type => (
                    <SelectItem key={type} value={type}>
                      <span className="text-black">
                        {roomsService.getRoomTypeDisplayName(type)}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.roomType && (
                <p className="text-sm text-red-600">{errors.roomType}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="decorStyle">Phong cách trang trí *</Label>
              <Select
                value={formData.decorStyle}
                onValueChange={value => handleInputChange('decorStyle', value)}
              >
                <SelectTrigger
                  className={
                    errors.decorStyle
                      ? 'border-red-500 text-black'
                      : 'text-black'
                  }
                >
                  <span className="text-black">
                    {getCurrentDecorStyleLabel()}
                  </span>
                </SelectTrigger>
                <SelectContent>
                  {Object.values(DecorStyle).map(style => (
                    <SelectItem key={style} value={style}>
                      <span className="text-black">
                        {roomsService.getDecorStyleDisplayName(style)}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.decorStyle && (
                <p className="text-sm text-red-600">{errors.decorStyle}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="capacity">Sức chứa (người) *</Label>
              <Input
                id="capacity"
                type="number"
                value={formData.capacity}
                onChange={e =>
                  handleInputChange('capacity', parseInt(e.target.value, 10))
                }
                min="1"
                placeholder="Ví dụ: 10"
                className={errors.capacity ? 'border-red-500' : ''}
              />
              {errors.capacity && (
                <p className="text-sm text-red-600">{errors.capacity}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="pricePerHour">Giá mỗi giờ (VND) *</Label>
              <Input
                id="pricePerHour"
                type="number"
                value={formData.pricePerHour}
                onChange={e =>
                  handleInputChange(
                    'pricePerHour',
                    parseInt(e.target.value, 10)
                  )
                }
                min="1000"
                placeholder="Ví dụ: 250000"
                className={errors.pricePerHour ? 'border-red-500' : ''}
              />
              {errors.pricePerHour && (
                <p className="text-sm text-red-600">{errors.pricePerHour}</p>
              )}
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="descriptionVi">Mô tả (Tiếng Việt)</Label>
              <textarea
                id="descriptionVi"
                value={formData.descriptionVi}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                  handleInputChange('descriptionVi', e.target.value)
                }
                placeholder="Mô tả chi tiết về phòng..."
                rows={3}
                className={`w-full p-2 border rounded-md text-gray-900 ${errors.descriptionVi ? 'border-red-500' : 'border-gray-300'}`}
              />
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="descriptionEn">Mô tả (Tiếng Anh)</Label>
              <textarea
                id="descriptionEn"
                value={formData.descriptionEn}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                  handleInputChange('descriptionEn', e.target.value)
                }
                placeholder="Detailed description of the room..."
                rows={3}
                className={`w-full p-2 border rounded-md text-gray-900 ${errors.descriptionEn ? 'border-red-500' : 'border-gray-300'}`}
              />
            </div>
          </CardContent>
        </Card>

        {/* Image Upload */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5 text-queens-gold" />
              Hình Ảnh Phòng
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ImageUpload
              images={formData.images}
              onImagesChange={images => handleInputChange('images', images)}
              maxImages={5}
              disabled={isSubmitting || uploadingImages}
            />
          </CardContent>
        </Card>

        {/* Amenities Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5 text-queens-gold" />
              Tiện Nghi Phòng
            </CardTitle>
          </CardHeader>
          <CardContent>
            <AmenitiesManager
              amenities={formData.amenities}
              onAmenitiesChange={amenities =>
                handleInputChange('amenities', amenities)
              }
              disabled={isSubmitting}
              maxAmenities={10}
            />
          </CardContent>
        </Card>

        {errors.general && (
          <Card className="border-red-500 bg-red-50">
            <CardContent className="p-4">
              <p className="text-sm text-red-700">{errors.general}</p>
            </CardContent>
          </Card>
        )}

        <div className="flex justify-end gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/dashboard/rooms')}
            disabled={isSubmitting || uploadingImages}
          >
            Hủy
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || uploadingImages}
            className="gap-2 bg-queens-gold hover:bg-queens-gold/90 text-white font-medium shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
          >
            {isSubmitting || uploadingImages ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                {uploadingImages ? 'Đang tải hình ảnh...' : 'Đang lưu...'}
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                Lưu Phòng
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
