import {
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  <PERSON><PERSON><PERSON>th,
  Matches,
} from 'class-validator';
import { UserRole } from 'shared-types';

export class CreateUserDto {
  @IsEmail({}, { message: '<PERSON>ail phải có định dạng hợp lệ' })
  email: string;

  @IsString({ message: 'Mật khẩu phải là chuỗi ký tự' })
  @MinLength(6, { message: 'Mật khẩu phải có ít nhất 6 ký tự' })
  password: string;

  @IsOptional()
  @IsString({ message: 'Tên phải là chuỗi ký tự' })
  name?: string;

  @IsOptional()
  @IsString({ message: 'Số điện thoại phải là chuỗi ký tự' })
  @Matches(/^[0-9+\-\s()]+$/, { message: 'Số điện thoại không hợp lệ' })
  phoneNumber?: string;

  @IsOptional()
  @IsEnum(UserRole, { message: '<PERSON><PERSON> tr<PERSON> không hợp lệ' })
  role?: User<PERSON><PERSON>;
}
