'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useAuthStore } from '../../stores/auth-store';
import { UserRole } from 'shared-types';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import {
  BarChart3,
  Building2,
  Calendar,
  ChevronLeft,
  ChevronRight,
  Settings,
  Shield,
  Users,
  LayoutDashboard,
  Home,
  LogOut,
} from 'lucide-react';

interface NavigationItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  requiredRole?: UserRole[];
  children?: NavigationItem[];
}

const navigationItems: NavigationItem[] = [
  {
    href: '/dashboard',
    label: 'Tổng quan',
    icon: LayoutDashboard,
  },
  {
    href: '/dashboard/locations',
    label: 'Quản lý chi nhánh',
    icon: Building2,
    children: [
      {
        href: '/dashboard/locations',
        label: 'Danh sách chi nhánh',
        icon: Building2,
      },
      {
        href: '/dashboard/locations/new',
        label: 'Thêm chi nhánh mới',
        icon: Building2,
        requiredRole: [UserRole.ADMIN, UserRole.SUPER_ADMIN],
      },
    ],
  },
  {
    href: '/dashboard/rooms',
    label: 'Quản lý phòng hát',
    icon: Home,
    children: [
      {
        href: '/dashboard/rooms',
        label: 'Danh sách phòng',
        icon: Home,
      },
      {
        href: '/dashboard/rooms/new',
        label: 'Thêm phòng mới',
        icon: Home,
        requiredRole: [UserRole.ADMIN, UserRole.SUPER_ADMIN],
      },
    ],
  },
  {
    href: '/dashboard/bookings',
    label: 'Quản lý đặt phòng',
    icon: Calendar,
    children: [
      {
        href: '/dashboard/bookings',
        label: 'Danh sách đặt phòng',
        icon: Calendar,
      },
      {
        href: '/dashboard/bookings/analytics',
        label: 'Thống kê đặt phòng',
        icon: BarChart3,
      },
    ],
  },
  {
    href: '/dashboard/users',
    label: 'Quản lý người dùng',
    icon: Users,
    requiredRole: [UserRole.ADMIN, UserRole.SUPER_ADMIN],
    children: [
      {
        href: '/dashboard/users',
        label: 'Danh sách người dùng',
        icon: Users,
        requiredRole: [UserRole.ADMIN, UserRole.SUPER_ADMIN],
      },
      {
        href: '/dashboard/users/new',
        label: 'Thêm người dùng mới',
        icon: Users,
        requiredRole: [UserRole.ADMIN, UserRole.SUPER_ADMIN],
      },
    ],
  },
  {
    href: '/dashboard/analytics',
    label: 'Phân tích & Báo cáo',
    icon: BarChart3,
  },
];

interface AdminSidebarProps {
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export default function AdminSidebar({
  isCollapsed = false,
  onToggleCollapse,
}: AdminSidebarProps) {
  const { user, clearAuth } = useAuthStore();
  const router = useRouter();
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const handleLogout = async () => {
    clearAuth();
    router.push('/auth/login');
  };

  const toggleExpanded = (href: string) => {
    setExpandedItems(prev =>
      prev.includes(href) ? prev.filter(item => item !== href) : [...prev, href]
    );
  };

  const hasPermission = (requiredRole?: UserRole[]) => {
    if (!requiredRole) return true;
    return user?.role && requiredRole.includes(user.role);
  };

  const isActiveRoute = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/dashboard';
    }
    return pathname.startsWith(href);
  };

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    if (!hasPermission(item.requiredRole)) {
      return null;
    }

    const isActive = isActiveRoute(item.href);
    const isExpanded = expandedItems.includes(item.href);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.href} className="space-y-1">
        <div className="flex items-center">
          <Link
            href={item.href}
            className={cn(
              'flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex-1',
              level > 0 && 'ml-4 pl-8',
              isActive
                ? 'bg-gold-100 text-gold-800 border-r-2 border-gold-600'
                : 'text-gray-700 hover:bg-gold-50 hover:text-gold-800',
              isCollapsed && 'justify-center px-2'
            )}
          >
            <item.icon className="h-5 w-5 flex-shrink-0" />
            {!isCollapsed && (
              <>
                <span className="ml-3 flex-1">{item.label}</span>
                {hasChildren && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 ml-2"
                    onClick={e => {
                      e.preventDefault();
                      toggleExpanded(item.href);
                    }}
                  >
                    {isExpanded ? (
                      <ChevronLeft className="h-3 w-3" />
                    ) : (
                      <ChevronRight className="h-3 w-3" />
                    )}
                  </Button>
                )}
              </>
            )}
          </Link>
        </div>

        {/* Children items */}
        {hasChildren && isExpanded && !isCollapsed && (
          <div className="space-y-1">
            {item.children!.map(child =>
              renderNavigationItem(child, level + 1)
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      className={cn(
        'bg-white border-r border-gray-200 transition-all duration-300 flex flex-col h-screen',
        isCollapsed ? 'w-16' : 'w-64'
      )}
    >
      {/* Header - Now User Info */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-gold-500 to-gold-600 rounded-full flex items-center justify-center">
                <span className="text-white font-semibold text-sm">
                  {user?.name?.charAt(0).toUpperCase() || 'A'}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-800 truncate">
                  {user?.name || 'Admin'}
                </p>
                <div className="text-xs text-gray-500">
                  {user?.role === UserRole.SUPER_ADMIN
                    ? 'Quản trị viên'
                    : 'Nhân viên'}
                </div>
              </div>
            </div>
          )}
          {isCollapsed && (
            <div className="w-8 h-8 bg-gradient-to-br from-gold-500 to-gold-600 rounded-full flex items-center justify-center mx-auto">
              <span className="text-white font-semibold text-sm">
                {user?.name?.charAt(0).toUpperCase() || 'A'}
              </span>
            </div>
          )}
          {onToggleCollapse && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleCollapse}
              className="h-8 w-8 p-0 flex-shrink-0"
            >
              {isCollapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronLeft className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
        {navigationItems.map(item => renderNavigationItem(item))}
      </nav>

      {/* Footer with Logout */}
      <div className="p-4 border-t border-gray-200">
        <Button
          variant="ghost"
          className={cn(
            'w-full flex items-center text-red-600 hover:text-red-700 hover:bg-red-50',
            isCollapsed && 'justify-center'
          )}
          onClick={handleLogout}
        >
          <LogOut className="h-5 w-5" />
          {!isCollapsed && <span className="ml-3">Đăng xuất</span>}
        </Button>
        {!isCollapsed && (
          <div className="text-xs text-gray-500 text-center mt-4">
            <p>Queen Karaoke Admin</p>
            <p>Phiên bản 1.0.0</p>
          </div>
        )}
      </div>
    </div>
  );
}
