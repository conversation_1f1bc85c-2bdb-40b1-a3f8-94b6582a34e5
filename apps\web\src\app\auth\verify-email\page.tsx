'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Button } from '../../../components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../components/ui/card';
import { authService } from '../../../services/auth-service';
import { Mail, CheckCircle2, XCircle } from 'lucide-react';

// Animation variants following the guidelines
const containerVariants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08,
      duration: 0.3,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  show: {
    opacity: 1,
    y: 0,
    transition: {
      type: 'spring',
      stiffness: 200,
      damping: 20,
      duration: 0.4,
    },
  },
};

interface ApiError extends Error {
  response?: {
    data?: {
      message?: string;
    };
  };
}

function VerifyEmailContent() {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const [message, setMessage] = useState<string>(
    'Đang xác thực email của bạn...'
  );
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    async function verify() {
      if (!token) {
        setMessage('Liên kết xác thực không hợp lệ hoặc bị thiếu.');
        setError('Không tìm thấy mã xác thực.');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const response = await authService.verifyEmail(token);
        setMessage(response.message || 'Xác thực email thành công!');
        setError(null);
      } catch (err) {
        const apiError = err as ApiError;
        const errorMessage =
          apiError.response?.data?.message ||
          apiError.message ||
          'Xác thực email thất bại. Vui lòng thử lại.';
        setMessage(errorMessage);
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    }

    verify();
  }, [token]);

  return (
    <motion.div
      className="min-h-screen bg-gradient-to-br from-gold-50 via-white to-gold-50 flex items-center justify-center p-4"
      initial="hidden"
      animate="show"
      variants={containerVariants}
    >
      <Card className="w-full max-w-md shadow-xl border-gold-200">
        <CardHeader className="text-center space-y-2 pb-2">
          <motion.div
            className="flex justify-center mb-6"
            variants={itemVariants}
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            {isLoading ? (
              <div className="w-16 h-16 bg-gradient-to-br from-gold-500 to-gold-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Mail className="w-8 h-8 text-white animate-pulse" />
              </div>
            ) : error ? (
              <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg">
                <XCircle className="w-8 h-8 text-white" />
              </div>
            ) : (
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg">
                <CheckCircle2 className="w-8 h-8 text-white" />
              </div>
            )}
          </motion.div>
          <CardTitle
            className={`text-3xl font-bold bg-gradient-to-r ${
              isLoading
                ? 'from-gold-700 to-gold-600'
                : error
                  ? 'from-red-700 to-red-600'
                  : 'from-green-700 to-green-600'
            } bg-clip-text text-transparent`}
          >
            {isLoading
              ? 'Đang Xác Thực Email'
              : error
                ? 'Xác Thực Thất Bại'
                : 'Xác Thực Thành Công'}
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {isLoading ? (
            <motion.div
              className="flex flex-col items-center space-y-4"
              variants={itemVariants}
            >
              <div className="w-8 h-8 border-4 border-gold-600 border-t-transparent rounded-full animate-spin" />
              <p className="text-gray-600">{message}</p>
            </motion.div>
          ) : (
            <>
              <motion.p
                className={`text-lg text-center ${
                  error ? 'text-red-600' : 'text-green-600'
                }`}
                variants={itemVariants}
              >
                {message}
              </motion.p>
              {!error && (
                <motion.p
                  className="text-gray-600 text-center"
                  variants={itemVariants}
                >
                  Bạn có thể đăng nhập vào tài khoản của mình ngay bây giờ.
                </motion.p>
              )}
            </>
          )}

          <motion.div variants={itemVariants}>
            <Button
              asChild
              variant={error ? 'destructive' : 'default'}
              className={`w-full ${
                !error &&
                'bg-gradient-to-r from-queens-gold to-gold-600 hover:from-queens-gold/90 hover:to-gold-600/90'
              } text-white font-semibold py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300`}
              disabled={isLoading}
            >
              <Link href="/auth/login">
                {error ? 'Thử lại' : 'Đến trang Đăng nhập'}
              </Link>
            </Button>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export default function VerifyEmailPage() {
  return <VerifyEmailContent />;
}
