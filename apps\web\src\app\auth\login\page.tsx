'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { useAuthStore } from '../../../stores/auth-store';
import { authService } from '../../../services/auth-service';
import { Mail, Lock, Crown } from 'lucide-react';
import { UserRole } from 'shared-types';

// Animation variants following the guidelines
const containerVariants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08, // 80ms stagger between child elements
      duration: 0.3, // 300ms for container fade in
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  show: {
    opacity: 1,
    y: 0,
    transition: {
      type: 'spring',
      stiffness: 200, // Medium spring stiffness
      damping: 20, // Medium damping
      duration: 0.4, // 400ms for individual elements
    },
  },
};

const formControlVariants = {
  hidden: { opacity: 0, x: -20 },
  show: {
    opacity: 1,
    x: 0,
    transition: {
      type: 'spring',
      stiffness: 150, // Light spring stiffness
      damping: 15, // Light damping
      duration: 0.3, // 300ms for form controls
    },
  },
};

export default function LoginPage() {
  const router = useRouter();
  const { setAuth, setLoading, setError, isLoading, error } = useAuthStore();

  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });

  const [formErrors, setFormErrors] = useState<{
    email?: string;
    password?: string;
  }>({});

  useEffect(() => {
    // Check if we have redirect data
    const bookingRedirect = sessionStorage.getItem('bookingRedirect');
    if (!bookingRedirect) {
      // If no redirect data and user navigates away, we don't need to clean up
      return;
    }

    // Only set up cleanup if we have redirect data
    const handleBeforeUnload = () => {
      // Only remove redirect data if user is actually leaving the site
      window.addEventListener('beforeunload', () => {
        sessionStorage.removeItem('bookingRedirect');
      });
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear field-specific error when user starts typing
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors(prev => ({ ...prev, [name]: undefined }));
    }

    // Clear general error
    if (error) {
      setError(null);
    }
  };

  const validateForm = () => {
    const errors: typeof formErrors = {};

    if (!formData.email) {
      errors.email = 'Vui lòng nhập email';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Vui lòng nhập địa chỉ email hợp lệ';
    }

    if (!formData.password) {
      errors.password = 'Vui lòng nhập mật khẩu';
    } else if (formData.password.length < 6) {
      errors.password = 'Mật khẩu phải có ít nhất 6 ký tự';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const loginResponse = await authService.login({
        email: formData.email,
        password: formData.password,
      });

      // Fetch the full user profile using the accessToken from loginResponse
      const fullUser = await authService.getCurrentUser(
        loginResponse.accessToken
      );

      setAuth(fullUser, loginResponse.accessToken);

      // Check for booking redirect data
      const bookingRedirect = sessionStorage.getItem('bookingRedirect');
      console.log('Login successful, checking redirect data:', bookingRedirect); // Debug log

      if (bookingRedirect) {
        // Don't remove the redirect data here - let the homepage handle it
        // This ensures the data is available when the homepage loads
        router.push('/');
      } else {
        // Regular role-based redirect
        if (
          fullUser.role === UserRole.ADMIN ||
          fullUser.role === UserRole.SUPER_ADMIN
        ) {
          router.push('/dashboard');
        } else {
          router.push('/');
        }
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : 'Đăng nhập thất bại. Vui lòng thử lại.';
      setError(errorMessage);
      // Clear any redirect data on login failure
      sessionStorage.removeItem('bookingRedirect');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex">
      {/* Left Panel - Login Form */}
      <motion.div
        className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-gradient-to-br from-gold-50 via-white to-gold-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          className="w-full max-w-md space-y-8"
          variants={containerVariants}
          initial="hidden"
          animate="show"
        >
          <motion.div className="text-center" variants={itemVariants}>
            <motion.div
              className="flex justify-center mb-6"
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <div className="w-16 h-16 bg-gradient-to-br from-gold-500 to-gold-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Crown className="w-8 h-8 text-white" />
              </div>
            </motion.div>
            <motion.h2
              className="text-3xl font-bold bg-gradient-to-r from-gold-700 to-gold-600 bg-clip-text text-transparent"
              variants={itemVariants}
            >
              Chào mừng trở lại
            </motion.h2>
            <motion.p className="mt-2 text-gray-600" variants={itemVariants}>
              Đăng nhập vào tài khoản Queen Karaoke của bạn
            </motion.p>
          </motion.div>

          <motion.form
            onSubmit={handleSubmit}
            className="space-y-6"
            variants={containerVariants}
          >
            {error && (
              <motion.div
                className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-600 text-sm"
                variants={itemVariants}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
              >
                {error}
              </motion.div>
            )}

            <motion.div className="space-y-2" variants={formControlVariants}>
              <Label htmlFor="email" className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-gold-600" />
                Địa chỉ Email
              </Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Nhập email của bạn"
                className={
                  formErrors.email
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                    : ''
                }
                disabled={isLoading}
              />
              {formErrors.email && (
                <motion.p
                  className="text-red-600 text-sm mt-1"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  {formErrors.email}
                </motion.p>
              )}
            </motion.div>

            <motion.div className="space-y-2" variants={formControlVariants}>
              <Label htmlFor="password" className="flex items-center gap-2">
                <Lock className="w-4 h-4 text-gold-600" />
                Mật khẩu
              </Label>
              <Input
                id="password"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="Nhập mật khẩu của bạn"
                className={
                  formErrors.password
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                    : ''
                }
                disabled={isLoading}
              />
              {formErrors.password && (
                <motion.p
                  className="text-red-600 text-sm mt-1"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  {formErrors.password}
                </motion.p>
              )}
            </motion.div>

            <motion.div variants={itemVariants}>
              <Button
                type="submit"
                className={`w-full bg-gradient-to-r from-queens-gold to-gold-600 hover:from-queens-gold/90 hover:to-gold-600/90 text-white font-semibold py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 relative ${
                  isLoading ? 'cursor-not-allowed opacity-80' : ''
                }`}
                disabled={isLoading}
              >
                {isLoading ? (
                  <motion.div
                    className="flex items-center justify-center"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      />
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      />
                    </svg>
                    <span>Đang đăng nhập...</span>
                  </motion.div>
                ) : (
                  <span>Đăng nhập</span>
                )}
              </Button>
            </motion.div>

            <motion.div
              className="text-center text-sm text-gray-600"
              variants={itemVariants}
            >
              Chưa có tài khoản?{' '}
              <Link
                href="/auth/register"
                className="text-gold-600 hover:text-gold-700 font-semibold"
              >
                Đăng ký ngay
              </Link>
            </motion.div>
          </motion.form>
        </motion.div>
      </motion.div>

      {/* Right Panel - Cover Image */}
      <motion.div
        className="hidden lg:block lg:w-1/2 relative overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{
          duration: 0.6,
          delay: 0.2,
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/50 to-black/60 z-10" />
        <motion.div
          className="absolute inset-0 bg-cover bg-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 0.3 }}
          style={{
            backgroundImage: "url('/images/auth/login-cover.jpg')",
          }}
        />
        <motion.div
          className="absolute inset-0 flex items-center justify-center z-20"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="text-center p-8 max-w-2xl mx-auto">
            <motion.div
              className="space-y-6"
              variants={containerVariants}
              initial="hidden"
              animate="show"
            >
              <motion.h1
                className="text-5xl font-bold mb-4 text-white drop-shadow-[0_2px_2px_rgba(0,0,0,0.8)] font-serif"
                variants={itemVariants}
              >
                Queen Karaoke
              </motion.h1>
              <motion.div
                className="w-24 h-1 bg-gradient-to-r from-gold-400 to-gold-600 mx-auto rounded-full shadow-lg"
                variants={itemVariants}
              />
              <motion.p
                className="text-2xl text-gold-100 drop-shadow-[0_2px_2px_rgba(0,0,0,0.8)] font-light tracking-wide"
                variants={itemVariants}
              >
                Trải nghiệm đẳng cấp, dịch vụ hoàng gia
              </motion.p>
            </motion.div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}
