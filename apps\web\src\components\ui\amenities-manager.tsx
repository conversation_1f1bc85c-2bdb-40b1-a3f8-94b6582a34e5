'use client';

import React, { useState } from 'react';
import { Plus, X, Edit3, Check, Tag } from 'lucide-react';
import { Button } from './button';
import { Input } from './input';
import { Label } from './label';
import { LocalizedString } from 'shared-types';

interface AmenitiesManagerProps {
  amenities: LocalizedString[];
  onAmenitiesChange: (amenities: LocalizedString[]) => void;
  disabled?: boolean;
  maxAmenities?: number;
}

const AmenitiesManager: React.FC<AmenitiesManagerProps> = ({
  amenities,
  onAmenitiesChange,
  disabled = false,
  maxAmenities = 10,
}) => {
  const [isAdding, setIsAdding] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [newAmenity, setNewAmenity] = useState<LocalizedString>({
    vi: '',
    en: '',
  });
  const [editAmenity, setEditAmenity] = useState<LocalizedString>({
    vi: '',
    en: '',
  });

  const handleAddAmenity = () => {
    if (newAmenity.vi.trim() || newAmenity.en.trim()) {
      const amenity: LocalizedString = {
        vi: newAmenity.vi.trim(),
        en: newAmenity.en.trim(),
      };
      onAmenitiesChange([...amenities, amenity]);
      setNewAmenity({ vi: '', en: '' });
      setIsAdding(false);
    }
  };

  const handleEditAmenity = (index: number) => {
    if (editAmenity.vi.trim() || editAmenity.en.trim()) {
      const updatedAmenities = [...amenities];
      updatedAmenities[index] = {
        vi: editAmenity.vi.trim(),
        en: editAmenity.en.trim(),
      };
      onAmenitiesChange(updatedAmenities);
      setEditingIndex(null);
      setEditAmenity({ vi: '', en: '' });
    }
  };

  const handleRemoveAmenity = (index: number) => {
    const updatedAmenities = amenities.filter((_, i) => i !== index);
    onAmenitiesChange(updatedAmenities);
  };

  const startEdit = (index: number) => {
    setEditingIndex(index);
    setEditAmenity({ ...amenities[index] });
  };

  const cancelEdit = () => {
    setEditingIndex(null);
    setEditAmenity({ vi: '', en: '' });
  };

  const cancelAdd = () => {
    setIsAdding(false);
    setNewAmenity({ vi: '', en: '' });
  };

  const startAdd = () => {
    setIsAdding(true);
    setEditingIndex(null);
  };

  const canAddMore = amenities.length < maxAmenities && !disabled;

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Tag className="h-5 w-5 text-queens-gold" />
          <h3 className="text-lg font-medium text-gray-900">
            Tiện Nghi ({amenities.length}/{maxAmenities})
          </h3>
        </div>
        {canAddMore && !isAdding && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={startAdd}
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            Thêm tiện nghi
          </Button>
        )}
      </div>

      {/* Add New Amenity Form */}
      {isAdding && (
        <div className="border border-gold-200 rounded-lg p-4 bg-gold-50/30">
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            Thêm tiện nghi mới:
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="new-amenity-vi">Tiếng Việt</Label>
              <Input
                id="new-amenity-vi"
                value={newAmenity.vi}
                onChange={e =>
                  setNewAmenity(prev => ({ ...prev, vi: e.target.value }))
                }
                placeholder="Ví dụ: Máy lạnh"
                className="text-gray-900"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-amenity-en">Tiếng Anh</Label>
              <Input
                id="new-amenity-en"
                value={newAmenity.en}
                onChange={e =>
                  setNewAmenity(prev => ({ ...prev, en: e.target.value }))
                }
                placeholder="Example: Air Conditioning"
                className="text-gray-900"
              />
            </div>
          </div>
          <div className="flex justify-end gap-2 mt-4">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={cancelAdd}
            >
              Hủy
            </Button>
            <Button
              type="button"
              size="sm"
              onClick={handleAddAmenity}
              disabled={!newAmenity.vi.trim() && !newAmenity.en.trim()}
              className="gap-2 bg-queens-gold hover:bg-queens-gold/90 text-white"
            >
              <Check className="h-4 w-4" />
              Thêm
            </Button>
          </div>
        </div>
      )}

      {/* Amenities List */}
      {amenities.length > 0 ? (
        <div className="space-y-3">
          {amenities.map((amenity, index) => (
            <div
              key={index}
              className="border border-gray-200 rounded-lg p-4 bg-white"
            >
              {editingIndex === index ? (
                /* Edit Mode */
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-gray-700">
                    Chỉnh sửa tiện nghi:
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`edit-amenity-vi-${index}`}>
                        Tiếng Việt
                      </Label>
                      <Input
                        id={`edit-amenity-vi-${index}`}
                        value={editAmenity.vi}
                        onChange={e =>
                          setEditAmenity(prev => ({
                            ...prev,
                            vi: e.target.value,
                          }))
                        }
                        placeholder="Tiến nghi bằng tiếng Việt"
                        className="text-gray-900"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`edit-amenity-en-${index}`}>
                        Tiếng Anh
                      </Label>
                      <Input
                        id={`edit-amenity-en-${index}`}
                        value={editAmenity.en}
                        onChange={e =>
                          setEditAmenity(prev => ({
                            ...prev,
                            en: e.target.value,
                          }))
                        }
                        placeholder="Amenity in English"
                        className="text-gray-900"
                      />
                    </div>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={cancelEdit}
                    >
                      Hủy
                    </Button>
                    <Button
                      type="button"
                      size="sm"
                      onClick={() => handleEditAmenity(index)}
                      disabled={
                        !editAmenity.vi.trim() && !editAmenity.en.trim()
                      }
                      className="gap-2 bg-queens-gold hover:bg-queens-gold/90 text-white"
                    >
                      <Check className="h-4 w-4" />
                      Lưu
                    </Button>
                  </div>
                </div>
              ) : (
                /* Display Mode */
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="bg-gold-100 text-gold-700 p-1 rounded-full">
                        <Tag className="h-3 w-3" />
                      </span>
                      <span className="text-sm font-medium text-gray-800">
                        {amenity.vi || amenity.en || 'Tiện nghi không tên'}
                      </span>
                    </div>
                    {amenity.vi && amenity.en && amenity.vi !== amenity.en && (
                      <p className="text-xs text-gray-500 ml-7">{amenity.en}</p>
                    )}
                  </div>
                  {!disabled && (
                    <div className="flex items-center gap-1">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => startEdit(index)}
                        className="h-8 w-8 p-0 text-gray-400 hover:text-gold-600"
                      >
                        <Edit3 className="h-4 w-4" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveAmenity(index)}
                        className="h-8 w-8 p-0 text-gray-400 hover:text-red-600"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        /* Empty State */
        <div className="text-center py-8 border-2 border-dashed border-gray-200 rounded-lg bg-gray-50">
          <Tag className="mx-auto h-12 w-12 text-gray-400 mb-3" />
          <p className="text-gray-500 mb-2">Chưa có tiện nghi nào</p>
          <p className="text-sm text-gray-400 mb-4">
            Thêm các tiện nghi như máy lạnh, âm thanh, TV để mô tả phòng tốt hơn
          </p>
          {canAddMore && (
            <Button
              type="button"
              variant="outline"
              onClick={startAdd}
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              Thêm tiện nghi đầu tiên
            </Button>
          )}
        </div>
      )}

      {/* Tips */}
      <div className="text-xs text-gray-500 bg-blue-50 border border-blue-200 rounded-lg p-3">
        <p className="font-medium text-blue-700 mb-1">💡 Gợi ý:</p>
        <p>
          • Mô tả ngắn gọn, dễ hiểu về tiện nghi (VD: "Máy lạnh", "Micro không
          dây")
        </p>
        <p>• Điền cả tiếng Việt và tiếng Anh để phục vụ đa dạng khách hàng</p>
        <p>• Chỉ liệt kê những tiện nghi thực sự có trong phòng</p>
      </div>
    </div>
  );
};

export default AmenitiesManager;
