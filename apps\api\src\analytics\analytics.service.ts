import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  RoomTypeEnum,
  DashboardAnalyticsDto,
  RoomTypeStatsDto,
  BookingTrendDto,
  PopularRoomDto,
  PeakHoursDto,
} from 'shared-types';

@Injectable()
export class AnalyticsService {
  constructor(private prisma: PrismaService) {}

  async getDashboardStats(): Promise<DashboardAnalyticsDto> {
    const now = new Date();
    const startOfToday = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
    );
    const startOfThisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfPreviousMonth = new Date(
      now.getFullYear(),
      now.getMonth() - 1,
      1,
    );
    const endOfPreviousMonth = new Date(
      now.getFullYear(),
      now.getMonth(),
      0,
      23,
      59,
      59,
    );

    // Parallel queries for better performance
    const [
      totalLocations,
      totalRooms,
      totalBookings,
      todayBookings,
      activeUsers,
      thisMonthBookings,
      previousMonthBookings,
      roomTypeDistribution,
      bookingDurations,
      revenueData,
    ] = await Promise.all([
      // Total active locations
      this.prisma.location.count({
        where: { isActive: true },
      }),

      // Total active rooms
      this.prisma.room.count({
        where: { isActive: true },
      }),

      // Total confirmed bookings
      this.prisma.booking.count({
        where: {
          status: { in: ['CONFIRMED', 'COMPLETED'] },
        },
      }),

      // Today's bookings
      this.prisma.booking.count({
        where: {
          createdAt: { gte: startOfToday },
          status: { in: ['CONFIRMED', 'COMPLETED'] },
        },
      }),

      // Active users (users with bookings in last 30 days)
      this.prisma.user.count({
        where: {
          bookings: {
            some: {
              createdAt: {
                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
              },
            },
          },
        },
      }),

      // This month's bookings and revenue
      this.prisma.booking.aggregate({
        where: {
          createdAt: { gte: startOfThisMonth },
          status: { in: ['CONFIRMED', 'COMPLETED'] },
        },
        _count: true,
        _sum: { totalPrice: true },
      }),

      // Previous month's bookings and revenue
      this.prisma.booking.aggregate({
        where: {
          createdAt: {
            gte: startOfPreviousMonth,
            lte: endOfPreviousMonth,
          },
          status: { in: ['CONFIRMED', 'COMPLETED'] },
        },
        _count: true,
        _sum: { totalPrice: true },
      }),

      // Room type distribution
      this.getRoomTypeDistribution(),

      // Average booking duration
      this.prisma.booking.findMany({
        where: {
          status: { in: ['CONFIRMED', 'COMPLETED'] },
        },
        select: {
          startTime: true,
          endTime: true,
        },
      }),

      // Total revenue
      this.prisma.booking.aggregate({
        where: {
          status: { in: ['CONFIRMED', 'COMPLETED'] },
        },
        _sum: { totalPrice: true },
      }),
    ]);

    // Calculate average booking duration
    const averageBookingDuration =
      bookingDurations.length > 0
        ? bookingDurations.reduce((sum, booking) => {
            const duration =
              (booking.endTime.getTime() - booking.startTime.getTime()) /
              (1000 * 60 * 60); // hours
            return sum + duration;
          }, 0) / bookingDurations.length
        : 0;

    // Calculate occupancy rate (simplified calculation)
    const occupancyRate = await this.calculateOccupancyRate();

    // Convert Decimal to number safely
    const toNumber = (value: any): number => {
      if (value === null || value === undefined) return 0;
      if (typeof value === 'number') return value;
      if (typeof value === 'object' && value.toNumber) return value.toNumber();
      return Number(value) || 0;
    };

    return {
      totalLocations,
      totalRooms,
      totalBookings,
      totalRevenue: toNumber(revenueData._sum.totalPrice),
      todayBookings,
      occupancyRate,
      activeUsers,
      thisMonthRevenue: toNumber(thisMonthBookings._sum.totalPrice),
      thisMonthBookings: thisMonthBookings._count || 0,
      previousMonthRevenue: toNumber(previousMonthBookings._sum.totalPrice),
      previousMonthBookings: previousMonthBookings._count || 0,
      averageBookingDuration,
      roomTypeDistribution,
    };
  }

  async getRoomTypeDistribution(): Promise<RoomTypeStatsDto[]> {
    const roomTypes = await this.prisma.room.groupBy({
      by: ['roomType'],
      where: { isActive: true },
      _count: true,
    });

    const roomTypeStats = await Promise.all(
      roomTypes.map(async (roomType) => {
        const [bookingStats, revenueStats] = await Promise.all([
          this.prisma.booking.count({
            where: {
              room: { roomType: roomType.roomType },
              status: { in: ['CONFIRMED', 'COMPLETED'] },
            },
          }),
          this.prisma.booking.aggregate({
            where: {
              room: { roomType: roomType.roomType },
              status: { in: ['CONFIRMED', 'COMPLETED'] },
            },
            _sum: { totalPrice: true },
          }),
        ]);

        // Calculate occupancy rate for this room type
        const occupancyRate = await this.calculateRoomTypeOccupancyRate(
          roomType.roomType as RoomTypeEnum,
        );

        const toNumber = (value: any): number => {
          if (value === null || value === undefined) return 0;
          if (typeof value === 'number') return value;
          if (typeof value === 'object' && value.toNumber)
            return value.toNumber();
          return Number(value) || 0;
        };

        return {
          roomType: roomType.roomType as RoomTypeEnum,
          count: roomType._count,
          bookings: bookingStats,
          revenue: toNumber(revenueStats._sum.totalPrice),
          occupancyRate,
        };
      }),
    );

    return roomTypeStats;
  }

  async getBookingTrends(days: number = 30): Promise<BookingTrendDto[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const trends = [];
    for (let i = 0; i < days; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      const nextDate = new Date(date);
      nextDate.setDate(nextDate.getDate() + 1);

      const [bookingCount, revenueData] = await Promise.all([
        this.prisma.booking.count({
          where: {
            createdAt: {
              gte: date,
              lt: nextDate,
            },
            status: { in: ['CONFIRMED', 'COMPLETED'] },
          },
        }),
        this.prisma.booking.aggregate({
          where: {
            createdAt: {
              gte: date,
              lt: nextDate,
            },
            status: { in: ['CONFIRMED', 'COMPLETED'] },
          },
          _sum: { totalPrice: true },
        }),
      ]);

      const toNumber = (value: any): number => {
        if (value === null || value === undefined) return 0;
        if (typeof value === 'number') return value;
        if (typeof value === 'object' && value.toNumber)
          return value.toNumber();
        return Number(value) || 0;
      };

      trends.push({
        date: date.toISOString().split('T')[0],
        bookings: bookingCount,
        revenue: toNumber(revenueData._sum.totalPrice),
        occupancyRate: 0, // Will be calculated separately if needed
      });
    }

    return trends;
  }

  async getPopularRooms(limit: number = 10): Promise<PopularRoomDto[]> {
    const bookingsByRoom = await this.prisma.booking.groupBy({
      by: ['roomId'],
      where: {
        status: { in: ['CONFIRMED', 'COMPLETED'] },
      },
      _count: {
        roomId: true,
      },
      _sum: {
        totalPrice: true,
      },
      orderBy: {
        _count: {
          roomId: 'desc',
        },
      },
      take: limit,
    });

    const roomDetails = await Promise.all(
      bookingsByRoom.map(async (booking) => {
        const roomData = await this.prisma.room.findUnique({
          where: { id: booking.roomId },
          include: {
            location: {
              select: { name: true },
            },
          },
        });

        // Safely access the localized name
        const getRoomName = (name: any): string => {
          if (!name) return 'Unknown Room';
          if (typeof name === 'string') return name;
          if (typeof name === 'object') {
            return name.en || name.vi || 'Unknown Room';
          }
          return 'Unknown Room';
        };

        const getLocationName = (name: any): string => {
          if (!name) return 'Unknown Location';
          if (typeof name === 'string') return name;
          if (typeof name === 'object') {
            return name.en || name.vi || 'Unknown Location';
          }
          return 'Unknown Location';
        };

        const toNumber = (value: any): number => {
          if (value === null || value === undefined) return 0;
          if (typeof value === 'number') return value;
          if (typeof value === 'object' && value.toNumber)
            return value.toNumber();
          return Number(value) || 0;
        };

        return {
          roomId: booking.roomId,
          roomName: getRoomName(roomData?.name),
          locationName: getLocationName(roomData?.location?.name),
          bookingCount: booking._count.roomId,
          revenue: toNumber(booking._sum.totalPrice),
          occupancyRate: 0, // Will be calculated if needed
        };
      }),
    );

    return roomDetails;
  }

  async getPeakHours(): Promise<PeakHoursDto[]> {
    const bookings = await this.prisma.booking.findMany({
      where: {
        status: { in: ['CONFIRMED', 'COMPLETED'] },
      },
      select: {
        startTime: true,
        totalPrice: true,
      },
    });

    const hourlyStats = new Map<number, { count: number; revenue: number }>();

    const toNumber = (value: any): number => {
      if (value === null || value === undefined) return 0;
      if (typeof value === 'number') return value;
      if (typeof value === 'object' && value.toNumber) return value.toNumber();
      return Number(value) || 0;
    };

    bookings.forEach((booking) => {
      const hour = booking.startTime.getHours();
      const current = hourlyStats.get(hour) || { count: 0, revenue: 0 };
      hourlyStats.set(hour, {
        count: current.count + 1,
        revenue: current.revenue + toNumber(booking.totalPrice),
      });
    });

    const peakHours = Array.from(hourlyStats.entries()).map(
      ([hour, stats]) => ({
        hour,
        bookingCount: stats.count,
        revenue: stats.revenue,
      }),
    );

    return peakHours.sort((a, b) => b.bookingCount - a.bookingCount);
  }

  private async calculateOccupancyRate(): Promise<number> {
    // Simplified occupancy calculation - can be enhanced based on business logic
    const totalRooms = await this.prisma.room.count({
      where: { isActive: true },
    });

    if (totalRooms === 0) return 0;

    const today = new Date();
    const startOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
    );
    const endOfDay = new Date(startOfDay);
    endOfDay.setDate(endOfDay.getDate() + 1);

    const todayBookings = await this.prisma.booking.count({
      where: {
        startTime: {
          gte: startOfDay,
          lt: endOfDay,
        },
        status: { in: ['CONFIRMED', 'COMPLETED'] },
      },
    });

    // Simplified calculation: assume each booking represents 1 room-hour of occupancy
    // In reality, this would need more sophisticated calculation based on actual time slots
    const potentialBookingSlots = totalRooms * 12; // Assuming 12 hours of operation per day
    return Math.min((todayBookings / potentialBookingSlots) * 100, 100);
  }

  private async calculateRoomTypeOccupancyRate(
    roomType: RoomTypeEnum,
  ): Promise<number> {
    const roomCount = await this.prisma.room.count({
      where: {
        roomType,
        isActive: true,
      },
    });

    if (roomCount === 0) return 0;

    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    const monthlyBookings = await this.prisma.booking.count({
      where: {
        room: { roomType },
        startTime: { gte: startOfMonth },
        status: { in: ['CONFIRMED', 'COMPLETED'] },
      },
    });

    // Simplified monthly occupancy rate
    const daysInMonth = new Date(
      today.getFullYear(),
      today.getMonth() + 1,
      0,
    ).getDate();
    const potentialSlots = roomCount * daysInMonth * 12; // 12 hours per day
    return Math.min((monthlyBookings / potentialSlots) * 100, 100);
  }
}
