'use client';

import React, { useState, useEffect, useCallback, memo } from 'react';
import { Clock, RotateCcw, Copy, Calendar } from 'lucide-react';
import { Label } from '../ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Button } from '../ui/button';
import { OperatingHours } from 'shared-types';

interface OperatingHoursEditorProps {
  value?: OperatingHours;
  onChange: (hours: OperatingHours) => void;
  errors?: string;
}

interface DayHours {
  openTime: string;
  closeTime: string;
  isClosed: boolean;
}

interface WeekSchedule {
  monday: DayHours;
  tuesday: DayHours;
  wednesday: DayHours;
  thursday: DayHours;
  friday: DayHours;
  saturday: DayHours;
  sunday: DayHours;
}

const DAYS_OF_WEEK = [
  { key: 'monday', label: 'Thứ Hai', shortLabel: 'T2' },
  { key: 'tuesday', label: 'Th<PERSON> <PERSON>', shortLabel: 'T3' },
  { key: 'wednesday', label: 'Th<PERSON> Tư', shortLabel: 'T4' },
  { key: 'thursday', label: 'Th<PERSON> Năm', shortLabel: 'T5' },
  { key: 'friday', label: 'Thứ Sáu', shortLabel: 'T6' },
  { key: 'saturday', label: 'Thứ Bảy', shortLabel: 'T7' },
  { key: 'sunday', label: 'Chủ Nhật', shortLabel: 'CN' },
] as const;

const DEFAULT_HOURS: DayHours = {
  openTime: '09:00',
  closeTime: '00:00',
  isClosed: false,
};

// Generate time options from 00:00 to 23:30
const generateTimeOptions = (): string[] => {
  const options = [];
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const timeStr = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      options.push(timeStr);
    }
  }
  return options;
};

const TIME_OPTIONS = generateTimeOptions();

export const OperatingHoursEditor: React.FC<OperatingHoursEditorProps> = memo(
  ({ value, onChange, errors }) => {
    const [schedule, setSchedule] = useState<WeekSchedule>(() => {
      // Initialize schedule from props or with default values
      const initialSchedule: WeekSchedule = {} as WeekSchedule;

      DAYS_OF_WEEK.forEach(({ key }) => {
        if (value && value[key as keyof OperatingHours]) {
          const dayValue = value[key as keyof OperatingHours];
          if (dayValue && dayValue.includes('-')) {
            const [openTime, closeTime] = dayValue.split('-');
            initialSchedule[key as keyof WeekSchedule] = {
              openTime: openTime.trim(),
              closeTime: closeTime.trim(),
              isClosed: false,
            };
          } else {
            initialSchedule[key as keyof WeekSchedule] = {
              ...DEFAULT_HOURS,
              isClosed: true,
            };
          }
        } else {
          initialSchedule[key as keyof WeekSchedule] = { ...DEFAULT_HOURS };
        }
      });

      return initialSchedule;
    });

    // Convert schedule to OperatingHours format whenever schedule changes
    useEffect(() => {
      const operatingHours: OperatingHours = {};

      DAYS_OF_WEEK.forEach(({ key }) => {
        const daySchedule = schedule[key as keyof WeekSchedule];
        if (daySchedule.isClosed) {
          operatingHours[key as keyof OperatingHours] = undefined;
        } else {
          operatingHours[key as keyof OperatingHours] =
            `${daySchedule.openTime}-${daySchedule.closeTime}`;
        }
      });

      onChange(operatingHours);
    }, [schedule, onChange]);

    const updateDaySchedule = useCallback(
      (day: keyof WeekSchedule, updates: Partial<DayHours>) => {
        setSchedule(prev => ({
          ...prev,
          [day]: { ...prev[day], ...updates },
        }));
      },
      []
    );

    const toggleDayStatus = useCallback((day: keyof WeekSchedule) => {
      setSchedule(prev => ({
        ...prev,
        [day]: {
          ...prev[day],
          isClosed: !prev[day].isClosed,
        },
      }));
    }, []);

    const copyHoursToAll = useCallback(
      (day: keyof WeekSchedule) => {
        const sourceDay = schedule[day];
        if (sourceDay.isClosed) return;

        const newSchedule: WeekSchedule = {} as WeekSchedule;
        DAYS_OF_WEEK.forEach(({ key }) => {
          newSchedule[key as keyof WeekSchedule] = {
            openTime: sourceDay.openTime,
            closeTime: sourceDay.closeTime,
            isClosed: false,
          };
        });

        setSchedule(newSchedule);
      },
      [schedule]
    );

    const resetToDefault = useCallback(() => {
      const newSchedule: WeekSchedule = {} as WeekSchedule;
      DAYS_OF_WEEK.forEach(({ key }) => {
        newSchedule[key as keyof WeekSchedule] = { ...DEFAULT_HOURS };
      });
      setSchedule(newSchedule);
    }, []);

    const formatTimeDisplay = useCallback((time: string): string => {
      if (time === '00:00') return '00:00 (nửa đêm)';
      const [hours, minutes] = time.split(':');
      const hourNum = parseInt(hours);
      if (hourNum >= 12) {
        return `${time} (${hourNum === 12 ? 'trưa' : 'chiều'})`;
      }
      return `${time} (sáng)`;
    }, []);

    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-queens-gold" />
            <Label className="text-h4 font-medium text-gray-700">
              Giờ Hoạt Động
            </Label>
          </div>
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={resetToDefault}
              className="gap-1 text-xs"
            >
              <RotateCcw className="h-3 w-3" />
              Đặt lại
            </Button>
          </div>
        </div>

        {/* Days Schedule */}
        <div className="space-y-3">
          {DAYS_OF_WEEK.map(({ key, label, shortLabel }) => {
            const daySchedule = schedule[key as keyof WeekSchedule];

            return (
              <div
                key={key}
                className={`grid grid-cols-1 md:grid-cols-12 gap-3 p-4 rounded-lg border transition-all duration-200 ${
                  daySchedule.isClosed
                    ? 'bg-gray-50 border-gray-200'
                    : 'bg-white border-gray-200 hover:border-gold-300'
                }`}
              >
                {/* Day Label & Status Toggle */}
                <div className="md:col-span-3 flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-full bg-gold-100 flex items-center justify-center">
                      <Calendar className="h-4 w-4 text-gold-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-700">{label}</p>
                      <p className="text-xs text-gray-500">{shortLabel}</p>
                    </div>
                  </div>

                  <button
                    type="button"
                    onClick={() => toggleDayStatus(key as keyof WeekSchedule)}
                    className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                      daySchedule.isClosed
                        ? 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                        : 'bg-green-100 text-green-700 hover:bg-green-200'
                    }`}
                  >
                    {daySchedule.isClosed ? 'Đóng cửa' : 'Mở cửa'}
                  </button>
                </div>

                {/* Time Selectors */}
                {!daySchedule.isClosed && (
                  <>
                    {/* Open Time */}
                    <div className="md:col-span-3">
                      <Label className="block text-sm text-gray-600 mb-1">
                        Giờ mở
                      </Label>
                      <Select
                        value={daySchedule.openTime}
                        onValueChange={time =>
                          updateDaySchedule(key as keyof WeekSchedule, {
                            openTime: time,
                          })
                        }
                      >
                        <SelectTrigger className="text-black">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {TIME_OPTIONS.map(time => (
                            <SelectItem key={`open-${time}`} value={time}>
                              <span className="text-black">
                                {formatTimeDisplay(time)}
                              </span>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Close Time */}
                    <div className="md:col-span-3">
                      <Label className="block text-sm text-gray-600 mb-1">
                        Giờ đóng
                      </Label>
                      <Select
                        value={daySchedule.closeTime}
                        onValueChange={time =>
                          updateDaySchedule(key as keyof WeekSchedule, {
                            closeTime: time,
                          })
                        }
                      >
                        <SelectTrigger className="text-black">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {TIME_OPTIONS.map(time => (
                            <SelectItem key={`close-${time}`} value={time}>
                              <span className="text-black">
                                {formatTimeDisplay(time)}
                              </span>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Copy Action */}
                    <div className="md:col-span-3 flex items-end">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          copyHoursToAll(key as keyof WeekSchedule)
                        }
                        className="w-full gap-1 text-xs"
                      >
                        <Copy className="h-3 w-3" />
                        Áp dụng cho tất cả
                      </Button>
                    </div>
                  </>
                )}

                {/* Closed Day Placeholder */}
                {daySchedule.isClosed && (
                  <div className="md:col-span-9 flex items-center">
                    <p className="text-sm text-gray-500 italic">
                      Chi nhánh đóng cửa vào ngày này
                    </p>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Summary */}
        <div className="bg-gold-50 border border-gold-200 rounded-lg p-4">
          <h4 className="font-medium text-gray-700 mb-2 flex items-center gap-2">
            <Clock className="h-4 w-4 text-gold-600" />
            Tóm tắt giờ hoạt động
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
            {DAYS_OF_WEEK.map(({ key, shortLabel }) => {
              const daySchedule = schedule[key as keyof WeekSchedule];
              return (
                <div key={key} className="flex justify-between">
                  <span className="font-medium text-gray-600">
                    {shortLabel}:
                  </span>
                  <span className="text-gray-800">
                    {daySchedule.isClosed
                      ? 'Đóng'
                      : `${daySchedule.openTime}-${daySchedule.closeTime}`}
                  </span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Error Display */}
        {errors && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-sm text-red-600">{errors}</p>
          </div>
        )}

        {/* Helper Text */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <p className="text-sm text-blue-700">
            <strong>Lưu ý:</strong> Giờ 00:00 có nghĩa là nửa đêm (kết thúc
            ngày). Ví dụ: 18:00-02:00 nghĩa là mở từ 6h chiều đến 2h sáng hôm
            sau.
          </p>
        </div>
      </div>
    );
  }
);

OperatingHoursEditor.displayName = 'OperatingHoursEditor';
