'use client';

import React from 'react';
import { LucideIcon } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { cn } from '../../lib/utils';

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  icon: LucideIcon;
  iconColor: string;
  valueColor: string;
  growth?: {
    value: number;
    trend: 'up' | 'down' | 'stable';
  };
  onClick?: () => void;
  loading?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon: Icon,
  iconColor,
  valueColor,
  growth,
  onClick,
  loading = false,
}) => {
  const formatGrowth = (growthValue: number) => {
    const abs = Math.abs(growthValue);
    const sign = growthValue >= 0 ? '+' : '-';
    return `${sign}${abs.toFixed(1)}%`;
  };

  const getGrowthColor = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-gray-500';
    }
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return '↗';
      case 'down':
        return '↘';
      default:
        return '→';
    }
  };

  if (loading) {
    return (
      <Card className="animate-pulse transition-all duration-300">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="h-4 bg-gray-200 rounded w-20"></div>
          <div className="w-6 h-6 bg-gray-200 rounded"></div>
        </CardHeader>
        <CardContent>
          <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-24"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      className={`luxury-hover transition-all duration-300 transform hover:scale-105 ${
        onClick ? 'cursor-pointer' : ''
      } animate-in fade-in slide-in-from-bottom duration-500`}
      onClick={onClick}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-600 transition-all duration-200">
          {title}
        </CardTitle>
        <Icon className={`w-6 h-6 ${iconColor} transition-all duration-200`} />
      </CardHeader>
      <CardContent>
        <div
          className={`text-2xl font-bold ${valueColor} mb-1 transition-all duration-300`}
        >
          {typeof value === 'number' ? value.toLocaleString('vi-VN') : value}
        </div>
        <div className="flex items-center justify-between">
          <p className="text-xs text-gray-600 transition-all duration-200">
            {subtitle}
          </p>
          {growth && (
            <div
              className={`text-xs flex items-center ${getGrowthColor(growth.trend)} 
                transition-all duration-300 animate-in slide-in-from-right delay-100`}
            >
              <span className="mr-1">{getTrendIcon(growth.trend)}</span>
              {formatGrowth(growth.value)}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default StatCard;
