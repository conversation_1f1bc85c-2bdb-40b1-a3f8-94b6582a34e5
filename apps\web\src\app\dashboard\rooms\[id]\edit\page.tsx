'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, Hotel, ImageIcon, Tag } from 'lucide-react';
import { roomsService } from '../../../../../services/rooms.service';
import { locationsService } from '../../../../../services/locations.service';
import {
  UpdateRoomDto,
  Room,
  Location,
  RoomTypeEnum,
  LocalizedString,
  DecorStyle,
} from 'shared-types';
import { Button } from '../../../../../components/ui/button';
import { Input } from '../../../../../components/ui/input';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../../../components/ui/card';
import { Label } from '../../../../../components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../../components/ui/select';
import ImageUpload from '../../../../../components/ui/image-upload';
import AmenitiesManager from '../../../../../components/ui/amenities-manager';
import { useDialogs } from '../../../../../components/ui/modal-provider';

interface FormData {
  nameVi: string;
  nameEn: string;
  descriptionVi: string;
  descriptionEn: string;
  capacity: number;
  pricePerHour: number;
  roomType: RoomTypeEnum | '';
  decorStyle: DecorStyle | '';
  isActive: boolean;
  images: File[];
  amenities: LocalizedString[];
}

interface FormErrors {
  [key: string]: string;
}

interface PageProps {
  params: Promise<{ id: string }>;
}

export default function EditRoomPage({ params }: PageProps) {
  const router = useRouter();
  const { alert } = useDialogs();
  const resolvedParams = use(params);
  const { id: roomId } = resolvedParams;

  const [room, setRoom] = useState<Room | null>(null);
  const [location, setLocation] = useState<Location | null>(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState<FormData>({
    nameVi: '',
    nameEn: '',
    descriptionVi: '',
    descriptionEn: '',
    capacity: 1,
    pricePerHour: 100000,
    roomType: '',
    decorStyle: DecorStyle.MODERN,
    isActive: true,
    images: [],
    amenities: [],
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadingImages, setUploadingImages] = useState(false);

  useEffect(() => {
    const fetchRoomDetails = async () => {
      if (!roomId || typeof roomId !== 'string') {
        setLoading(false);
        console.error(
          'Room ID is missing, not a string, or not a UUID from the URL parameter.'
        );
        await alert('ID phòng không hợp lệ trong URL.', 'Lỗi', 'error');
        router.push('/dashboard/rooms');
        return;
      }

      try {
        setLoading(true);
        let fetchedRoom: Room | null = null;
        let fetchedLocation: Location | null = null;

        // Get all locations and find the room
        const locationsResponse = await locationsService.getLocations({
          page: 1,
          limit: 100,
        });
        if (!locationsResponse || !locationsResponse.items) {
          throw new Error('Could not fetch locations.');
        }

        for (const loc of locationsResponse.items) {
          if (!loc.id || typeof loc.id !== 'string') {
            console.warn(`Skipping location with invalid ID: ${loc.id}`);
            continue;
          }
          try {
            const roomAttempt = await roomsService.getRoom(loc.id, roomId);
            if (roomAttempt) {
              fetchedRoom = roomAttempt;
              if (fetchedRoom.locationId) {
                fetchedLocation = await locationsService.getLocation(
                  fetchedRoom.locationId
                );
              }
              break;
            }
          } catch (error) {
            // This error is expected if the room is not in this location
          }
        }

        if (
          fetchedRoom &&
          fetchedLocation &&
          fetchedRoom.locationId === fetchedLocation.id
        ) {
          setRoom(fetchedRoom);
          setLocation(fetchedLocation);

          setFormData({
            nameVi: fetchedRoom.name?.vi || '',
            nameEn: fetchedRoom.name?.en || '',
            descriptionVi: fetchedRoom.description?.vi || '',
            descriptionEn: fetchedRoom.description?.en || '',
            capacity: fetchedRoom.capacity || 1,
            pricePerHour: fetchedRoom.pricePerHour || 100000,
            roomType: fetchedRoom.roomType || '',
            decorStyle: fetchedRoom.decorStyle || DecorStyle.MODERN,
            isActive: fetchedRoom.isActive ?? true,
            images: [], // New images to upload
            amenities: fetchedRoom.amenities || [],
          });
        } else {
          console.error(
            `Room with ID ${roomId} not found, or its location could not be determined, or location ID mismatch.`
          );
          await alert(
            'Không tìm thấy thông tin phòng hoặc chi nhánh liên quan.',
            'Lỗi',
            'error'
          );
        }
        setLoading(false);
      } catch (error) {
        console.error('Error fetching room details:', error);
        await alert(
          'Đã xảy ra lỗi nghiêm trọng khi tải thông tin phòng.',
          'Lỗi',
          'error'
        );
        setLoading(false);
      }
    };

    fetchRoomDetails();
  }, [roomId, router, alert]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.nameVi.trim()) {
      newErrors.nameVi = 'Tên tiếng Việt là bắt buộc';
    }

    if (!formData.nameEn.trim()) {
      newErrors.nameEn = 'Tên tiếng Anh là bắt buộc';
    }

    if (!formData.roomType) {
      newErrors.roomType = 'Vui lòng chọn loại phòng';
    }

    if (!formData.decorStyle) {
      newErrors.decorStyle = 'Vui lòng chọn phong cách trang trí';
    }

    if (formData.capacity < 1) {
      newErrors.capacity = 'Sức chứa phải lớn hơn 0';
    }

    if (formData.pricePerHour < 1000) {
      newErrors.pricePerHour = 'Giá phòng phải lớn hơn 1,000 VND';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (
    field: keyof FormData,
    value: string | number | boolean | File[] | LocalizedString[]
  ) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleExistingImageRemove = async (imageUrl: string) => {
    if (!room || !location) return;

    try {
      // Remove the image from the existing images array
      const updatedImages = room.images?.filter(img => img !== imageUrl) || [];

      // Update the room with the new images array
      await roomsService.updateRoom(location.id, room.id, {
        images: updatedImages,
      });

      // Update local state
      setRoom(prev => (prev ? { ...prev, images: updatedImages } : null));
    } catch (error) {
      console.error('Error removing image:', error);
      await alert('Có lỗi xảy ra khi xóa hình ảnh.', 'Lỗi', 'error');
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!validateForm()) return;
    if (!room || !location) return;

    setIsSubmitting(true);
    try {
      const updateData: UpdateRoomDto = {
        name: { vi: formData.nameVi, en: formData.nameEn },
        description: { vi: formData.descriptionVi, en: formData.descriptionEn },
        capacity: formData.capacity,
        pricePerHour: formData.pricePerHour,
        roomType: formData.roomType as RoomTypeEnum,
        decorStyle: formData.decorStyle as DecorStyle,
        isActive: formData.isActive,
        amenities: formData.amenities,
      };

      await roomsService.updateRoom(location.id, room.id, updateData);

      if (formData.images.length > 0) {
        setUploadingImages(true);
        try {
          await roomsService.uploadRoomImages(
            location.id,
            room.id,
            formData.images
          );
        } catch (imageError) {
          console.error('Error uploading images:', imageError);
          await alert(
            'Phòng đã cập nhật thành công nhưng có lỗi khi tải hình ảnh.',
            'Cảnh báo',
            'warning'
          );
        } finally {
          setUploadingImages(false);
        }
      }

      await alert(
        'Phòng đã được cập nhật thành công!',
        'Thành công',
        'success'
      );
      router.push('/dashboard/rooms');
    } catch (error) {
      console.error('Error updating room:', error);
      await alert(
        'Có lỗi xảy ra khi cập nhật phòng. Vui lòng thử lại.',
        'Lỗi',
        'error'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to get current room type display name
  const getCurrentRoomTypeLabel = (): string => {
    if (!formData.roomType) return 'Chọn loại phòng';
    return roomsService.getRoomTypeDisplayName(
      formData.roomType as RoomTypeEnum
    );
  };

  // Helper function to get current decor style display name
  const getCurrentDecorStyleLabel = (): string => {
    if (!formData.decorStyle) return 'Chọn phong cách trang trí';
    return roomsService.getDecorStyleDisplayName(formData.decorStyle);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-queens-gold"></div>
      </div>
    );
  }

  if (!room || !location) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-h1 font-bold text-gray-900">Chỉnh Sửa Phòng</h1>
          </div>
        </div>

        <Card>
          <CardContent className="p-12 text-center">
            <Hotel className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Không tìm thấy phòng
            </h3>
            <p className="text-gray-600 mb-6">
              Phòng này có thể đã bị xóa hoặc không tồn tại
            </p>
            <Button
              onClick={() => router.push('/dashboard/rooms')}
              className="bg-queens-gold hover:bg-queens-gold/90 text-white font-medium shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
            >
              Quay lại danh sách phòng
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={() => router.back()} className="p-2">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-h1 font-bold text-gray-900">Chỉnh Sửa Phòng</h1>
          <p className="text-lg text-gray-600">
            Cập nhật thông tin phòng karaoke
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Hotel className="h-5 w-5 text-queens-gold" />
              Thông Tin Cơ Bản
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="roomType">Loại phòng *</Label>
              <Select
                value={formData.roomType}
                onValueChange={value =>
                  handleInputChange('roomType', value as RoomTypeEnum)
                }
              >
                <SelectTrigger
                  className={
                    errors.roomType ? 'border-red-500 text-black' : 'text-black'
                  }
                >
                  <span className="text-black">
                    {getCurrentRoomTypeLabel()}
                  </span>
                </SelectTrigger>
                <SelectContent>
                  {Object.values(RoomTypeEnum).map(type => (
                    <SelectItem key={type} value={type}>
                      <span className="text-black">
                        {roomsService.getRoomTypeDisplayName(type)}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.roomType && (
                <p className="text-sm text-red-600">{errors.roomType}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="decorStyle">Phong cách trang trí *</Label>
              <Select
                value={formData.decorStyle}
                onValueChange={value =>
                  handleInputChange('decorStyle', value as DecorStyle)
                }
              >
                <SelectTrigger
                  className={
                    errors.decorStyle
                      ? 'border-red-500 text-black'
                      : 'text-black'
                  }
                >
                  <span className="text-black">
                    {getCurrentDecorStyleLabel()}
                  </span>
                </SelectTrigger>
                <SelectContent>
                  {Object.values(DecorStyle).map(style => (
                    <SelectItem key={style} value={style}>
                      <span className="text-black">
                        {roomsService.getDecorStyleDisplayName(style)}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.decorStyle && (
                <p className="text-sm text-red-600">{errors.decorStyle}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="nameVi">Tên tiếng Việt *</Label>
              <Input
                id="nameVi"
                value={formData.nameVi}
                onChange={e => handleInputChange('nameVi', e.target.value)}
                placeholder="VD: Phòng Ruby VIP 01"
                className={errors.nameVi ? 'border-red-500' : ''}
              />
              {errors.nameVi && (
                <p className="text-sm text-red-600">{errors.nameVi}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="nameEn">Tên tiếng Anh *</Label>
              <Input
                id="nameEn"
                value={formData.nameEn}
                onChange={e => handleInputChange('nameEn', e.target.value)}
                placeholder="EG: Ruby VIP Room 01"
                className={errors.nameEn ? 'border-red-500' : ''}
              />
              {errors.nameEn && (
                <p className="text-sm text-red-600">{errors.nameEn}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="capacity">Sức chứa (người) *</Label>
                <Input
                  id="capacity"
                  type="number"
                  min="1"
                  max="50"
                  value={formData.capacity}
                  onChange={e =>
                    handleInputChange('capacity', parseInt(e.target.value) || 1)
                  }
                  className={errors.capacity ? 'border-red-500' : ''}
                />
                {errors.capacity && (
                  <p className="text-sm text-red-600">{errors.capacity}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="pricePerHour">Giá/giờ (VND) *</Label>
                <Input
                  id="pricePerHour"
                  type="number"
                  min="1000"
                  value={formData.pricePerHour}
                  onChange={e =>
                    handleInputChange(
                      'pricePerHour',
                      parseInt(e.target.value) || 100000
                    )
                  }
                  className={errors.pricePerHour ? 'border-red-500' : ''}
                />
                {errors.pricePerHour && (
                  <p className="text-sm text-red-600">{errors.pricePerHour}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="isActive">Trạng thái</Label>
              <Select
                value={formData.isActive ? 'true' : 'false'}
                onValueChange={value =>
                  handleInputChange('isActive', value === 'true')
                }
              >
                <SelectTrigger className="text-black">
                  <span className="text-black">
                    {formData.isActive ? 'Đang hoạt động' : 'Ngừng hoạt động'}
                  </span>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">
                    <span className="text-black">Đang hoạt động</span>
                  </SelectItem>
                  <SelectItem value="false">
                    <span className="text-black">Ngừng hoạt động</span>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="descriptionVi">Mô tả tiếng Việt</Label>
              <textarea
                id="descriptionVi"
                value={formData.descriptionVi}
                onChange={e =>
                  handleInputChange('descriptionVi', e.target.value)
                }
                placeholder="Phòng VIP với âm thanh chất lượng cao và không gian sang trọng"
                rows={3}
                className="w-full p-2 border rounded-md text-gray-900 border-gray-300"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="descriptionEn">Mô tả tiếng Anh</Label>
              <textarea
                id="descriptionEn"
                value={formData.descriptionEn}
                onChange={e =>
                  handleInputChange('descriptionEn', e.target.value)
                }
                placeholder="VIP room with high-quality sound system and luxurious space"
                rows={3}
                className="w-full p-2 border rounded-md text-gray-900 border-gray-300"
              />
            </div>
          </CardContent>
        </Card>

        {/* Image Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5 text-queens-gold" />
              Quản Lý Hình Ảnh
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ImageUpload
              images={formData.images}
              onImagesChange={images => handleInputChange('images', images)}
              existingImages={room.images || []}
              onExistingImageRemove={handleExistingImageRemove}
              maxImages={5}
              disabled={isSubmitting || uploadingImages}
            />
          </CardContent>
        </Card>

        {/* Amenities Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5 text-queens-gold" />
              Quản Lý Tiện Nghi
            </CardTitle>
          </CardHeader>
          <CardContent>
            <AmenitiesManager
              amenities={formData.amenities}
              onAmenitiesChange={amenities =>
                handleInputChange('amenities', amenities)
              }
              disabled={isSubmitting}
              maxAmenities={10}
            />
          </CardContent>
        </Card>

        {/* Location Information */}
        <Card>
          <CardHeader>
            <CardTitle>Thông Tin Chi Nhánh</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600 mb-2">Phòng này thuộc về:</p>
              <p className="font-medium text-gray-900">
                {location.name?.vi || location.name?.en || 'Không có tên'}
              </p>
              <p className="text-sm text-gray-600 mt-1">{location.address}</p>
            </div>
          </CardContent>
        </Card>

        {errors.general && (
          <Card className="border-red-500 bg-red-50">
            <CardContent className="p-4">
              <p className="text-sm text-red-700">{errors.general}</p>
            </CardContent>
          </Card>
        )}

        <div className="flex gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting || uploadingImages}
          >
            Hủy
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || uploadingImages}
            className="gap-2 bg-queens-gold hover:bg-queens-gold/90 text-white font-medium shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
          >
            {isSubmitting || uploadingImages ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                {uploadingImages ? 'Đang tải hình ảnh...' : 'Đang cập nhật...'}
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                Cập Nhật Phòng
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
