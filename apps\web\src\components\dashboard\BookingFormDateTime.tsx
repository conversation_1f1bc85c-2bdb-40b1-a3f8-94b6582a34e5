'use client';

import React, { useEffect, useState, useCallback } from 'react';
import {
  Calendar,
  Clock,
  Users,
  AlertTriangle,
  CheckCircle,
} from 'lucide-react';
import { Room, Location } from 'shared-types';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { useAvailabilityCheck } from '../../hooks/useAvailabilityCheck';
import { AvailabilityStatus } from '../booking/AvailabilityStatus';
import type { AvailabilityState } from '../../hooks/useAvailabilityCheck';

interface BookingFormData {
  locationId: string;
  roomId: string;
  startTime: string;
  endTime: string;
  numberOfGuests: number;
  isGuestBooking: boolean;
  userId?: string;
  guestName?: string;
  guestEmail?: string;
  guestPhone?: string;
  notes?: string;
}

interface BookingFormDateTimeProps {
  formData: BookingFormData;
  selectedRoom: Room | null;
  selectedLocation: Location | null;
  errors: Partial<Record<keyof BookingFormData, string>>;
  onUpdate: (updates: Partial<BookingFormData>) => void;
  onAvailabilityChange?: (availability: AvailabilityState) => void;
}

const getMinDate = () => {
  return new Date().toISOString().split('T')[0];
};

const getMaxDate = () => {
  const maxDate = new Date();
  maxDate.setMonth(maxDate.getMonth() + 3);
  return maxDate.toISOString().split('T')[0];
};

// Fallback time options (8 AM to 11:30 PM in 30-minute intervals)
const generateDefaultTimeOptions = () => {
  const options = [];
  for (let hour = 8; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      options.push(time);
    }
  }
  return options;
};

// Generate time options based on location's operating hours or fallback to default
const generateTimeOptions = (
  location: Location,
  selectedDate?: string
): string[] => {
  if (!location?.operatingHours || !selectedDate) {
    return generateDefaultTimeOptions();
  }

  try {
    const operatingHours =
      typeof location.operatingHours === 'string'
        ? JSON.parse(location.operatingHours)
        : location.operatingHours;

    const date = new Date(selectedDate);
    const dayNames = [
      'sunday',
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
    ];
    const dayName = dayNames[date.getDay()];

    const hoursForDay = operatingHours[dayName];
    if (!hoursForDay || !hoursForDay.includes('-')) {
      return [];
    }

    const [openTimeStr, closeTimeStr] = hoursForDay.split('-');
    const [openHour, openMinute] = openTimeStr.split(':').map(Number);
    const [closeHour, closeMinute] = closeTimeStr.split(':').map(Number);

    const options = [];
    let currentHour = openHour;
    let currentMinute = openMinute;

    const crossesMidnight =
      closeHour < openHour ||
      (closeHour === openHour && closeMinute <= openMinute);
    const endHour = crossesMidnight ? closeHour + 24 : closeHour;
    const endMinute = closeMinute;

    while (true) {
      const displayHour = currentHour >= 24 ? currentHour - 24 : currentHour;
      const timeStr = `${displayHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
      options.push(timeStr);

      const currentTotalMinutes = currentHour * 60 + currentMinute;
      const endTotalMinutes = endHour * 60 + endMinute;

      if (currentTotalMinutes >= endTotalMinutes) {
        break;
      }

      currentMinute += 30;
      if (currentMinute >= 60) {
        currentMinute = 0;
        currentHour++;
      }

      if (options.length > 48) {
        break;
      }
    }
    return options;
  } catch (error) {
    console.error('Error parsing operating hours:', error);
    return generateDefaultTimeOptions();
  }
};

const BookingFormDateTime: React.FC<BookingFormDateTimeProps> = ({
  formData,
  selectedRoom,
  selectedLocation,
  errors,
  onUpdate,
  onAvailabilityChange,
}) => {
  const [availableTimeOptions, setAvailableTimeOptions] = useState<string[]>(
    []
  );
  const [estimatedPrice, setEstimatedPrice] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);

  // Use the new availability checking hook
  const availability = useAvailabilityCheck();

  // Helper to get the current time string (HH:MM:SS) or a default
  const extractTimePart = (
    isoString: string | undefined,
    defaultTime: string = '00:00:00'
  ): string => {
    if (isoString && isoString.includes('T')) {
      const parts = isoString.split('T');
      if (parts.length > 1 && parts[1].match(/\d{2}:\d{2}:\d{2}/)) {
        return parts[1];
      }
      // If time part is present but not full HH:MM:SS (e.g. HH:MM), attempt to complete it
      if (parts.length > 1 && parts[1].match(/\d{2}:\d{2}/)) {
        return `${parts[1]}:00`;
      }
    }
    return defaultTime;
  };

  // Updated to use location's operating hours for time generation
  useEffect(() => {
    if (formData.startTime && selectedLocation) {
      const selectedDate = formData.startTime.split('T')[0];
      const newTimeOptions = generateTimeOptions(
        selectedLocation,
        selectedDate
      );
      setAvailableTimeOptions(newTimeOptions);
    } else {
      setAvailableTimeOptions(generateDefaultTimeOptions());
    }
  }, [formData.startTime, selectedLocation]);

  // Get valid end time options based on start time
  const getValidEndTimeOptions = useCallback(
    (startTimeHM: string | null): string[] => {
      if (!startTimeHM || !availableTimeOptions.length) {
        return availableTimeOptions;
      }

      const startIndex = availableTimeOptions.indexOf(startTimeHM);
      if (startIndex === -1) {
        return availableTimeOptions;
      }

      // Return only times after the start time
      return availableTimeOptions.slice(startIndex + 1);
    },
    [availableTimeOptions]
  );

  const createISOString = (dateStr: string, timeStr: string): string => {
    return `${dateStr}T${timeStr}:00`;
  };

  // Calculate duration and price when times change
  useEffect(() => {
    if (formData.startTime && formData.endTime && selectedRoom) {
      const start = new Date(formData.startTime);
      let end = new Date(formData.endTime);

      // Handle midnight crossover: if end time is earlier than start time,
      // assume end time is on the next day
      if (end <= start) {
        end = new Date(end.getTime() + 24 * 60 * 60 * 1000); // Add 24 hours
      }

      if (start < end) {
        const durationMs = end.getTime() - start.getTime();
        const durationHours = durationMs / (1000 * 60 * 60);
        const price = durationHours * selectedRoom.pricePerHour;

        setDuration(durationHours);
        setEstimatedPrice(price);
      } else {
        setDuration(0);
        setEstimatedPrice(0);
      }
    }
  }, [formData.startTime, formData.endTime, selectedRoom]);

  // Real availability check - replace the old mocked implementation
  useEffect(() => {
    if (formData.startTime && formData.endTime && formData.roomId) {
      // Use the real availability check
      availability.checkAvailability(
        formData.roomId,
        formData.startTime,
        formData.endTime
      );
    } else {
      // Clear availability when inputs are incomplete
      availability.clearAvailability();
    }
  }, [formData.startTime, formData.endTime, formData.roomId]);

  // Notify parent component about availability changes
  useEffect(() => {
    if (onAvailabilityChange) {
      onAvailabilityChange(availability);
    }
  }, [
    availability.status,
    availability.error,
    availability.isChecking,
    availability.data?.isGenerallyAvailable,
    onAvailabilityChange,
  ]);

  const handleDateTimeChange = (
    field: 'startTime' | 'endTime',
    value: string
  ) => {
    onUpdate({ [field]: value });
  };

  const handleNumberOfGuestsChange = (value: number) => {
    onUpdate({ numberOfGuests: Math.max(1, value) });
  };

  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  };

  const formatDuration = (hours: number): string => {
    const fullHours = Math.floor(hours);
    const minutes = Math.round((hours - fullHours) * 60);

    if (fullHours > 0 && minutes > 0) {
      return `${fullHours} giờ ${minutes} phút`;
    } else if (fullHours > 0) {
      return `${fullHours} giờ`;
    } else if (minutes > 0) {
      return `${minutes} phút`;
    }
    return '0 phút';
  };

  // Get current start time for filtering end times
  const currentStartTimeHM = formData.startTime
    ? formData.startTime.split('T')[1]?.slice(0, 5)
    : null;

  const validEndTimeOptions = getValidEndTimeOptions(currentStartTimeHM);

  // Handle retry for availability check
  const handleRetryAvailability = () => {
    if (formData.startTime && formData.endTime && formData.roomId) {
      availability.retryCheck(
        formData.roomId,
        formData.startTime,
        formData.endTime
      );
    }
  };

  // Handle selecting alternative time slot
  const handleSelectAlternative = (startTime: string, endTime: string) => {
    onUpdate({
      startTime,
      endTime,
    });
    // The availability check will be triggered automatically by useEffect
  };

  return (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-h3 font-semibold text-gray-900 mb-2">
          Chọn thời gian và số khách
        </h2>
        <p className="text-base text-gray-600">
          Chọn ngày, giờ bắt đầu, kết thúc và số lượng khách
        </p>
      </div>

      {/* Date and Time Selection */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Date Selection */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            Ngày và thời gian
          </h3>

          {/* Date Input */}
          <div>
            <label
              htmlFor="bookingDate"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Ngày đặt phòng <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type="date"
                id="bookingDate"
                min={getMinDate()}
                max={getMaxDate()}
                value={
                  formData.startTime ? formData.startTime.split('T')[0] : ''
                }
                onChange={e => {
                  const newDate = e.target.value;
                  if (newDate) {
                    onUpdate({
                      startTime: createISOString(newDate, '00:00'),
                      endTime: createISOString(newDate, '00:00'),
                    });
                  }
                }}
                className={`w-full pl-10 pr-3 py-3 border rounded-lg text-black focus:ring-2 focus:ring-gold-500 focus:border-gold-500 ${
                  errors.startTime ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            </div>
            {errors.startTime && (
              <p className="mt-1 text-sm text-red-600">{errors.startTime}</p>
            )}
          </div>

          {/* Time Selection */}
          <div className="grid grid-cols-2 gap-4">
            {/* Start Time */}
            <div>
              <label
                htmlFor="startTime"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Giờ bắt đầu <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <Select
                  value={currentStartTimeHM || ''}
                  onValueChange={timeHM => {
                    // timeHM is HH:MM
                    const datePart = formData.startTime
                      ? formData.startTime.split('T')[0]
                      : getMinDate();
                    const newStartTime = createISOString(datePart, timeHM);

                    let newEndTime = formData.endTime || '';
                    const newStartTimeIndex =
                      availableTimeOptions.indexOf(timeHM);

                    if (newStartTimeIndex !== -1) {
                      const typicalDurationSlots = 2;
                      if (
                        availableTimeOptions.length >
                        newStartTimeIndex + typicalDurationSlots
                      ) {
                        newEndTime = createISOString(
                          datePart,
                          availableTimeOptions[
                            newStartTimeIndex + typicalDurationSlots
                          ]
                        );
                      } else if (
                        availableTimeOptions.length >
                        newStartTimeIndex + 1
                      ) {
                        newEndTime = createISOString(
                          datePart,
                          availableTimeOptions[newStartTimeIndex + 1]
                        );
                      } else {
                        const lastAvailableTime =
                          availableTimeOptions[availableTimeOptions.length - 1];
                        newEndTime = createISOString(
                          datePart,
                          lastAvailableTime
                        );
                        if (new Date(newEndTime) <= new Date(newStartTime)) {
                          newEndTime = newStartTime;
                        }
                      }
                    } else {
                      // Reset end time to be after the new start time
                      const validEndOptions = getValidEndTimeOptions(timeHM);
                      if (validEndOptions.length > 0) {
                        newEndTime = createISOString(
                          datePart,
                          validEndOptions[0]
                        );
                      } else {
                        newEndTime = newStartTime;
                      }
                    }

                    onUpdate({ startTime: newStartTime, endTime: newEndTime });
                  }}
                >
                  <SelectTrigger
                    className={`w-full ${
                      errors.startTime ? 'border-red-500' : 'border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <Clock className="w-5 h-5 text-gray-400" />
                      <SelectValue placeholder="Chọn giờ" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    {availableTimeOptions.length === 0 ? (
                      <div className="py-2 px-4 text-sm text-gray-500">
                        {selectedLocation
                          ? 'Ngày này không hoạt động'
                          : 'Vui lòng chọn chi nhánh trước'}
                      </div>
                    ) : (
                      availableTimeOptions.map(time => (
                        <SelectItem key={`start-${time}`} value={time}>
                          {time}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              {errors.startTime && (
                <p className="mt-1 text-sm text-red-600">{errors.startTime}</p>
              )}
            </div>

            {/* End Time */}
            <div>
              <label
                htmlFor="endTime"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Giờ kết thúc <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <Select
                  value={
                    formData.endTime
                      ? formData.endTime.split('T')[1]?.slice(0, 5)
                      : ''
                  }
                  onValueChange={timeHM => {
                    // timeHM is HH:MM
                    const datePart = formData.startTime
                      ? formData.startTime.split('T')[0]
                      : getMinDate();
                    const newEndTime = createISOString(datePart, timeHM);
                    onUpdate({ endTime: newEndTime });
                  }}
                >
                  <SelectTrigger
                    className={`w-full ${
                      errors.endTime ? 'border-red-500' : 'border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <Clock className="w-5 h-5 text-gray-400" />
                      <SelectValue placeholder="Chọn giờ" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    {!currentStartTimeHM ? (
                      <div className="py-2 px-4 text-sm text-gray-500">
                        Vui lòng chọn giờ bắt đầu trước
                      </div>
                    ) : validEndTimeOptions.length === 0 ? (
                      <div className="py-2 px-4 text-sm text-gray-500">
                        Không có giờ kết thúc khả dụng
                      </div>
                    ) : (
                      validEndTimeOptions.map(time => (
                        <SelectItem key={`end-${time}`} value={time}>
                          {time}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              {errors.endTime && (
                <p className="mt-1 text-sm text-red-600">{errors.endTime}</p>
              )}
            </div>
          </div>

          {/* Number of Guests */}
          <div>
            <label
              htmlFor="numberOfGuests"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Số lượng khách <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type="number"
                id="numberOfGuests"
                min="1"
                max={selectedRoom?.capacity || 10}
                value={formData.numberOfGuests}
                onChange={e =>
                  handleNumberOfGuestsChange(parseInt(e.target.value) || 1)
                }
                className={`w-full pl-10 pr-3 py-3 border rounded-lg text-black focus:ring-2 focus:ring-gold-500 focus:border-gold-500 ${
                  errors.numberOfGuests ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            </div>
            {selectedRoom && (
              <p className="mt-1 text-sm text-gray-500">
                Sức chứa tối đa: {selectedRoom.capacity} người
              </p>
            )}
            {errors.numberOfGuests && (
              <p className="mt-1 text-sm text-red-600">
                {errors.numberOfGuests}
              </p>
            )}
          </div>
        </div>

        {/* Booking Summary */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            Tóm tắt đặt phòng
          </h3>

          <div className="bg-gray-50 rounded-lg p-4 border">
            {selectedRoom ? (
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Phòng:</span>
                  <span className="font-medium text-gray-900">
                    {selectedRoom.name?.vi || selectedRoom.name?.en || 'N/A'}
                  </span>
                </div>

                {formData.startTime && formData.endTime && (
                  <>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Thời gian:</span>
                      <span className="font-medium text-gray-900">
                        {formatDuration(duration)}
                      </span>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Số khách:</span>
                      <span className="font-medium text-gray-900">
                        {formData.numberOfGuests} người
                      </span>
                    </div>

                    <div className="border-t border-gray-200 pt-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Giá dự kiến:</span>
                        <span className="font-semibold text-lg text-gold-600">
                          {formatPrice(estimatedPrice)}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatPrice(selectedRoom.pricePerHour)}/giờ ×{' '}
                        {formatDuration(duration)}
                      </p>
                    </div>
                  </>
                )}
              </div>
            ) : (
              <p className="text-gray-500 text-center">Chưa chọn phòng</p>
            )}
          </div>

          {/* Enhanced Availability Status */}
          <AvailabilityStatus
            availability={availability}
            onRetry={handleRetryAvailability}
            onSelectAlternative={handleSelectAlternative}
          />

          {/* Booking Guidelines */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">Lưu ý</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Thời gian đặt phòng tối thiểu: 30 phút</li>
              <li>• Thời gian đặt phòng tối đa: 8 giờ</li>
              <li>• Có thể đặt phòng trước tối đa 3 tháng</li>
              <li>• Giá có thể thay đổi vào các ngày lễ, tết</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingFormDateTime;
