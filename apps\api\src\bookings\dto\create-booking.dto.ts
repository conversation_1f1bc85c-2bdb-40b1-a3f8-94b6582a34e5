import {
  IsNotEmpty,
  IsU<PERSON><PERSON>,
  IsDateString,
  IsInt,
  Min,
  IsO<PERSON>al,
  IsString,
  <PERSON>Length,
  IsEmail,
  ValidateIf,
} from 'class-validator';

export class CreateBookingDto {
  @IsNotEmpty({ message: 'Room ID is required' })
  @IsUUID('4', { message: 'Room ID must be a valid UUID' })
  roomId: string;

  // userId will be injected by the controller/service for logged-in users
  // For guest bookings, userId will be null or not present in the initial DTO from guest.
  @IsOptional()
  @IsUUID('4', { message: 'User ID must be a valid UUID if provided' })
  userId?: string;

  @IsNotEmpty({ message: 'Start time is required' })
  @IsDateString(
    {},
    { message: 'Start time must be a valid ISO 8601 date string' },
  )
  startTime: string; // Will be converted to DateTime in service

  @IsNotEmpty({ message: 'End time is required' })
  @IsDateString(
    {},
    { message: 'End time must be a valid ISO 8601 date string' },
  )
  endTime: string; // Will be converted to DateTime in service

  @IsNotEmpty({ message: 'Number of guests is required' })
  @IsInt({ message: 'Number of guests must be an integer' })
  @Min(1, { message: 'Number of guests must be at least 1' })
  numberOfGuests: number;

  // Guest details - required if userId is not present (i.e., a guest booking)
  @ValidateIf((o) => !o.userId)
  @IsNotEmpty({ message: 'Guest name is required for guest bookings' })
  @IsString()
  @MaxLength(255)
  guestName?: string;

  @ValidateIf((o) => !o.userId)
  @IsNotEmpty({ message: 'Guest email is required for guest bookings' })
  @IsEmail({}, { message: 'Guest email must be a valid email address' })
  @MaxLength(255)
  guestEmail?: string;

  @ValidateIf((o) => !o.userId)
  @IsNotEmpty({ message: 'Guest phone is required for guest bookings' })
  @IsString()
  @MaxLength(20)
  guestPhone?: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string;

  // locationId will be derived from roomId in the service
  // bookingDate will be derived from startTime in the service
  // durationMinutes will be calculated in the service
  // totalPrice and currency will be calculated/set in the service, potentially after promo codes etc.
  // status will be set by the service, defaulting to PENDING_PAYMENT or CONFIRMED based on flow
}
