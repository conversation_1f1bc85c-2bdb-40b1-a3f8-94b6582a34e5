'use client';

import { useState, useEffect } from 'react';
import {
  User,
  UserPlus,
  Search,
  Mail,
  Phone,
  X,
  CheckCircle,
  Loader2,
} from 'lucide-react';
import { useUserSearch } from '../../hooks/useUserSearch';

interface BookingFormData {
  locationId: string;
  roomId: string;
  startTime: string;
  endTime: string;
  numberOfGuests: number;
  isGuestBooking: boolean;
  userId?: string;
  guestName?: string;
  guestEmail?: string;
  guestPhone?: string;
  notes?: string;
}

interface BookingFormCustomerProps {
  formData: BookingFormData;
  errors: Partial<Record<keyof BookingFormData, string>>;
  onUpdate: (updates: Partial<BookingFormData>) => void;
}

const BookingFormCustomer: React.FC<BookingFormCustomerProps> = ({
  formData,
  errors,
  onUpdate,
}) => {
  const userSearch = useUserSearch(300);

  const handleBookingTypeChange = (isGuest: boolean) => {
    onUpdate({
      isGuestBooking: isGuest,
      userId: undefined,
      guestName: '',
      guestEmail: '',
      guestPhone: '',
    });

    if (isGuest) {
      userSearch.clearSelection();
      userSearch.clearResults();
    }
  };

  const handleGuestInfoChange = (field: string, value: string) => {
    onUpdate({ [field]: value });
  };

  const handleUserSelect = (user: any) => {
    userSearch.selectUser(user);
    onUpdate({ userId: user.id });
  };

  const handleUserSearchInput = (query: string) => {
    userSearch.setQuery(query);
  };

  const handleClearSelectedUser = () => {
    userSearch.clearSelection();
    onUpdate({ userId: undefined });
  };

  return (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-h3 font-semibold text-gray-900 mb-2">
          Thông tin khách hàng
        </h2>
        <p className="text-base text-gray-600">
          Chọn loại đặt phòng và nhập thông tin khách hàng
        </p>
      </div>

      {/* Booking Type Selection */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Loại đặt phòng <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Guest Booking */}
            <div
              className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                formData.isGuestBooking
                  ? 'border-gold-500 bg-gold-50 ring-2 ring-gold-200'
                  : 'border-gray-300 bg-white hover:border-gray-400'
              }`}
              onClick={() => handleBookingTypeChange(true)}
            >
              <div className="flex items-center gap-3">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    formData.isGuestBooking
                      ? 'bg-gold-600 text-white'
                      : 'bg-gray-100 text-gray-400'
                  }`}
                >
                  <UserPlus className="w-5 h-5" />
                </div>
                <div className="flex-1">
                  <h3
                    className={`font-medium ${
                      formData.isGuestBooking
                        ? 'text-gold-700'
                        : 'text-gray-900'
                    }`}
                  >
                    Khách lẻ (Guest)
                  </h3>
                  <p className="text-sm text-gray-600">
                    Đặt phòng cho khách hàng chưa có tài khoản
                  </p>
                </div>
                {formData.isGuestBooking && (
                  <div className="w-6 h-6 bg-gold-600 rounded-full flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                )}
              </div>
            </div>

            {/* Existing User Booking */}
            <div
              className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                !formData.isGuestBooking
                  ? 'border-gold-500 bg-gold-50 ring-2 ring-gold-200'
                  : 'border-gray-300 bg-white hover:border-gray-400'
              }`}
              onClick={() => handleBookingTypeChange(false)}
            >
              <div className="flex items-center gap-3">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    !formData.isGuestBooking
                      ? 'bg-gold-600 text-white'
                      : 'bg-gray-100 text-gray-400'
                  }`}
                >
                  <User className="w-5 h-5" />
                </div>
                <div className="flex-1">
                  <h3
                    className={`font-medium ${
                      !formData.isGuestBooking
                        ? 'text-gold-700'
                        : 'text-gray-900'
                    }`}
                  >
                    Khách hàng có tài khoản
                  </h3>
                  <p className="text-sm text-gray-600">
                    Đặt phòng cho khách hàng đã đăng ký
                  </p>
                </div>
                {!formData.isGuestBooking && (
                  <div className="w-6 h-6 bg-gold-600 rounded-full flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Guest Information Form */}
      {formData.isGuestBooking && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            Thông tin khách hàng
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Guest Name */}
            <div>
              <label
                htmlFor="guestName"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Họ và tên <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="guestName"
                value={formData.guestName || ''}
                onChange={e =>
                  handleGuestInfoChange('guestName', e.target.value)
                }
                className={`w-full px-3 py-3 border rounded-lg text-black focus:ring-2 focus:ring-gold-500 focus:border-gold-500 ${
                  errors.guestName ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Nhập họ và tên khách hàng"
              />
              {errors.guestName && (
                <p className="mt-1 text-sm text-red-600">{errors.guestName}</p>
              )}
            </div>

            {/* Guest Phone */}
            <div>
              <label
                htmlFor="guestPhone"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Số điện thoại <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  type="tel"
                  id="guestPhone"
                  value={formData.guestPhone || ''}
                  onChange={e =>
                    handleGuestInfoChange('guestPhone', e.target.value)
                  }
                  className={`w-full pl-10 pr-3 py-3 border rounded-lg text-black focus:ring-2 focus:ring-gold-500 focus:border-gold-500 ${
                    errors.guestPhone ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Nhập số điện thoại"
                />
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              </div>
              {errors.guestPhone && (
                <p className="mt-1 text-sm text-red-600">{errors.guestPhone}</p>
              )}
            </div>
          </div>

          {/* Guest Email */}
          <div>
            <label
              htmlFor="guestEmail"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Email <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type="email"
                id="guestEmail"
                value={formData.guestEmail || ''}
                onChange={e =>
                  handleGuestInfoChange('guestEmail', e.target.value)
                }
                className={`w-full pl-10 pr-3 py-3 border rounded-lg text-black focus:ring-2 focus:ring-gold-500 focus:border-gold-500 ${
                  errors.guestEmail ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Nhập địa chỉ email"
              />
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            </div>
            {errors.guestEmail && (
              <p className="mt-1 text-sm text-red-600">{errors.guestEmail}</p>
            )}
          </div>
        </div>
      )}

      {/* User Search Form (for existing users) */}
      {!formData.isGuestBooking && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            Tìm kiếm khách hàng
          </h3>

          {/* Selected User Display */}
          {userSearch.selectedUser && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div className="font-medium text-green-900">
                      Đã chọn khách hàng
                    </div>
                    <div className="text-sm text-green-700">
                      {userSearch.selectedUser.name || 'Chưa có tên'}
                    </div>
                    <div className="text-sm text-green-600">
                      {userSearch.selectedUser.email}
                    </div>
                    {userSearch.selectedUser.phoneNumber && (
                      <div className="text-sm text-green-600">
                        {userSearch.selectedUser.phoneNumber}
                      </div>
                    )}
                  </div>
                </div>
                <button
                  type="button"
                  onClick={handleClearSelectedUser}
                  className="text-green-600 hover:text-green-700 p-1"
                  aria-label="Bỏ chọn khách hàng"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>
          )}

          {/* Search Input */}
          {!userSearch.selectedUser && (
            <>
              <div>
                <label
                  htmlFor="userSearch"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Tìm kiếm theo tên, email hoặc số điện thoại{' '}
                  <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="text"
                    id="userSearch"
                    value={userSearch.query}
                    onChange={e => handleUserSearchInput(e.target.value)}
                    className={`w-full pl-10 pr-10 py-3 border rounded-lg text-black focus:ring-2 focus:ring-gold-500 focus:border-gold-500 ${
                      errors.userId ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Nhập tên, email hoặc số điện thoại để tìm kiếm..."
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  {userSearch.loading && (
                    <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 animate-spin" />
                  )}
                </div>
                {errors.userId && (
                  <p className="mt-1 text-sm text-red-600">{errors.userId}</p>
                )}
                {userSearch.query.length > 0 && userSearch.query.length < 2 && (
                  <p className="mt-1 text-sm text-gray-500">
                    Nhập ít nhất 2 ký tự để tìm kiếm
                  </p>
                )}
              </div>

              {/* Error State */}
              {userSearch.error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 text-red-800">
                    <svg
                      className="w-5 h-5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 000 2v4a1 1 0 002 0V7a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="text-sm font-medium">Lỗi tìm kiếm</span>
                  </div>
                  <p className="text-red-700 text-sm mt-1">
                    {userSearch.error}
                  </p>
                </div>
              )}

              {/* Search Results */}
              {userSearch.results.length > 0 && (
                <div className="border border-gray-300 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">
                    Kết quả tìm kiếm ({userSearch.results.length})
                  </h4>
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {userSearch.results.map(user => (
                      <div
                        key={user.id}
                        className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                        onClick={() => handleUserSelect(user)}
                      >
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">
                            {user.name || 'Chưa có tên'}
                          </div>
                          <div className="text-sm text-gray-600">
                            {user.email}
                          </div>
                          {user.phoneNumber && (
                            <div className="text-sm text-gray-600">
                              {user.phoneNumber}
                            </div>
                          )}
                          <div className="text-xs text-gray-500 mt-1">
                            {user.bookingCount || 0} đặt phòng
                          </div>
                        </div>
                        <button
                          type="button"
                          className="text-gold-600 hover:text-gold-700 text-sm font-medium px-3 py-1 rounded-md hover:bg-gold-50 transition-colors"
                        >
                          Chọn
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* No Results */}
              {userSearch.query.length >= 2 &&
                !userSearch.loading &&
                userSearch.results.length === 0 &&
                !userSearch.error && (
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center gap-2 text-gray-600">
                      <Search className="w-5 h-5" />
                      <span className="text-sm font-medium">
                        Không tìm thấy kết quả
                      </span>
                    </div>
                    <p className="text-gray-600 text-sm mt-1">
                      Không tìm thấy khách hàng nào với từ khóa "
                      {userSearch.query}". Vui lòng thử từ khóa khác hoặc tạo
                      đặt phòng khách lẻ.
                    </p>
                  </div>
                )}
            </>
          )}
        </div>
      )}

      {/* Additional Notes */}
      <div>
        <label
          htmlFor="notes"
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          Ghi chú thêm
        </label>
        <textarea
          id="notes"
          value={formData.notes || ''}
          onChange={e => handleGuestInfoChange('notes', e.target.value)}
          rows={3}
          className="w-full px-3 py-3 border border-gray-300 rounded-lg text-black focus:ring-2 focus:ring-gold-500 focus:border-gold-500"
          placeholder="Nhập ghi chú thêm cho đặt phòng (không bắt buộc)"
        />
      </div>
    </div>
  );
};

export default BookingFormCustomer;
